# KooLek Standalone Application

A standalone financial management application that replicates all the features and functionality of the KooLek module from the SIMI Digital platform.

## Features

- **Contribution Processing**: Submit and track contribution requests with file attachments
- **Expense Management**: Create, edit, and manage expense records with receipts
- **Collection Tracking**: Monitor contribution collections and status
- **Verification System**: Admin approval workflow for transactions
- **User Management**: Staff profile and role management
- **Dashboard**: Financial summaries and analytics
- **Mobile Responsive**: Optimized for both desktop and mobile devices
- **Role-Based Access**: Admin and user permission controls

## Technology Stack

- **Frontend**: Next.js 14, React 18, TypeScript
- **UI Framework**: Material-UI (MUI), Tailwind CSS
- **State Management**: Redux Toolkit
- **Forms**: Formik with Yup validation
- **Date Handling**: Day.js, Moment.js
- **HTTP Client**: Axios
- **Notifications**: Notistack

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. Clone or navigate to the project directory:
```bash
cd koolek-standalone
```

2. Install dependencies:
```bash
npm install
# or
yarn install
```

3. Start the development server:
```bash
npm run dev
# or
yarn dev
```

4. Open [http://localhost:3002](http://localhost:3002) in your browser

### Building for Production

```bash
npm run build
npm run start
```

## Project Structure

```
koolek-standalone/
├── src/
│   ├── components/          # Reusable UI components
│   ├── pages/              # Next.js pages
│   ├── store/              # Redux store and slices
│   ├── services/           # API services
│   ├── utils/              # Utility functions
│   ├── contexts/           # React contexts
│   └── styles/             # Global styles
├── public/                 # Static assets
└── ...config files
```

## Key Features

### Dashboard
- Financial summary cards
- Recent transactions table
- Year-based filtering
- Role-based data visibility

### Contribution Management
- Submit contribution requests
- File attachment support
- Status tracking (pending, verified, rejected)
- Admin verification workflow

### Expense Tracking
- Create and manage expenses
- Receipt uploads
- Categorization
- Year-based organization

### User Management
- Staff profile management
- Role assignment
- Monthly contribution tracking
- Access control

### Collection Management
- Contribution collection tracking
- Status monitoring
- Reporting capabilities

## API Integration

The application is designed to work with a backend API. Configure the API base URL in:
- `src/services/authService.js`
- `src/services/apiService.js`

## Authentication

The application includes a complete authentication system with:
- JWT token management
- Automatic token refresh
- Protected routes
- Role-based access control

## Mobile Support

The application is fully responsive with:
- Mobile-optimized layouts
- Touch-friendly interfaces
- Floating action buttons for mobile
- Responsive tables and forms

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is private and proprietary.
