'use client'

import { useState } from 'react'
import { useSession, signOut } from 'next-auth/react'
import { useRouter, usePathname } from 'next/navigation'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { OrganizationSelector } from '@/components/organization/OrganizationSelector'
import { Breadcrumbs } from './Breadcrumbs'
import {
  Menu,
  Bell,
  Search,
  Settings,
  User,
  LogOut,
  Building2,
  HelpCircle
} from 'lucide-react'
import { useOrganization } from '@/contexts/OrganizationContext'
import { Input } from '@/components/ui/input'

interface HeaderProps {
  onMenuClick: () => void
}

export function Header({ onMenuClick }: HeaderProps) {
  const { data: session } = useSession()
  const { currentOrganization } = useOrganization()
  const router = useRouter()
  const pathname = usePathname()
  const [searchQuery, setSearchQuery] = useState('')

  const handleSignOut = async () => {
    await signOut({ callbackUrl: '/signin' })
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      // Implement search functionality
      console.log('Search:', searchQuery)
    }
  }

  return (
    <header className="page-header sticky top-0 z-30 border-b border-border/50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container-app">
        <div className="flex h-16 items-center justify-between">
          {/* Left side */}
          <div className="flex items-center gap-4">
            {/* Mobile menu button */}
            <Button
              variant="ghost"
              size="icon-sm"
              className="lg:hidden hover:bg-muted/50"
              onClick={onMenuClick}
            >
              <Menu className="h-5 w-5" />
            </Button>

            {/* Organization selector - Enhanced for mobile */}
            <div className="hidden lg:block">
              <OrganizationSelector />
            </div>

            {/* Mobile organization indicator */}
            <div className="lg:hidden">
              {currentOrganization && (
                <div className="flex items-center gap-2 px-3 py-1.5 bg-primary/5 rounded-lg border border-primary/10">
                  <Building2 className="h-4 w-4 text-primary" />
                  <span className="text-sm font-medium text-primary truncate max-w-[120px]">
                    {currentOrganization.name}
                  </span>
                </div>
              )}
            </div>

            {/* Breadcrumbs */}
            <div className="hidden md:block">
              <Breadcrumbs />
            </div>
          </div>

          {/* Center - Search */}
          <div className="flex-1 max-w-md mx-4">
            <form onSubmit={handleSearch} className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search contributions, expenses..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 bg-muted/30 border-border/50 focus:bg-background focus:border-border transition-all duration-200"
              />
            </form>
          </div>

          {/* Right side */}
          <div className="flex items-center gap-1">
            {/* Notifications */}
            <Button variant="ghost" size="icon-sm" className="relative hover:bg-muted/50">
              <Bell className="h-5 w-5" />
              <span className="absolute -top-1 -right-1 h-3 w-3 bg-destructive rounded-full text-xs flex items-center justify-center text-white shadow-small">
                3
              </span>
            </Button>

            {/* Help */}
            <Button variant="ghost" size="icon-sm" className="hover:bg-muted/50">
              <HelpCircle className="h-5 w-5" />
            </Button>

            {/* User menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-9 w-9 rounded-full hover:bg-muted/50 ml-2">
                  <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center border border-primary/20 shadow-subtle">
                    <span className="text-sm font-semibold text-primary">
                      {session?.user?.name?.charAt(0).toUpperCase() || 'U'}
                    </span>
                  </div>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-64 shadow-large border-border/50" align="end" forceMount>
                <DropdownMenuLabel className="font-normal p-4">
                  <div className="flex flex-col space-y-2">
                    <p className="text-body font-semibold leading-none text-foreground">
                      {session?.user?.name}
                    </p>
                    <p className="text-caption leading-none text-muted-foreground">
                      {session?.user?.email}
                    </p>
                    {currentOrganization && (
                      <div className="flex items-center gap-2 mt-2 px-2 py-1 bg-primary/5 rounded-md border border-primary/10">
                        <Building2 className="h-3 w-3 text-primary" />
                        <p className="text-caption leading-none text-primary font-medium">
                          {currentOrganization.name}
                        </p>
                      </div>
                    )}
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator className="bg-border/50" />
                <DropdownMenuItem
                  onClick={() => router.push('/profile')}
                  className="p-3 cursor-pointer hover:bg-muted/50 transition-colors"
                >
                  <User className="mr-3 h-4 w-4" />
                  <span className="text-body">Profile</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => router.push('/organizations')}
                  className="p-3 cursor-pointer hover:bg-muted/50 transition-colors"
                >
                  <Building2 className="mr-3 h-4 w-4" />
                  <span className="text-body">Organizations</span>
                </DropdownMenuItem>
                {currentOrganization && (
                  <DropdownMenuItem
                    onClick={() => router.push(`/${currentOrganization.slug}/settings`)}
                    className="p-3 cursor-pointer hover:bg-muted/50 transition-colors"
                  >
                    <Settings className="mr-3 h-4 w-4" />
                    <span className="text-body">Settings</span>
                  </DropdownMenuItem>
                )}
                <DropdownMenuSeparator className="bg-border/50" />
                <DropdownMenuItem
                  onClick={handleSignOut}
                  className="p-3 cursor-pointer hover:bg-destructive/10 hover:text-destructive transition-colors"
                >
                  <LogOut className="mr-3 h-4 w-4" />
                  <span className="text-body">Sign out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </header>
  )
}
