'use client'

import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { ChevronRight, Home } from 'lucide-react'
import { useOrganization } from '@/contexts/OrganizationContext'
import { cn } from '@/lib/utils'

export function Breadcrumbs() {
  const pathname = usePathname()
  const { currentOrganization } = useOrganization()

  if (!pathname || !currentOrganization) return null

  // Parse the pathname to create breadcrumbs
  const segments = pathname.split('/').filter(Boolean)

  // Remove the organization slug from segments
  const orgSlugIndex = segments.findIndex(segment => segment === currentOrganization.slug)
  if (orgSlugIndex !== -1) {
    segments.splice(orgSlugIndex, 1)
  }

  // Define breadcrumb labels
  const breadcrumbLabels: Record<string, string> = {
    dashboard: 'Dashboard',
    KooLeknts: 'KooLeknts',
    expenses: 'Expenses',
    collections: 'Collections',
    members: 'Members',
    settings: 'Settings',
    reports: 'Reports',
    new: 'New',
    edit: 'Edit',
    invitations: 'Invitations',
    billing: 'Billing',
    security: 'Security',
    integrations: 'Integrations',
  }

  // Build breadcrumb items
  const breadcrumbs = [
    {
      label: currentOrganization.name,
      href: `/${currentOrganization.slug}/dashboard`,
      isHome: true,
    },
  ]

  let currentPath = `/${currentOrganization.slug}`

  segments.forEach((segment, index) => {
    currentPath += `/${segment}`
    const label = breadcrumbLabels[segment] || segment.charAt(0).toUpperCase() + segment.slice(1)
    const isLast = index === segments.length - 1

    breadcrumbs.push({
      label,
      href: isLast ? undefined : currentPath,
      isHome: false,
    })
  })

  // Don't show breadcrumbs if we're just on the dashboard
  if (breadcrumbs.length <= 1) return null

  return (
    <nav className="flex items-center space-x-1 text-sm text-muted-foreground">
      {breadcrumbs.map((breadcrumb, index) => (
        <div key={breadcrumb.label} className="flex items-center">
          {index > 0 && (
            <ChevronRight className="h-4 w-4 mx-1 text-muted-foreground/50" />
          )}

          {breadcrumb.href ? (
            <Link
              href={breadcrumb.href}
              className={cn(
                "hover:text-foreground transition-colors rounded-md px-2 py-1 hover:bg-muted/50",
                breadcrumb.isHome && "flex items-center gap-1"
              )}
            >
              {breadcrumb.isHome && <Home className="h-3 w-3" />}
              <span className={cn(
                "truncate",
                breadcrumb.isHome ? "max-w-[120px] sm:max-w-none" : "max-w-[100px] sm:max-w-none"
              )}>
                {breadcrumb.label}
              </span>
            </Link>
          ) : (
            <span className="text-foreground font-medium px-2 py-1 rounded-md bg-primary/5 border border-primary/10">
              <span className="truncate max-w-[120px] sm:max-w-none block">
                {breadcrumb.label}
              </span>
            </span>
          )}
        </div>
      ))}
    </nav>
  )
}
