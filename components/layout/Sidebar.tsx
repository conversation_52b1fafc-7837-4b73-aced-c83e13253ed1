'use client'

import { usePathname, useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { OrganizationSelector } from '@/components/organization/OrganizationSelector'
import { useOrganization } from '@/contexts/OrganizationContext'
import { useSession } from 'next-auth/react'
import { cn } from '@/lib/utils'
import {
  Building2,
  LayoutDashboard,
  CreditCard,
  Receipt,
  Users,
  Settings,
  BarChart3,
  FileText,
  Wallet,
  X,
  Plus,
  TrendingUp,
  Shield,
  Zap
} from 'lucide-react'

interface SidebarProps {
  open: boolean
  onClose: () => void
}

export function Sidebar({ open, onClose }: SidebarProps) {
  const pathname = usePathname()
  const router = useRouter()
  const { currentOrganization } = useOrganization()
  const { data: session } = useSession()

  const navigation = [
    {
      name: 'Dashboard',
      href: `/${currentOrganization?.slug}/dashboard`,
      icon: LayoutDashboard,
      current: pathname?.includes('/dashboard'),
      permissions: ['OWNER', 'ADMIN', 'MEMBER', 'VIEWER'],
      description: 'Overview and analytics',
    },
    {
      name: 'Contributions',
      href: `/${currentOrganization?.slug}/contributions`,
      icon: CreditCard,
      current: pathname?.includes('/contributions'),
      permissions: ['OWNER', 'ADMIN', 'MEMBER'],
      badge: '12',
      description: 'Manage contribution requests',
    },
    {
      name: 'Expenses',
      href: `/${currentOrganization?.slug}/expenses`,
      icon: Receipt,
      current: pathname?.includes('/expenses'),
      permissions: ['OWNER', 'ADMIN', 'MEMBER'],
      description: 'Track and manage expenses',
    },
    {
      name: 'Collections',
      href: `/${currentOrganization?.slug}/collections`,
      icon: Wallet,
      current: pathname?.includes('/collections'),
      permissions: ['OWNER', 'ADMIN', 'MEMBER'],
      description: 'Manage collection campaigns',
    },
    {
      name: 'Reports',
      href: `/${currentOrganization?.slug}/reports`,
      icon: BarChart3,
      current: pathname?.includes('/reports'),
      permissions: ['OWNER', 'ADMIN'],
    },
    {
      name: 'Members',
      href: `/${currentOrganization?.slug}/members`,
      icon: Users,
      current: pathname?.includes('/members'),
      permissions: ['OWNER', 'ADMIN'],
    },
  ]

  const secondaryNavigation = [
    {
      name: 'Settings',
      href: `/${currentOrganization?.slug}/settings`,
      icon: Settings,
      current: pathname?.includes('/settings'),
      permissions: ['OWNER', 'ADMIN'],
    },
  ]

  const quickActions = [
    {
      name: 'New Contribution',
      href: `/${currentOrganization?.slug}/contributions/new`,
      icon: Plus,
      color: 'bg-blue-500 hover:bg-blue-600',
    },
    {
      name: 'Add Expense',
      href: `/${currentOrganization?.slug}/expenses/new`,
      icon: Receipt,
      color: 'bg-green-500 hover:bg-green-600',
    },
  ]

  const userRole = currentOrganization?.role || 'VIEWER'

  const filteredNavigation = navigation.filter(item =>
    item.permissions.includes(userRole)
  )

  const filteredSecondaryNavigation = secondaryNavigation.filter(item =>
    item.permissions.includes(userRole)
  )

  if (!currentOrganization) {
    return null
  }

  return (
    <>
      {/* Mobile sidebar */}
      <div className={cn(
        "fixed inset-y-0 left-0 z-50 w-64 bg-background border-r border-border/50 shadow-large transform transition-transform duration-300 ease-in-out lg:hidden",
        open ? "translate-x-0" : "-translate-x-full"
      )}>
        <SidebarContent
          navigation={filteredNavigation}
          secondaryNavigation={filteredSecondaryNavigation}
          quickActions={quickActions}
          onClose={onClose}
          showCloseButton
        />
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:left-0 lg:z-50 lg:block lg:w-64 lg:bg-background lg:border-r lg:border-border/50">
        <SidebarContent
          navigation={filteredNavigation}
          secondaryNavigation={filteredSecondaryNavigation}
          quickActions={quickActions}
        />
      </div>
    </>
  )
}

interface SidebarContentProps {
  navigation: any[]
  secondaryNavigation: any[]
  quickActions: any[]
  onClose?: () => void
  showCloseButton?: boolean
}

function SidebarContent({
  navigation,
  secondaryNavigation,
  quickActions,
  onClose,
  showCloseButton
}: SidebarContentProps) {
  const { currentOrganization } = useOrganization()
  const { data: session } = useSession()

  return (
    <div className="flex h-full flex-col bg-background">
      {/* Header */}
      <div className="flex h-16 items-center justify-between px-6 border-b border-border/50">
        <div className="flex items-center gap-3">
          <div className="p-1.5 bg-primary/10 rounded-lg border border-primary/20">
            <Building2 className="h-5 w-5 text-primary" />
          </div>
          <span className="font-bold text-h3 text-foreground">KooLek</span>
        </div>
        {showCloseButton && (
          <Button variant="ghost" size="icon-sm" onClick={onClose} className="hover:bg-muted/50">
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Organization selector */}
      <div className="p-4 border-b border-border/30 lg:hidden">
        <OrganizationSelector />
      </div>

      {/* Quick Actions */}
      <div className="p-4 border-b border-border/30">
        <div className="space-y-3">
          <h3 className="text-caption font-semibold text-muted-foreground uppercase tracking-wider">
            Quick Actions
          </h3>
          <div className="grid grid-cols-2 gap-2">
            {quickActions.map((action) => (
              <Link key={action.name} href={action.href}>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start text-xs h-9 hover:bg-primary/5 hover:border-primary/20 hover:text-primary transition-all duration-200"
                >
                  <action.icon className="h-3.5 w-3.5 mr-2" />
                  {action.name.split(' ')[0]}
                </Button>
              </Link>
            ))}
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-8 overflow-y-auto scrollbar-thin">
        {/* Organization Context */}
        {currentOrganization && (
          <div className="bg-primary/5 rounded-lg p-4 border border-primary/10 shadow-subtle">
            <div className="flex items-center gap-2 mb-3">
              <Building2 className="h-4 w-4 text-primary" />
              <span className="text-body font-semibold text-primary">Current Organization</span>
            </div>
            <div className="space-y-2">
              <div className="font-semibold text-body text-foreground">{currentOrganization.name}</div>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-xs border-primary/20 bg-primary/5 text-primary">
                  {currentOrganization.role.toLowerCase()}
                </Badge>
                <span className="text-caption text-muted-foreground">{currentOrganization.memberCount} members</span>
              </div>
            </div>
          </div>
        )}

        {/* Primary navigation */}
        <div className="space-y-2">
          <h3 className="text-caption font-semibold text-muted-foreground uppercase tracking-wider px-3 mb-3">
            Navigation
          </h3>
          {navigation.map((item) => {
            const Icon = item.icon
            const hasPermission = item.permissions.includes(currentOrganization?.role || 'VIEWER')

            if (!hasPermission) return null

            return (
              <Link key={item.name} href={item.href}>
                <div className={cn(
                  "group relative flex items-center gap-3 rounded-lg px-3 py-2.5 text-body font-medium transition-all duration-200 hover:bg-muted/50",
                  item.current
                    ? "bg-primary text-primary-foreground shadow-small hover:bg-primary/90"
                    : "text-foreground hover:text-foreground"
                )}>
                  <Icon className={cn(
                    "h-4 w-4 transition-colors duration-200",
                    item.current ? "text-primary-foreground" : "text-muted-foreground group-hover:text-foreground"
                  )} />
                  <div className="flex-1 min-w-0">
                    <span className="block truncate">{item.name}</span>
                    {item.description && !item.current && (
                      <span className="text-caption text-muted-foreground group-hover:text-muted-foreground/80 transition-colors duration-200">
                        {item.description}
                      </span>
                    )}
                  </div>
                  {item.badge && (
                    <Badge
                      variant={item.current ? "secondary" : "outline"}
                      className={cn(
                        "text-xs font-medium",
                        item.current
                          ? "bg-primary-foreground/10 text-primary-foreground border-primary-foreground/20"
                          : "bg-muted text-muted-foreground border-border/50"
                      )}
                    >
                      {item.badge}
                    </Badge>
                  )}
                </div>
              </Link>
            )
          })}
        </div>

        {/* Secondary navigation */}
        {secondaryNavigation.length > 0 && (
          <div className="space-y-2">
            <h3 className="text-caption font-semibold text-muted-foreground uppercase tracking-wider px-3 mb-3">
              Organization
            </h3>
            {secondaryNavigation.map((item) => {
              const Icon = item.icon
              return (
                <Link key={item.name} href={item.href}>
                  <div className={cn(
                    "group relative flex items-center gap-3 rounded-lg px-3 py-2.5 text-body font-medium transition-all duration-200 hover:bg-muted/50",
                    item.current
                      ? "bg-primary text-primary-foreground shadow-small hover:bg-primary/90"
                      : "text-foreground hover:text-foreground"
                  )}>
                    <Icon className={cn(
                      "h-4 w-4 transition-colors duration-200",
                      item.current ? "text-primary-foreground" : "text-muted-foreground group-hover:text-foreground"
                    )} />
                    <span className="truncate">{item.name}</span>
                  </div>
                </Link>
              )
            })}
          </div>
        )}
      </nav>

      {/* Organization info */}
      <div className="p-4 border-t border-border/50 bg-muted/20">
        <div className="space-y-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg border border-primary/20">
              <Building2 className="h-4 w-4 text-primary" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-body font-semibold truncate text-foreground">
                {currentOrganization.name}
              </p>
              <p className="text-caption text-muted-foreground">
                {currentOrganization.role.toLowerCase()} access
              </p>
            </div>
          </div>

          {/* Upgrade prompt for non-owners */}
          {currentOrganization.role !== 'OWNER' && (
            <div className="p-3 bg-gradient-to-br from-primary/10 via-primary/5 to-transparent rounded-lg border border-primary/20 shadow-subtle">
              <div className="flex items-center gap-2 mb-2">
                <Zap className="h-4 w-4 text-primary" />
                <span className="text-caption font-semibold text-primary">Pro Features</span>
              </div>
              <p className="text-caption text-muted-foreground mb-3 leading-relaxed">
                Unlock advanced analytics and reporting capabilities
              </p>
              <Button size="sm" className="w-full text-caption h-8 shadow-small">
                Upgrade Plan
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
