'use client'

import React from 'react'
import { useOrganizationRole } from '@/contexts/OrganizationContext'
import { hasPermission, hasAnyPermission, hasAllPermissions, Permission, canAccessResource } from '@/lib/permissions'

interface PermissionGateProps {
  children: React.ReactNode
  permission?: Permission
  permissions?: Permission[]
  requireAll?: boolean
  resource?: 'contributions' | 'expenses' | 'collections' | 'categories' | 'members' | 'organization'
  action?: 'read' | 'create' | 'update' | 'delete'
  isOwner?: boolean
  fallback?: React.ReactNode
  showFallback?: boolean
}

/**
 * PermissionGate component that conditionally renders children based on user permissions
 *
 * @param permission - Single permission to check
 * @param permissions - Array of permissions to check
 * @param requireAll - If true, user must have ALL permissions. If false, user needs ANY permission
 * @param resource - Resource type for action-based permission checking
 * @param action - Action type for resource-based permission checking
 * @param isOwner - Whether the user owns the resource (for member-level permissions)
 * @param fallback - Component to render when permission is denied
 * @param showFallback - Whether to show fallback component or nothing
 */
export function PermissionGate({
  children,
  permission,
  permissions,
  requireAll = false,
  resource,
  action,
  isOwner = false,
  fallback = null,
  showFallback = false,
}: PermissionGateProps) {
  const role = useOrganizationRole()

  // If no role, deny access
  if (!role) {
    return showFallback ? <>{fallback}</> : null
  }

  let hasAccess = false

  // Check resource-based permissions
  if (resource && action) {
    hasAccess = canAccessResource(role, action, resource, isOwner)
  }
  // Check single permission
  else if (permission) {
    hasAccess = hasPermission(role, permission)
  }
  // Check multiple permissions
  else if (permissions && permissions.length > 0) {
    hasAccess = requireAll
      ? hasAllPermissions(role, permissions)
      : hasAnyPermission(role, permissions)
  }
  // If no permission criteria provided, allow access
  else {
    hasAccess = true
  }

  if (hasAccess) {
    return <>{children}</>
  }

  return showFallback ? <>{fallback}</> : null
}

// Convenience components for common permission patterns

interface AdminOnlyProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  showFallback?: boolean
}

export function AdminOnly({ children, fallback, showFallback = false }: AdminOnlyProps) {
  const role = useOrganizationRole()

  if (role === 'OWNER' || role === 'ADMIN') {
    return <>{children}</>
  }

  return showFallback ? <>{fallback}</> : null
}

interface OwnerOnlyProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  showFallback?: boolean
}

export function OwnerOnly({ children, fallback, showFallback = false }: OwnerOnlyProps) {
  const role = useOrganizationRole()

  if (role === 'OWNER') {
    return <>{children}</>
  }

  return showFallback ? <>{fallback}</> : null
}

interface MemberOrAboveProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  showFallback?: boolean
}

export function MemberOrAbove({ children, fallback, showFallback = false }: MemberOrAboveProps) {
  const role = useOrganizationRole()

  if (role === 'OWNER' || role === 'ADMIN' || role === 'MEMBER') {
    return <>{children}</>
  }

  return showFallback ? <>{fallback}</> : null
}

// Hook for permission checking in components
export function usePermissions() {
  const role = useOrganizationRole()

  return {
    role,
    hasPermission: (permission: Permission) => role ? hasPermission(role, permission) : false,
    hasAnyPermission: (permissions: Permission[]) => role ? hasAnyPermission(role, permissions) : false,
    hasAllPermissions: (permissions: Permission[]) => role ? hasAllPermissions(role, permissions) : false,
    canAccessResource: (
      action: 'read' | 'create' | 'update' | 'delete',
      resource: 'contributions' | 'expenses' | 'collections' | 'categories' | 'members' | 'organization',
      isOwner?: boolean
    ) => role ? canAccessResource(role, action, resource, isOwner) : false,
    isAdmin: role === 'OWNER' || role === 'ADMIN',
    isOwner: role === 'OWNER',
    isMemberOrAbove: role === 'OWNER' || role === 'ADMIN' || role === 'MEMBER',
  }
}
