'use client'

import { useState } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { Loader2, Save, X } from 'lucide-react'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useToast } from '@/hooks/use-toast'
import { useOrganization } from '@/contexts/OrganizationContext'
import { api } from '@/components/providers/TRPCProvider'

// Validation schema
const contributionFormSchema = z.object({
  reference: z.string().min(1, 'Reference is required'),
  amount: z.number().positive('Amount must be positive'),
  category: z.string().min(1, 'Category is required'),
  description: z.string().optional(),
  year: z.number().int().min(2020).max(2030).optional(),
})

type ContributionFormValues = z.infer<typeof contributionFormSchema>

// Common contribution categories
const CONTRIBUTION_CATEGORIES = [
  'Monthly Contribution',
  'Special Assessment',
  'Event Contribution',
  'Emergency Fund',
  'Development Fund',
  'Welfare Fund',
  'Other',
]

interface ContributionFormProps {
  onSuccess?: () => void
  onCancel?: () => void
  initialData?: Partial<ContributionFormValues>
  mode?: 'create' | 'edit'
}

export function ContributionForm({
  onSuccess,
  onCancel,
  initialData,
  mode = 'create',
}: ContributionFormProps) {
  const router = useRouter()
  const { toast } = useToast()
  const { currentOrganization } = useOrganization()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const form = useForm<ContributionFormValues>({
    resolver: zodResolver(contributionFormSchema),
    defaultValues: {
      reference: initialData?.reference || '',
      amount: initialData?.amount || 0,
      category: initialData?.category || '',
      description: initialData?.description || '',
      year: initialData?.year || new Date().getFullYear(),
    },
  })

  const createMutation = api.contributions.create.useMutation({
    onSuccess: (data) => {
      toast({
        title: 'Success',
        description: 'Contribution created successfully',
      })
      onSuccess?.()
      router.push(`/${currentOrganization?.slug}/contributions`)
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create contribution',
        variant: 'destructive',
      })
    },
  })

  const onSubmit = async (data: ContributionFormValues) => {
    if (!currentOrganization) {
      toast({
        title: 'Error',
        description: 'No organization selected',
        variant: 'destructive',
      })
      return
    }

    setIsSubmitting(true)
    try {
      await createMutation.mutateAsync({
        organizationId: currentOrganization.id,
        ...data,
      })
    } catch (error) {
      // Error is handled by onError callback
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCancel = () => {
    if (onCancel) {
      onCancel()
    } else {
      router.back()
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {mode === 'create' ? 'Create New Contribution' : 'Edit Contribution'}
        </CardTitle>
        <CardDescription>
          {mode === 'create'
            ? 'Submit a new contribution request for your organization'
            : 'Update the contribution details'
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Reference Field */}
            <FormField
              control={form.control}
              name="reference"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Reference</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter reference number or description"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    A unique reference for this contribution (e.g., receipt number, transaction ID)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Amount Field */}
            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Amount</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      placeholder="0.00"
                      {...field}
                      onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                    />
                  </FormControl>
                  <FormDescription>
                    The contribution amount in your organization's currency
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Category Field */}
            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Category</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a category" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {CONTRIBUTION_CATEGORIES.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Choose the type of contribution
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Year Field */}
            <FormField
              control={form.control}
              name="year"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Year</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="2020"
                      max="2030"
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value) || new Date().getFullYear())}
                    />
                  </FormControl>
                  <FormDescription>
                    The year this contribution is for
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Description Field */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Add any additional details about this contribution..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Optional additional information about this contribution
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Form Actions */}
            <div className="flex items-center justify-end gap-3 pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={isSubmitting}
              >
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                {mode === 'create' ? 'Create Contribution' : 'Update Contribution'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
