'use client'

import React, { useState } from 'react'
import { api } from '@/components/providers/TRPCProvider'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  TestTube,
  Send,
  CheckCircle,
  XCircle,
  Clock,
  Code,
  AlertCircle,
} from 'lucide-react'

interface WebhookTesterProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  webhook: any
}

const SAMPLE_PAYLOADS = {
  'invitation.created': {
    event: 'invitation.created',
    data: {
      id: 'inv_123',
      email: '<EMAIL>',
      role: 'MEMBER',
      organizationId: 'org_123',
      inviterId: 'user_123',
      createdAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
    },
    organization: {
      id: 'org_123',
      name: 'Test Organization',
      slug: 'test-org',
    },
    inviter: {
      id: 'user_123',
      name: 'John <PERSON>e',
      email: '<EMAIL>',
    },
  },
  'member.joined': {
    event: 'member.joined',
    data: {
      id: 'user_456',
      email: '<EMAIL>',
      role: 'MEMBER',
      organizationId: 'org_123',
      joinedAt: new Date().toISOString(),
    },
    organization: {
      id: 'org_123',
      name: 'Test Organization',
      slug: 'test-org',
    },
  },
  'KooLeknt.created': {
    event: 'KooLeknt.created',
    data: {
      id: 'pay_789',
      amount: 1500.00,
      currency: 'USD',
      description: 'Monthly subscription',
      organizationId: 'org_123',
      userId: 'user_123',
      createdAt: new Date().toISOString(),
      status: 'pending',
    },
    organization: {
      id: 'org_123',
      name: 'Test Organization',
      slug: 'test-org',
    },
    user: {
      id: 'user_123',
      name: 'John Doe',
      email: '<EMAIL>',
    },
  },
}

export function WebhookTester({ open, onOpenChange, webhook }: WebhookTesterProps) {
  const [selectedEvent, setSelectedEvent] = useState<string>('')
  const [customPayload, setCustomPayload] = useState('')
  const [testResult, setTestResult] = useState<any>(null)
  const [isCustomMode, setIsCustomMode] = useState(false)

  const testMutation = api.webhooks.test.useMutation({
    onSuccess: (result) => {
      setTestResult(result)
    },
    onError: (error) => {
      setTestResult({
        success: false,
        error: error.message,
      })
    },
  })

  const handleTest = async () => {
    if (!webhook) return

    let payload
    if (isCustomMode) {
      try {
        payload = JSON.parse(customPayload)
      } catch (error) {
        setTestResult({
          success: false,
          error: 'Invalid JSON payload',
        })
        return
      }
    } else {
      payload = SAMPLE_PAYLOADS[selectedEvent as keyof typeof SAMPLE_PAYLOADS]
    }

    await testMutation.mutateAsync({
      webhookId: webhook.id,
      payload,
    })
  }

  const loadSamplePayload = (event: string) => {
    const sample = SAMPLE_PAYLOADS[event as keyof typeof SAMPLE_PAYLOADS]
    if (sample) {
      setCustomPayload(JSON.stringify(sample, null, 2))
    }
  }

  const getStatusIcon = (result: any) => {
    if (!result) return null

    if (result.success) {
      return <CheckCircle className="h-5 w-5 text-green-600" />
    } else {
      return <XCircle className="h-5 w-5 text-red-600" />
    }
  }

  const getStatusBadge = (result: any) => {
    if (!result) return null

    if (result.success) {
      return <Badge variant="default" className="bg-green-100 text-green-800">Success</Badge>
    } else {
      return <Badge variant="destructive">Failed</Badge>
    }
  }

  if (!webhook) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TestTube className="h-5 w-5" />
            Test Webhook: {webhook.url}
          </DialogTitle>
          <DialogDescription>
            Send a test payload to verify your webhook endpoint is working correctly
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Test Configuration */}
          <div className="space-y-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Test Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="sample-mode"
                      name="test-mode"
                      checked={!isCustomMode}
                      onChange={() => setIsCustomMode(false)}
                    />
                    <Label htmlFor="sample-mode">Sample Event</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="custom-mode"
                      name="test-mode"
                      checked={isCustomMode}
                      onChange={() => setIsCustomMode(true)}
                    />
                    <Label htmlFor="custom-mode">Custom Payload</Label>
                  </div>
                </div>

                {!isCustomMode && (
                  <div>
                    <Label htmlFor="event-select">Select Event Type</Label>
                    <Select value={selectedEvent} onValueChange={setSelectedEvent}>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose an event to test" />
                      </SelectTrigger>
                      <SelectContent>
                        {webhook.events.map((event: string) => (
                          <SelectItem key={event} value={event}>
                            {event.replace(/\./g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <Label htmlFor="payload">Test Payload</Label>
                    {!isCustomMode && selectedEvent && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => loadSamplePayload(selectedEvent)}
                      >
                        <Code className="h-4 w-4 mr-2" />
                        Load Sample
                      </Button>
                    )}
                  </div>
                  <Textarea
                    id="payload"
                    placeholder={isCustomMode ? 'Enter custom JSON payload...' : 'Select an event to see sample payload'}
                    value={customPayload}
                    onChange={(e) => setCustomPayload(e.target.value)}
                    rows={12}
                    className="font-mono text-sm"
                    readOnly={!isCustomMode && !selectedEvent}
                  />
                </div>

                <Button
                  onClick={handleTest}
                  disabled={
                    testMutation.isLoading ||
                    (!isCustomMode && !selectedEvent) ||
                    (isCustomMode && !customPayload.trim())
                  }
                  className="w-full"
                >
                  <Send className="h-4 w-4 mr-2" />
                  {testMutation.isLoading ? 'Sending Test...' : 'Send Test Webhook'}
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Test Results */}
          <div className="space-y-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Test Results
                </CardTitle>
              </CardHeader>
              <CardContent>
                {testResult ? (
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(testResult)}
                      {getStatusBadge(testResult)}
                      {testResult.status && (
                        <Badge variant="outline">
                          HTTP {testResult.status}
                        </Badge>
                      )}
                    </div>

                    {testResult.success ? (
                      <div className="space-y-2">
                        <p className="text-sm text-green-600">
                          ✅ Webhook test successful!
                        </p>
                        {testResult.statusText && (
                          <p className="text-xs text-gray-600">
                            Response: {testResult.statusText}
                          </p>
                        )}
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <p className="text-sm text-red-600">
                          ❌ Webhook test failed
                        </p>
                        {testResult.error && (
                          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                            <p className="text-sm text-red-800">
                              <strong>Error:</strong> {testResult.error}
                            </p>
                          </div>
                        )}
                        {testResult.status && (
                          <p className="text-xs text-gray-600">
                            HTTP Status: {testResult.status} {testResult.statusText}
                          </p>
                        )}
                      </div>
                    )}

                    <div className="text-xs text-gray-500">
                      Test completed at {new Date().toLocaleTimeString()}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <TestTube className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>No test results yet</p>
                    <p className="text-xs">Configure and send a test webhook to see results</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <AlertCircle className="h-4 w-4" />
                  Webhook Info
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">URL:</span>
                  <span className="font-mono text-xs break-all">{webhook.url}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Events:</span>
                  <span>{webhook.events.length} subscribed</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Secret:</span>
                  <span>{webhook.secret ? 'Configured' : 'None'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Status:</span>
                  <Badge variant={webhook.isActive ? 'default' : 'outline'}>
                    {webhook.isActive ? 'Active' : 'Inactive'}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
