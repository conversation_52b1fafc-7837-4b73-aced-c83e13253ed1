'use client'

import React, { useState } from 'react'
import { useOrganization } from '@/contexts/OrganizationContext'
import { api } from '@/components/providers/TRPCProvider'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Switch } from '@/components/ui/switch'
import {
  Webhook,
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
  TestTube,
  Activity,
  RefreshCw,
  CheckCircle,
  XCircle,
  Clock,
  Copy,
} from 'lucide-react'
import { WebhookTester } from './WebhookTester'
import { WebhookLogs } from './WebhookLogs'

interface WebhookManagementProps {
  className?: string
}

interface WebhookFormData {
  url: string
  events: string[]
  secret: string
  isActive: boolean
}

const WEBHOOK_EVENTS = [
  { value: 'invitation.created', label: 'Invitation Created' },
  { value: 'invitation.accepted', label: 'Invitation Accepted' },
  { value: 'invitation.expired', label: 'Invitation Expired' },
  { value: 'invitation.revoked', label: 'Invitation Revoked' },
  { value: 'member.joined', label: 'Member Joined' },
  { value: 'member.left', label: 'Member Left' },
  { value: 'member.role_changed', label: 'Member Role Changed' },
  { value: 'KooLeknt.created', label: 'KooLeknt Created' },
  { value: 'KooLeknt.verified', label: 'KooLeknt Verified' },
  { value: 'KooLeknt.rejected', label: 'KooLeknt Rejected' },
  { value: 'expense.created', label: 'Expense Created' },
  { value: 'expense.approved', label: 'Expense Approved' },
  { value: 'expense.rejected', label: 'Expense Rejected' },
  { value: 'organization.updated', label: 'Organization Updated' },
]

export function WebhookManagement({ className }: WebhookManagementProps) {
  const { currentOrganization } = useOrganization()
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isTesterDialogOpen, setIsTesterDialogOpen] = useState(false)
  const [isLogsDialogOpen, setIsLogsDialogOpen] = useState(false)
  const [selectedWebhook, setSelectedWebhook] = useState<any>(null)
  const [formData, setFormData] = useState<WebhookFormData>({
    url: '',
    events: [],
    secret: '',
    isActive: true,
  })

  // Fetch webhooks
  const {
    data: webhooks,
    isLoading,
    refetch,
  } = api.webhooks.getOrganizationWebhooks.useQuery(
    { organizationId: currentOrganization?.id || '' },
    { enabled: !!currentOrganization?.id }
  )

  // Mutations
  const createMutation = api.webhooks.create.useMutation({
    onSuccess: () => {
      setIsCreateDialogOpen(false)
      resetForm()
      refetch()
    },
  })

  const updateMutation = api.webhooks.update.useMutation({
    onSuccess: () => {
      setIsEditDialogOpen(false)
      resetForm()
      refetch()
    },
  })

  const deleteMutation = api.webhooks.delete.useMutation({
    onSuccess: () => refetch(),
  })

  const testMutation = api.webhooks.test.useMutation()

  const resetForm = () => {
    setFormData({
      url: '',
      events: [],
      secret: '',
      isActive: true,
    })
    setSelectedWebhook(null)
  }

  const generateSecret = () => {
    const secret = crypto.randomUUID()
    setFormData(prev => ({ ...prev, secret }))
  }

  const handleCreate = async () => {
    if (!currentOrganization?.id) return

    await createMutation.mutateAsync({
      organizationId: currentOrganization.id,
      ...formData,
    })
  }

  const handleUpdate = async () => {
    if (!selectedWebhook) return

    await updateMutation.mutateAsync({
      webhookId: selectedWebhook.id,
      ...formData,
    })
  }

  const handleEdit = (webhook: any) => {
    setSelectedWebhook(webhook)
    setFormData({
      url: webhook.url,
      events: webhook.events,
      secret: webhook.secret || '',
      isActive: webhook.isActive,
    })
    setIsEditDialogOpen(true)
  }

  const handleTest = (webhook: any) => {
    setSelectedWebhook(webhook)
    setIsTesterDialogOpen(true)
  }

  const handleViewLogs = (webhook: any) => {
    setSelectedWebhook(webhook)
    setIsLogsDialogOpen(true)
  }

  const handleDelete = async (webhook: any) => {
    if (confirm('Are you sure you want to delete this webhook?')) {
      await deleteMutation.mutateAsync({ webhookId: webhook.id })
    }
  }

  const copySecret = (secret: string) => {
    navigator.clipboard.writeText(secret)
  }

  const getStatusBadge = (webhook: any) => {
    if (!webhook.isActive) {
      return <Badge variant="outline">Inactive</Badge>
    }

    if (webhook.failureCount > 5) {
      return <Badge variant="destructive">Failing</Badge>
    }

    if (webhook.lastTriggered) {
      return <Badge variant="default">Active</Badge>
    }

    return <Badge variant="outline">Pending</Badge>
  }

  if (!currentOrganization) return null

  return (
    <div className={className}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Webhook Management</h2>
          <p className="text-sm text-gray-600">
            Configure webhooks to receive real-time notifications about organization events
          </p>
        </div>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create Webhook
        </Button>
      </div>

      {/* Webhooks Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Webhook className="h-5 w-5" />
            Active Webhooks
          </CardTitle>
          <CardDescription>
            {webhooks?.length || 0} webhooks configured
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>URL</TableHead>
                  <TableHead>Events</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Last Triggered</TableHead>
                  <TableHead>Deliveries</TableHead>
                  <TableHead className="w-[50px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      <div className="flex items-center justify-center">
                        <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                        Loading webhooks...
                      </div>
                    </TableCell>
                  </TableRow>
                ) : webhooks?.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      <div className="text-center">
                        <Webhook className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">No webhooks</h3>
                        <p className="text-gray-600 mb-4">
                          Create your first webhook to start receiving real-time notifications.
                        </p>
                        <Button onClick={() => setIsCreateDialogOpen(true)}>
                          <Plus className="h-4 w-4 mr-2" />
                          Create Webhook
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  webhooks?.map((webhook) => (
                    <TableRow key={webhook.id}>
                      <TableCell className="font-medium max-w-xs truncate">
                        {webhook.url}
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {webhook.events.length} events
                        </Badge>
                      </TableCell>
                      <TableCell>{getStatusBadge(webhook)}</TableCell>
                      <TableCell>
                        {webhook.lastTriggered ? (
                          <span className="text-sm text-gray-600">
                            {new Date(webhook.lastTriggered).toLocaleDateString()}
                          </span>
                        ) : (
                          <span className="text-sm text-gray-400">Never</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewLogs(webhook)}
                        >
                          <Activity className="h-4 w-4 mr-1" />
                          {webhook._count?.deliveries || 0}
                        </Button>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleTest(webhook)}>
                              <TestTube className="h-4 w-4 mr-2" />
                              Test Webhook
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleViewLogs(webhook)}>
                              <Activity className="h-4 w-4 mr-2" />
                              View Logs
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleEdit(webhook)}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleDelete(webhook)}
                              className="text-red-600"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Create Webhook Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create Webhook</DialogTitle>
            <DialogDescription>
              Configure a new webhook to receive real-time notifications
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="webhook-url">Webhook URL</Label>
              <Input
                id="webhook-url"
                placeholder="https://your-app.com/webhooks"
                value={formData.url}
                onChange={(e) => setFormData(prev => ({ ...prev, url: e.target.value }))}
              />
            </div>

            <div>
              <Label>Events to Subscribe</Label>
              <div className="grid grid-cols-2 gap-2 mt-2 max-h-48 overflow-y-auto border rounded-lg p-3">
                {WEBHOOK_EVENTS.map((event) => (
                  <div key={event.value} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={event.value}
                      checked={formData.events.includes(event.value)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setFormData(prev => ({
                            ...prev,
                            events: [...prev.events, event.value]
                          }))
                        } else {
                          setFormData(prev => ({
                            ...prev,
                            events: prev.events.filter(ev => ev !== event.value)
                          }))
                        }
                      }}
                      className="rounded"
                    />
                    <Label htmlFor={event.value} className="text-sm">
                      {event.label}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between">
                <Label htmlFor="webhook-secret">Webhook Secret</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={generateSecret}
                >
                  Generate
                </Button>
              </div>
              <div className="flex gap-2">
                <Input
                  id="webhook-secret"
                  placeholder="Optional secret for verification"
                  value={formData.secret}
                  onChange={(e) => setFormData(prev => ({ ...prev, secret: e.target.value }))}
                />
                {formData.secret && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => copySecret(formData.secret)}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="webhook-active"
                checked={formData.isActive}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
              />
              <Label htmlFor="webhook-active">Active</Label>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleCreate}
              disabled={!formData.url || formData.events.length === 0 || createMutation.isLoading}
            >
              {createMutation.isLoading ? 'Creating...' : 'Create Webhook'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Webhook Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Webhook</DialogTitle>
            <DialogDescription>
              Update webhook configuration
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-webhook-url">Webhook URL</Label>
              <Input
                id="edit-webhook-url"
                placeholder="https://your-app.com/webhooks"
                value={formData.url}
                onChange={(e) => setFormData(prev => ({ ...prev, url: e.target.value }))}
              />
            </div>

            <div>
              <Label>Events to Subscribe</Label>
              <div className="grid grid-cols-2 gap-2 mt-2 max-h-48 overflow-y-auto border rounded-lg p-3">
                {WEBHOOK_EVENTS.map((event) => (
                  <div key={event.value} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={`edit-${event.value}`}
                      checked={formData.events.includes(event.value)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setFormData(prev => ({
                            ...prev,
                            events: [...prev.events, event.value]
                          }))
                        } else {
                          setFormData(prev => ({
                            ...prev,
                            events: prev.events.filter(ev => ev !== event.value)
                          }))
                        }
                      }}
                      className="rounded"
                    />
                    <Label htmlFor={`edit-${event.value}`} className="text-sm">
                      {event.label}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between">
                <Label htmlFor="edit-webhook-secret">Webhook Secret</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={generateSecret}
                >
                  Generate New
                </Button>
              </div>
              <div className="flex gap-2">
                <Input
                  id="edit-webhook-secret"
                  placeholder="Optional secret for verification"
                  value={formData.secret}
                  onChange={(e) => setFormData(prev => ({ ...prev, secret: e.target.value }))}
                />
                {formData.secret && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => copySecret(formData.secret)}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="edit-webhook-active"
                checked={formData.isActive}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
              />
              <Label htmlFor="edit-webhook-active">Active</Label>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleUpdate}
              disabled={!formData.url || formData.events.length === 0 || updateMutation.isLoading}
            >
              {updateMutation.isLoading ? 'Updating...' : 'Update Webhook'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Webhook Tester Dialog */}
      <WebhookTester
        open={isTesterDialogOpen}
        onOpenChange={setIsTesterDialogOpen}
        webhook={selectedWebhook}
      />

      {/* Webhook Logs Dialog */}
      <WebhookLogs
        open={isLogsDialogOpen}
        onOpenChange={setIsLogsDialogOpen}
        webhook={selectedWebhook}
      />
    </div>
  )
}
