'use client'

import React, { useState } from 'react'
import { api } from '@/components/providers/TRPCProvider'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Activity,
  CheckCircle,
  XCircle,
  Clock,
  RefreshCw,
  Eye,
  Code,
  Calendar,
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface WebhookLogsProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  webhook: any
}

interface DeliveryDetailsProps {
  delivery: any
  open: boolean
  onOpenChange: (open: boolean) => void
}

function DeliveryDetails({ delivery, open, onOpenChange }: DeliveryDetailsProps) {
  if (!delivery) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Code className="h-5 w-5" />
            Delivery Details
          </DialogTitle>
          <DialogDescription>
            Event: {delivery.event} • {new Date(delivery.createdAt).toLocaleString()}
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Request Details */}
          <div className="space-y-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Request</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <h4 className="text-sm font-medium mb-2">Payload</h4>
                    <div className="bg-gray-50 rounded-lg p-3 max-h-64 overflow-y-auto">
                      <pre className="text-xs font-mono">
                        {JSON.stringify(delivery.payload, null, 2)}
                      </pre>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-2">Headers</h4>
                    <div className="bg-gray-50 rounded-lg p-3">
                      <div className="space-y-1 text-xs">
                        <div><strong>Content-Type:</strong> application/json</div>
                        <div><strong>User-Agent:</strong> KooLek-Webhooks/1.0</div>
                        {delivery.webhook?.secret && (
                          <div><strong>X-Webhook-Secret:</strong> [REDACTED]</div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Response Details */}
          <div className="space-y-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Response</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    {delivery.status === 'success' ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-600" />
                    )}
                    <Badge variant={delivery.status === 'success' ? 'default' : 'destructive'}>
                      {delivery.status.toUpperCase()}
                    </Badge>
                    {delivery.response?.status && (
                      <Badge variant="outline">
                        HTTP {delivery.response.status}
                      </Badge>
                    )}
                  </div>

                  {delivery.response && (
                    <>
                      <div>
                        <h4 className="text-sm font-medium mb-2">Status</h4>
                        <div className="bg-gray-50 rounded-lg p-3">
                          <div className="text-sm">
                            {delivery.response.status} {delivery.response.statusText}
                          </div>
                        </div>
                      </div>

                      {delivery.response.headers && (
                        <div>
                          <h4 className="text-sm font-medium mb-2">Response Headers</h4>
                          <div className="bg-gray-50 rounded-lg p-3 max-h-32 overflow-y-auto">
                            <pre className="text-xs">
                              {JSON.stringify(delivery.response.headers, null, 2)}
                            </pre>
                          </div>
                        </div>
                      )}
                    </>
                  )}

                  <div>
                    <h4 className="text-sm font-medium mb-2">Timing</h4>
                    <div className="bg-gray-50 rounded-lg p-3 text-sm space-y-1">
                      <div><strong>Sent:</strong> {new Date(delivery.createdAt).toLocaleString()}</div>
                      <div><strong>Duration:</strong> {delivery.duration || 'N/A'}ms</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export function WebhookLogs({ open, onOpenChange, webhook }: WebhookLogsProps) {
  const [selectedDelivery, setSelectedDelivery] = useState<any>(null)
  const [isDetailsOpen, setIsDetailsOpen] = useState(false)

  // Fetch webhook deliveries
  const {
    data: deliveriesData,
    isLoading,
    refetch,
  } = api.webhooks.getDeliveries.useQuery(
    {
      webhookId: webhook?.id || '',
      limit: 50,
      offset: 0,
    },
    { enabled: !!webhook?.id && open }
  )

  const handleViewDetails = (delivery: any) => {
    setSelectedDelivery(delivery)
    setIsDetailsOpen(true)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return <Badge variant="default" className="bg-green-100 text-green-800">Success</Badge>
      case 'failed':
        return <Badge variant="destructive">Failed</Badge>
      default:
        return <Badge variant="outline">Pending</Badge>
    }
  }

  if (!webhook) return null

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Webhook Delivery Logs
            </DialogTitle>
            <DialogDescription>
              Delivery history for {webhook.url}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Summary Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Total Deliveries</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{deliveriesData?.total || 0}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">
                    {deliveriesData?.deliveries ?
                      Math.round((deliveriesData.deliveries.filter(d => d.status === 'success').length / deliveriesData.deliveries.length) * 100) : 0
                    }%
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Last Triggered</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-sm">
                    {webhook.lastTriggered ?
                      formatDistanceToNow(new Date(webhook.lastTriggered), { addSuffix: true }) :
                      'Never'
                    }
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Failure Count</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-red-600">{webhook.failureCount || 0}</div>
                </CardContent>
              </Card>
            </div>

            {/* Delivery History */}
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm">Recent Deliveries</CardTitle>
                  <Button variant="outline" size="sm" onClick={() => refetch()}>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Refresh
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="border rounded-lg">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Event</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Response</TableHead>
                        <TableHead>Timestamp</TableHead>
                        <TableHead className="w-[50px]"></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {isLoading ? (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center py-8">
                            <div className="flex items-center justify-center">
                              <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                              Loading delivery logs...
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : deliveriesData?.deliveries.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center py-8">
                            <div className="text-center">
                              <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                              <h3 className="text-lg font-medium text-gray-900 mb-2">No deliveries</h3>
                              <p className="text-gray-600">
                                No webhook deliveries have been made yet.
                              </p>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : (
                        deliveriesData?.deliveries.map((delivery) => (
                          <TableRow key={delivery.id}>
                            <TableCell className="font-medium">
                              <div className="flex items-center gap-2">
                                {getStatusIcon(delivery.status)}
                                {delivery.event}
                              </div>
                            </TableCell>
                            <TableCell>{getStatusBadge(delivery.status)}</TableCell>
                            <TableCell>
                              {delivery.response && typeof delivery.response === 'object' && 'status' in delivery.response ? (
                                <Badge variant="outline">
                                  {(delivery.response as any).status} {(delivery.response as any).statusText}
                                </Badge>
                              ) : (
                                <span className="text-gray-400">No response</span>
                              )}
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-1 text-sm text-gray-600">
                                <Calendar className="h-3 w-3" />
                                {formatDistanceToNow(new Date(delivery.createdAt), { addSuffix: true })}
                              </div>
                            </TableCell>
                            <TableCell>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleViewDetails(delivery)}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delivery Details Dialog */}
      <DeliveryDetails
        delivery={selectedDelivery}
        open={isDetailsOpen}
        onOpenChange={setIsDetailsOpen}
      />
    </>
  )
}
