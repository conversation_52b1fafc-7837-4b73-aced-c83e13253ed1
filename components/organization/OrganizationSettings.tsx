'use client'

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useOrganization } from '@/contexts/OrganizationContext'
import { api } from '@/components/providers/TRPCProvider'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useToast } from '@/hooks/use-toast'
import {
  Settings,
  Users,
  Mail,
  Webhook,
  BarChart3,
  Shield,
  Bell,
  Palette,
  Trash2,
  AlertTriangle,
} from 'lucide-react'
import { InvitationDashboard } from '@/components/invitations/InvitationDashboard'
import { MemberManagement } from '@/components/organization/MemberManagement'
import { WebhookManagement } from '@/components/webhooks/WebhookManagement'

interface OrganizationSettingsProps {
  className?: string
}

export function OrganizationSettings({ className }: OrganizationSettingsProps) {
  const router = useRouter()
  const { toast } = useToast()
  const { currentOrganization, refreshOrganizations } = useOrganization()
  const [activeTab, setActiveTab] = useState('members')
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [deleteConfirmation, setDeleteConfirmation] = useState('')

  // Delete organization mutation
  const deleteOrganizationMutation = api.organization.delete.useMutation({
    onSuccess: () => {
      toast({
        title: 'Organization deleted',
        description: 'The organization has been permanently deleted.',
      })
      setIsDeleteDialogOpen(false)
      setDeleteConfirmation('')
      refreshOrganizations()
      router.push('/organizations')
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete organization',
        variant: 'destructive',
      })
    },
  })

  const handleDeleteOrganization = async () => {
    if (!currentOrganization) return

    if (deleteConfirmation !== currentOrganization.name) {
      toast({
        title: 'Confirmation failed',
        description: 'Please type the organization name exactly as shown.',
        variant: 'destructive',
      })
      return
    }

    await deleteOrganizationMutation.mutateAsync({
      id: currentOrganization.id,
    })
  }

  const openDeleteDialog = () => {
    setDeleteConfirmation('')
    setIsDeleteDialogOpen(true)
  }

  if (!currentOrganization) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-muted-foreground">Please select an organization</p>
      </div>
    )
  }

  return (
    <div className={className}>
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-gray-900 mb-2">Organization Settings</h1>
        <p className="text-gray-600">
          Manage settings and preferences for {currentOrganization.name}
        </p>
      </div>

      {/* Settings Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="members" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Members
          </TabsTrigger>
          <TabsTrigger value="invitations" className="flex items-center gap-2">
            <Mail className="h-4 w-4" />
            Invitations
          </TabsTrigger>
          <TabsTrigger value="webhooks" className="flex items-center gap-2">
            <Webhook className="h-4 w-4" />
            Webhooks
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Analytics
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Security
          </TabsTrigger>
          <TabsTrigger value="general" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            General
          </TabsTrigger>
        </TabsList>

        {/* Members Tab */}
        <TabsContent value="members" className="space-y-6">
          <MemberManagement />
        </TabsContent>

        {/* Invitations Tab */}
        <TabsContent value="invitations" className="space-y-6">
          <InvitationDashboard />
        </TabsContent>

        {/* Webhooks Tab */}
        <TabsContent value="webhooks" className="space-y-6">
          <WebhookManagement />
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Members</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{currentOrganization.memberCount}</div>
                <p className="text-xs text-muted-foreground">Active team members</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total KooLeknts</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{currentOrganization.KooLekntCount}</div>
                <p className="text-xs text-muted-foreground">Recorded KooLeknts</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{currentOrganization.expenseCount}</div>
                <p className="text-xs text-muted-foreground">Tracked expenses</p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Organization Analytics
              </CardTitle>
              <CardDescription>
                Detailed insights into your organization's activity and performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Advanced Analytics</h3>
                <p className="text-gray-600 mb-4">
                  Get detailed insights into member activity, financial trends, and organization growth.
                </p>
                <Button>
                  <BarChart3 className="h-4 w-4 mr-2" />
                  View Analytics
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Tab */}
        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Security Settings
              </CardTitle>
              <CardDescription>
                Configure security policies and access controls for your organization
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">Two-Factor Authentication</h4>
                  <p className="text-sm text-gray-600">Require 2FA for all organization members</p>
                </div>
                <Badge variant="outline">Optional</Badge>
              </div>

              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">Domain Restriction</h4>
                  <p className="text-sm text-gray-600">Only allow members from specific email domains</p>
                </div>
                <Badge variant="outline">Disabled</Badge>
              </div>

              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">Session Timeout</h4>
                  <p className="text-sm text-gray-600">Automatically log out inactive users</p>
                </div>
                <Badge variant="outline">30 days</Badge>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* General Tab */}
        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Organization Information
              </CardTitle>
              <CardDescription>
                Basic information about your organization
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Organization Name</label>
                    <p className="text-sm text-muted-foreground mt-1">{currentOrganization.name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Slug</label>
                    <p className="text-sm text-muted-foreground mt-1 font-mono bg-muted px-2 py-1 rounded">
                      {currentOrganization.slug}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Organization Type</label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {currentOrganization.type || 'Business'}
                    </p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Created</label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {currentOrganization.createdAt ?
                        new Date(currentOrganization.createdAt).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        }) :
                        'Unknown'
                      }
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Total Members</label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {currentOrganization.memberCount || 0} active members
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Your Role</label>
                    <Badge variant="outline" className="mt-1">
                      {currentOrganization.role}
                    </Badge>
                  </div>
                </div>
              </div>

              {currentOrganization.domain && (
                <div className="pt-4 border-t">
                  <label className="text-sm font-medium">Domain</label>
                  <p className="text-sm text-muted-foreground mt-1">{currentOrganization.domain}</p>
                </div>
              )}

              <div className="flex justify-end pt-4 border-t">
                <Button variant="outline">
                  <Settings className="h-4 w-4 mr-2" />
                  Edit Organization
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Notification Preferences
              </CardTitle>
              <CardDescription>
                Configure how you receive notifications about organization activity
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">Email Notifications</h4>
                  <p className="text-sm text-gray-600">Receive email updates about important events</p>
                </div>
                <Badge variant="default">Enabled</Badge>
              </div>

              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">Weekly Reports</h4>
                  <p className="text-sm text-gray-600">Get weekly summaries of organization activity</p>
                </div>
                <Badge variant="outline">Disabled</Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="h-5 w-5" />
                Branding & Appearance
              </CardTitle>
              <CardDescription>
                Customize the look and feel of your organization
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Palette className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Custom Branding</h3>
                <p className="text-gray-600 mb-4">
                  Upload your logo and customize colors to match your brand.
                </p>
                <Button>
                  <Palette className="h-4 w-4 mr-2" />
                  Customize Branding
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Delete Organization - Only show for owners */}
          {currentOrganization.role === 'OWNER' && (
            <Card className="border-red-200">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-red-600">
                  <Trash2 className="h-5 w-5" />
                  Danger Zone
                </CardTitle>
                <CardDescription>
                  Permanently delete this organization and all its data
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-start gap-3">
                    <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-red-800">Delete Organization</h4>
                      <p className="text-sm text-red-700 mt-1">
                        This action cannot be undone. This will permanently delete the organization,
                        all members, KooLeknts, expenses, and associated data.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button
                    variant="destructive"
                    onClick={openDeleteDialog}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Organization
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      {/* Delete Organization Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              Delete Organization
            </DialogTitle>
            <DialogDescription>
              This action cannot be undone. This will permanently delete the organization
              <span className="font-semibold"> "{currentOrganization?.name}"</span> and
              all of its data including members, KooLeknts, expenses, and settings.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-800">
                <strong>Warning:</strong> All data will be permanently lost and cannot be recovered.
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="delete-confirmation">
                Type <span className="font-mono font-semibold">{currentOrganization?.name}</span> to confirm:
              </Label>
              <Input
                id="delete-confirmation"
                value={deleteConfirmation}
                onChange={(e) => setDeleteConfirmation(e.target.value)}
                placeholder={currentOrganization?.name}
                className="font-mono"
              />
            </div>
          </div>

          <DialogFooter className="gap-2">
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={deleteOrganizationMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteOrganization}
              disabled={
                deleteConfirmation !== currentOrganization?.name ||
                deleteOrganizationMutation.isPending
              }
              className="bg-red-600 hover:bg-red-700"
            >
              {deleteOrganizationMutation.isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Organization
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
