'use client'

import React, { useState } from 'react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Building2, Plus, Users, CreditCard, Receipt } from 'lucide-react'
import { useOrganization } from '@/contexts/OrganizationContext'
import { cn } from '@/lib/utils'

interface OrganizationSelectorProps {
  className?: string
}

export function OrganizationSelector({ className }: OrganizationSelectorProps) {
  const {
    currentOrganization,
    organizations,
    isLoading,
    switchOrganization,
    createOrganization,
  } = useOrganization()

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isCreating, setIsCreating] = useState(false)
  const [createForm, setCreateForm] = useState({
    name: '',
    slug: '',
    domain: '',
  })
  const [createError, setCreateError] = useState('')

  const handleOrganizationChange = async (organizationId: string) => {
    if (organizationId === 'create-new') {
      setIsCreateDialogOpen(true)
      return
    }

    try {
      await switchOrganization(organizationId)
    } catch (error) {
      console.error('Failed to switch organization:', error)
    }
  }

  const handleCreateOrganization = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsCreating(true)
    setCreateError('')

    try {
      await createOrganization({
        name: createForm.name,
        slug: createForm.slug,
        domain: createForm.domain || undefined,
      })

      setIsCreateDialogOpen(false)
      setCreateForm({ name: '', slug: '', domain: '' })
    } catch (error: any) {
      setCreateError(error.message || 'Failed to create organization')
    } finally {
      setIsCreating(false)
    }
  }

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  const handleNameChange = (name: string) => {
    setCreateForm(prev => ({
      ...prev,
      name,
      slug: prev.slug || generateSlug(name),
    }))
  }

  if (isLoading) {
    return (
      <div className={cn("flex items-center space-x-3", className)}>
        <div className="h-8 w-8 animate-pulse rounded-lg bg-muted" />
        <div className="h-4 w-32 animate-pulse rounded bg-muted" />
      </div>
    )
  }

  return (
    <div className={cn("flex items-center space-x-3", className)}>
      <div className="p-1.5 bg-primary/10 rounded-lg border border-primary/20">
        <Building2 className="h-4 w-4 text-primary" />
      </div>

      <Select
        value={currentOrganization?.id || ''}
        onValueChange={handleOrganizationChange}
      >
        <SelectTrigger className="w-[280px] border-border/50 hover:border-border focus:border-primary transition-all duration-200 shadow-subtle">
          <SelectValue placeholder="Select organization">
            {currentOrganization && (
              <div className="flex items-center space-x-2">
                <span className="font-semibold text-foreground">{currentOrganization.name}</span>
                <Badge variant="outline" className="text-xs border-primary/20 bg-primary/5 text-primary">
                  {currentOrganization.role.toLowerCase()}
                </Badge>
              </div>
            )}
          </SelectValue>
        </SelectTrigger>
        <SelectContent className="shadow-large border-border/50">
          {organizations.map((org) => (
            <SelectItem key={org.id} value={org.id} className="p-3 cursor-pointer hover:bg-muted/50 transition-colors">
              <div className="flex items-center justify-between w-full">
                <div className="flex flex-col space-y-2">
                  <div className="flex items-center space-x-2">
                    <span className="font-semibold text-foreground">{org.name}</span>
                    <Badge variant="outline" className="text-xs border-primary/20 bg-primary/5 text-primary">
                      {org.role.toLowerCase()}
                    </Badge>
                  </div>
                  <div className="flex items-center space-x-4 text-caption text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <Users className="h-3 w-3" />
                      <span>{org.memberCount} members</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <CreditCard className="h-3 w-3" />
                      <span>{org.KooLekntCount} contributions</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Receipt className="h-3 w-3" />
                      <span>{org.expenseCount} expenses</span>
                    </div>
                  </div>
                </div>
              </div>
            </SelectItem>
          ))}
          <SelectItem value="create-new" className="p-3 cursor-pointer hover:bg-primary/5 transition-colors border-t border-border/30">
            <div className="flex items-center space-x-3 text-primary">
              <div className="p-1 bg-primary/10 rounded border border-primary/20">
                <Plus className="h-3 w-3" />
              </div>
              <span className="font-semibold">Create new organization</span>
            </div>
          </SelectItem>
        </SelectContent>
      </Select>

      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <form onSubmit={handleCreateOrganization}>
            <DialogHeader>
              <DialogTitle>Create New Organization</DialogTitle>
              <DialogDescription>
                Create a new organization to manage your team's KooLeknts and expenses.
              </DialogDescription>
            </DialogHeader>

            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Organization Name</Label>
                <Input
                  id="name"
                  value={createForm.name}
                  onChange={(e) => handleNameChange(e.target.value)}
                  placeholder="Acme Inc."
                  required
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="slug">URL Slug</Label>
                <Input
                  id="slug"
                  value={createForm.slug}
                  onChange={(e) => setCreateForm(prev => ({ ...prev, slug: e.target.value }))}
                  placeholder="acme-inc"
                  pattern="^[a-z0-9-]+$"
                  title="Only lowercase letters, numbers, and hyphens allowed"
                  required
                />
                <p className="text-xs text-gray-500">
                  This will be used in your organization's URL: /org/{createForm.slug}
                </p>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="domain">Domain (Optional)</Label>
                <Input
                  id="domain"
                  value={createForm.domain}
                  onChange={(e) => setCreateForm(prev => ({ ...prev, domain: e.target.value }))}
                  placeholder="acme.com"
                  type="url"
                />
                <p className="text-xs text-gray-500">
                  Users with this domain will be automatically invited
                </p>
              </div>

              {createError && (
                <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
                  {createError}
                </div>
              )}
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsCreateDialogOpen(false)}
                disabled={isCreating}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isCreating}>
                {isCreating ? 'Creating...' : 'Create Organization'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  )
}
