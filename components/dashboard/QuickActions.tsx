'use client'

import { But<PERSON> } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Plus,
  CreditCard,
  Receipt,
  Users,
  FileText,
  Wallet,
  ChevronDown
} from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useOrganization } from '@/contexts/OrganizationContext'

export function QuickActions() {
  const router = useRouter()
  const { currentOrganization } = useOrganization()

  if (!currentOrganization) return null

  const actions = [
    {
      label: 'New Contribution',
      icon: CreditCard,
      href: `/${currentOrganization.slug}/contributions/new`,
      description: 'Create a new contribution request',
      permissions: ['OWNER', 'ADMIN', 'MEMBER'],
    },
    {
      label: 'Add Expense',
      icon: Receipt,
      href: `/${currentOrganization.slug}/expenses/new`,
      description: 'Record a new expense',
      permissions: ['OWNER', 'ADMIN', 'MEMBER'],
    },
    {
      label: 'New Collection',
      icon: Wallet,
      href: `/${currentOrganization.slug}/collections/new`,
      description: 'Start a new collection',
      permissions: ['OWNER', 'ADMIN', 'MEMBER'],
    },
    {
      label: 'Invite Member',
      icon: Users,
      href: `/${currentOrganization.slug}/members/invitations`,
      description: 'Invite team members',
      permissions: ['OWNER', 'ADMIN'],
    },
    {
      label: 'Generate Report',
      icon: FileText,
      href: `/${currentOrganization.slug}/reports/new`,
      description: 'Create financial report',
      permissions: ['OWNER', 'ADMIN'],
    },
  ]

  const userRole = currentOrganization.role
  const filteredActions = actions.filter(action =>
    action.permissions.includes(userRole)
  )

  const primaryActions = filteredActions.slice(0, 2)
  const secondaryActions = filteredActions.slice(2)

  return (
    <div className="flex items-center gap-3">
      {/* Primary quick actions - Enhanced for mobile */}
      {primaryActions.map((action) => {
        const Icon = action.icon
        return (
          <Button
            key={action.label}
            onClick={() => router.push(action.href)}
            className="hidden sm:flex shadow-sm border-gray-200 hover:bg-gray-50 hover:border-gray-300"
            size="sm"
            variant="outline"
          >
            <Icon className="h-4 w-4 mr-2" />
            <span className="hidden md:inline">{action.label}</span>
            <span className="md:hidden">{action.label.split(' ')[0]}</span>
          </Button>
        )
      })}

      {/* More actions dropdown - Enhanced */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button className="shadow-sm" size="sm">
            <Plus className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">More</span>
            <span className="sm:hidden">Actions</span>
            <ChevronDown className="h-4 w-4 ml-2" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-64 sm:w-56">
          <DropdownMenuLabel className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Quick Actions
          </DropdownMenuLabel>
          <DropdownMenuSeparator />

          {/* Show primary actions on mobile */}
          <div className="sm:hidden">
            {primaryActions.map((action) => {
              const Icon = action.icon
              return (
                <DropdownMenuItem
                  key={action.label}
                  onClick={() => router.push(action.href)}
                  className="touch-friendly"
                >
                  <Icon className="mr-3 h-4 w-4" />
                  <div className="flex-1">
                    <div className="font-medium">{action.label}</div>
                    <div className="text-xs text-muted-foreground">
                      {action.description}
                    </div>
                  </div>
                </DropdownMenuItem>
              )
            })}
            {primaryActions.length > 0 && <DropdownMenuSeparator />}
          </div>

          {/* Secondary actions */}
          {secondaryActions.map((action) => {
            const Icon = action.icon
            return (
              <DropdownMenuItem
                key={action.label}
                onClick={() => router.push(action.href)}
                className="touch-friendly"
              >
                <Icon className="mr-3 h-4 w-4" />
                <div className="flex-1">
                  <div className="font-medium">{action.label}</div>
                  <div className="text-xs text-muted-foreground">
                    {action.description}
                  </div>
                </div>
              </DropdownMenuItem>
            )
          })}

          {/* Organization context in dropdown */}
          <DropdownMenuSeparator />
          <div className="px-2 py-1.5">
            <div className="text-xs text-muted-foreground">
              Actions for {currentOrganization.name}
            </div>
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}
