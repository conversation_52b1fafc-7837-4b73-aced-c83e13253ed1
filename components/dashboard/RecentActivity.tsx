'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Activity,
  User,
  UserPlus,
  Settings,
  CreditCard,
  Receipt,
  Clock,
  ExternalLink
} from 'lucide-react'
import { useOrganization } from '@/contexts/OrganizationContext'
import Link from 'next/link'

interface ActivityItem {
  id: string
  type: 'user_joined' | 'contribution_created' | 'expense_submitted' | 'settings_updated' | 'member_invited'
  user: string
  action: string
  target?: string
  timestamp: string
  metadata?: Record<string, any>
}

export function RecentActivity() {
  const { currentOrganization } = useOrganization()

  // Mock data - in real app, this would come from API
  const activities: ActivityItem[] = [
    {
      id: '1',
      type: 'user_joined',
      user: '<PERSON>',
      action: 'joined the organization',
      timestamp: '2 minutes ago',
    },
    {
      id: '2',
      type: 'contribution_created',
      user: '<PERSON>',
      action: 'created a contribution request',
      target: 'Client Invoice #1234',
      timestamp: '15 minutes ago',
      metadata: { amount: '$2,500.00' },
    },
    {
      id: '3',
      type: 'expense_submitted',
      user: '<PERSON>',
      action: 'submitted an expense',
      target: 'Office Supplies',
      timestamp: '1 hour ago',
      metadata: { amount: '$156.78' },
    },
    {
      id: '4',
      type: 'member_invited',
      user: 'John Smith',
      action: 'invited a new member',
      target: '<EMAIL>',
      timestamp: '2 hours ago',
    },
    {
      id: '5',
      type: 'settings_updated',
      user: 'Admin',
      action: 'updated organization settings',
      target: 'Contribution Methods',
      timestamp: '3 hours ago',
    },
    {
      id: '6',
      type: 'contribution_created',
      user: 'Lisa Wang',
      action: 'created a contribution request',
      target: 'Vendor Contribution',
      timestamp: '4 hours ago',
      metadata: { amount: '$890.00' },
    },
  ]

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'user_joined':
        return <UserPlus className="h-4 w-4 text-green-600" />
      case 'contribution_created':
        return <CreditCard className="h-4 w-4 text-blue-600" />
      case 'expense_submitted':
        return <Receipt className="h-4 w-4 text-orange-600" />
      case 'settings_updated':
        return <Settings className="h-4 w-4 text-purple-600" />
      case 'member_invited':
        return <User className="h-4 w-4 text-indigo-600" />
      default:
        return <Activity className="h-4 w-4 text-gray-600" />
    }
  }

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'user_joined':
        return 'bg-green-50 border-green-200'
      case 'contribution_created':
        return 'bg-blue-50 border-blue-200'
      case 'expense_submitted':
        return 'bg-orange-50 border-orange-200'
      case 'settings_updated':
        return 'bg-purple-50 border-purple-200'
      case 'member_invited':
        return 'bg-indigo-50 border-indigo-200'
      default:
        return 'bg-gray-50 border-gray-200'
    }
  }

  return (
    <Card className="h-fit bg-white border-0 shadow-sm">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2 text-lg font-semibold text-gray-900">
              <Activity className="h-5 w-5 text-primary" />
              Recent Activity
            </CardTitle>
            <CardDescription className="text-sm text-gray-600">
              Latest actions in {currentOrganization?.name}
            </CardDescription>
          </div>
          <Link href={`/${currentOrganization?.slug}/activity`}>
            <Button variant="ghost" size="sm" className="text-gray-500 hover:text-gray-700">
              <ExternalLink className="h-4 w-4" />
            </Button>
          </Link>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          {activities.map((activity) => (
            <div
              key={activity.id}
              className="group p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors cursor-pointer"
            >
              <div className="flex items-start gap-3">
                <div className="mt-0.5 p-1.5 rounded-full bg-white shadow-sm">
                  {getActivityIcon(activity.type)}
                </div>
                <div className="flex-1 space-y-2 min-w-0">
                  <div className="text-sm leading-relaxed">
                    <span className="font-medium text-gray-900">{activity.user}</span>
                    <span className="text-gray-600"> {activity.action}</span>
                    {activity.target && (
                      <span className="font-medium text-gray-900"> {activity.target}</span>
                    )}
                  </div>

                  <div className="flex items-center justify-between">
                    {activity.metadata?.amount && (
                      <Badge
                        variant="outline"
                        className="text-xs border-0 bg-primary/5 text-primary font-medium"
                      >
                        {activity.metadata.amount}
                      </Badge>
                    )}

                    <div className="flex items-center gap-1 text-xs text-gray-500 ml-auto">
                      <Clock className="h-3 w-3" />
                      {activity.timestamp}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* View all activity */}
        <div className="mt-6 pt-4 border-t border-gray-100">
          <Link href={`/${currentOrganization?.slug}/activity`}>
            <Button
              variant="outline"
              className="w-full border-gray-200 hover:bg-gray-50 hover:border-gray-300 transition-colors"
              size="sm"
            >
              View All Activity
              <ExternalLink className="h-4 w-4 ml-2" />
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  )
}
