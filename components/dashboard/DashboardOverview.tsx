'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  TrendingUp,
  ArrowUpRight,
  Calendar,
  DollarSign,
  Clock,
  CheckCircle,
  AlertCircle,
  ExternalLink
} from 'lucide-react'
import { useOrganization } from '@/contexts/OrganizationContext'
import Link from 'next/link'

interface Transaction {
  id: string
  type: 'contribution' | 'expense' | 'collection'
  title: string
  amount: string
  status: 'pending' | 'completed' | 'failed'
  date: string
  description: string
}

export function DashboardOverview() {
  const { currentOrganization } = useOrganization()

  // Mock data - in real app, this would come from API
  const recentTransactions: Transaction[] = [
    {
      id: '1',
      type: 'KooLeknt',
      title: 'Client KooLeknt - Project Alpha',
      amount: '+$2,500.00',
      status: 'completed',
      date: '2 hours ago',
      description: '<PERSON>oLeknt received from Acme Corp',
    },
    {
      id: '2',
      type: 'expense',
      title: 'Office Supplies',
      amount: '-$156.78',
      status: 'pending',
      date: '4 hours ago',
      description: 'Pending approval from manager',
    },
    {
      id: '3',
      type: 'collection',
      title: 'Team Lunch Collection',
      amount: '+$240.00',
      status: 'completed',
      date: '1 day ago',
      description: 'Collection completed successfully',
    },
    {
      id: '4',
      type: 'contribution',
      title: 'Freelancer Contribution',
      amount: '-$800.00',
      status: 'failed',
      date: '2 days ago',
      description: 'Contribution failed - insufficient funds',
    },
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      completed: 'bg-green-50 text-green-700 border-green-200',
      pending: 'bg-yellow-50 text-yellow-700 border-yellow-200',
      failed: 'bg-red-50 text-red-700 border-red-200',
    }

    return (
      <Badge
        variant="secondary"
        className={`text-xs ${variants[status as keyof typeof variants] || ''}`}
      >
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'KooLeknt':
        return '💳'
      case 'expense':
        return '🧾'
      case 'collection':
        return '💰'
      default:
        return '📄'
    }
  }

  return (
    <div className="space-y-6">
      {/* Financial Trends Chart */}
      <Card className="bg-white border-0 shadow-sm">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2 text-lg font-semibold text-gray-900">
                <TrendingUp className="h-5 w-5 text-primary" />
                Financial Overview
              </CardTitle>
              <CardDescription className="text-sm text-gray-600">
                Revenue and expense trends for {currentOrganization?.name}
              </CardDescription>
            </div>
            <Button variant="outline" size="sm" className="border-gray-200 hover:bg-gray-50">
              <Calendar className="h-4 w-4 mr-2" />
              Last 30 days
            </Button>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          {/* Placeholder for chart - would integrate with a charting library */}
          <div className="h-64 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl flex items-center justify-center border border-gray-200">
            <div className="text-center space-y-3">
              <div className="p-4 bg-white rounded-full shadow-sm">
                <TrendingUp className="h-8 w-8 text-primary" />
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-gray-900">
                  Financial trends chart will be displayed here
                </p>
                <p className="text-xs text-gray-500">
                  Integration with Chart.js or Recharts
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent Transactions */}
      <Card className="bg-white border-0 shadow-sm">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg font-semibold text-gray-900">Recent Transactions</CardTitle>
              <CardDescription className="text-sm text-gray-600">
                Latest financial activity in your organization
              </CardDescription>
            </div>
            <Link href={`/${currentOrganization?.slug}/transactions`}>
              <Button variant="outline" size="sm" className="border-gray-200 hover:bg-gray-50">
                View All
                <ExternalLink className="h-4 w-4 ml-2" />
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-3">
            {recentTransactions.map((transaction) => (
              <div
                key={transaction.id}
                className="group flex items-center justify-between p-4 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors cursor-pointer"
              >
                <div className="flex items-center gap-4">
                  <div className="p-2 bg-white rounded-lg shadow-sm text-xl">
                    {getTypeIcon(transaction.type)}
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <p className="font-medium text-gray-900">{transaction.title}</p>
                      {getStatusIcon(transaction.status)}
                    </div>
                    <p className="text-sm text-gray-600">
                      {transaction.description}
                    </p>
                    <p className="text-xs text-gray-500">
                      {transaction.date}
                    </p>
                  </div>
                </div>
                <div className="text-right space-y-2">
                  <p className={`font-semibold ${
                    transaction.amount.startsWith('+')
                      ? 'text-green-600'
                      : 'text-red-600'
                  }`}>
                    {transaction.amount}
                  </p>
                  {getStatusBadge(transaction.status)}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
