'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import {
  Building2,
  Users,
  TrendingUp,
  Calendar,
  Target,
  Award,
  Clock,
  CheckCircle,
  AlertTriangle,
  ExternalLink,
  Settings,
  UserPlus
} from 'lucide-react'
import { useOrganization } from '@/contexts/OrganizationContext'
import { cn } from '@/lib/utils'
import Link from 'next/link'

interface OrganizationMetric {
  label: string
  value: string | number
  target?: string | number
  progress?: number
  status: 'success' | 'warning' | 'error' | 'info'
  icon: React.ElementType
}

export function OrganizationOverview() {
  const { currentOrganization } = useOrganization()

  if (!currentOrganization) return null

  // Mock organization metrics - in real app, this would come from API
  const organizationMetrics: OrganizationMetric[] = [
    {
      label: 'Active Members',
      value: currentOrganization.memberCount || 0,
      target: 50,
      progress: ((currentOrganization.memberCount || 0) / 50) * 100,
      status: 'success',
      icon: Users,
    },
    {
      label: 'Monthly Revenue Goal',
      value: '$45,231',
      target: '$50,000',
      progress: 90,
      status: 'success',
      icon: Target,
    },
    {
      label: 'Contribution Processing',
      value: '98.5%',
      progress: 98.5,
      status: 'success',
      icon: CheckCircle,
    },
    {
      label: 'Response Time',
      value: '2.3h',
      target: '< 4h',
      progress: 75,
      status: 'warning',
      icon: Clock,
    },
  ]

  const quickStats = [
    {
      label: 'Organization Type',
      value: currentOrganization.type || 'Business',
      icon: Building2,
    },
    {
      label: 'Created',
      value: new Date(currentOrganization.createdAt || Date.now()).toLocaleDateString(),
      icon: Calendar,
    },
    {
      label: 'Your Role',
      value: currentOrganization.role,
      icon: Award,
    },
  ]

  const recentAchievements = [
    {
      title: 'Contribution Milestone',
      description: 'Processed 1000+ contributions this month',
      date: '2 days ago',
      type: 'success',
    },
    {
      title: 'Team Growth',
      description: 'Added 5 new team members',
      date: '1 week ago',
      type: 'info',
    },
    {
      title: 'Revenue Target',
      description: 'Reached 90% of monthly goal',
      date: '3 days ago',
      type: 'success',
    },
  ]

  return (
    <div className="space-y-6">
      {/* Organization Header */}
      <Card className="bg-white border-0 shadow-sm">
        <CardHeader className="pb-4">
          <div className="flex flex-col gap-4">
            <div className="flex items-start gap-4">
              <div className="p-3 bg-gradient-to-br from-primary/10 to-primary/5 rounded-xl">
                <Building2 className="h-6 w-6 text-primary" />
              </div>
              <div className="flex-1 min-w-0">
                <CardTitle className="text-xl font-semibold text-gray-900 truncate">
                  {currentOrganization.name}
                </CardTitle>
                <div className="flex items-center gap-2 mt-1">
                  <Badge
                    variant="outline"
                    className="text-xs border-primary/20 bg-primary/5 text-primary font-medium"
                  >
                    {currentOrganization.role}
                  </Badge>
                  <span className="text-sm text-gray-500">•</span>
                  <span className="text-sm text-gray-500">
                    {currentOrganization.memberCount} members
                  </span>
                </div>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
              <Link href={`/${currentOrganization.slug}/members/invite`} className="flex-1">
                <Button size="sm" className="w-full">
                  <UserPlus className="h-4 w-4 mr-2" />
                  Invite Members
                </Button>
              </Link>
              <Link href={`/${currentOrganization.slug}/settings`} className="flex-1">
                <Button variant="outline" size="sm" className="w-full border-gray-200 hover:bg-gray-50">
                  <Settings className="h-4 w-4 mr-2" />
                  Settings
                </Button>
              </Link>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="grid grid-cols-1 gap-3">
            {quickStats.map((stat) => {
              const Icon = stat.icon
              return (
                <div key={stat.label} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                  <Icon className="h-4 w-4 text-gray-600" />
                  <div className="flex-1">
                    <div className="text-xs text-gray-500">{stat.label}</div>
                    <div className="text-sm font-semibold text-gray-900">{stat.value}</div>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Organization Metrics */}
      <Card className="bg-white border-0 shadow-sm">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-lg font-semibold text-gray-900">
            <TrendingUp className="h-5 w-5 text-primary" />
            Organization Metrics
          </CardTitle>
          <CardDescription className="text-sm text-gray-600">
            Key performance indicators for {currentOrganization.name}
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-5">
            {organizationMetrics.map((metric) => {
              const Icon = metric.icon
              return (
                <div key={metric.label} className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Icon className="h-4 w-4 text-gray-600" />
                      <span className="text-sm font-medium text-gray-900">{metric.label}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-bold text-gray-900">{metric.value}</span>
                      {metric.target && (
                        <span className="text-xs text-gray-500">/ {metric.target}</span>
                      )}
                      <Badge
                        variant="outline"
                        className={cn(
                          "text-xs border-0 font-medium",
                          metric.status === 'success' ? "bg-green-50 text-green-700" :
                          metric.status === 'warning' ? "bg-yellow-50 text-yellow-700" :
                          "bg-red-50 text-red-700"
                        )}
                      >
                        {metric.status}
                      </Badge>
                    </div>
                  </div>
                  {metric.progress !== undefined && (
                    <Progress
                      value={metric.progress}
                      className="h-2 bg-gray-100"
                    />
                  )}
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Recent Achievements */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5 text-primary" />
                Recent Achievements
              </CardTitle>
              <CardDescription>
                Latest milestones and accomplishments
              </CardDescription>
            </div>
            <Link href={`/${currentOrganization.slug}/achievements`}>
              <Button variant="ghost" size="sm">
                <ExternalLink className="h-4 w-4" />
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentAchievements.map((achievement, index) => (
              <div
                key={index}
                className="flex items-start gap-3 p-3 rounded-lg border hover:bg-muted/50 transition-colors"
              >
                <div className={`p-1 rounded-full ${
                  achievement.type === 'success' ? 'bg-green-100 text-green-600' :
                  achievement.type === 'info' ? 'bg-blue-100 text-blue-600' :
                  'bg-yellow-100 text-yellow-600'
                }`}>
                  {achievement.type === 'success' ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : achievement.type === 'info' ? (
                    <TrendingUp className="h-4 w-4" />
                  ) : (
                    <AlertTriangle className="h-4 w-4" />
                  )}
                </div>
                <div className="flex-1">
                  <div className="font-medium text-sm">{achievement.title}</div>
                  <div className="text-xs text-muted-foreground">{achievement.description}</div>
                  <div className="text-xs text-muted-foreground mt-1">{achievement.date}</div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
