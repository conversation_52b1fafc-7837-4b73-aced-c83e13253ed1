'use client'

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  CreditCard,
  Receipt,
  Wallet,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react'
import { useOrganization } from '@/contexts/OrganizationContext'
import { cn } from '@/lib/utils'

interface FinancialCardData {
  title: string
  value: string
  change: string
  changeType: 'positive' | 'negative' | 'neutral'
  icon: React.ElementType
  description: string
}

export function FinancialCards() {
  const { currentOrganization } = useOrganization()

  // Mock data - in real app, this would come from API
  // Enhanced with organization-specific context
  const financialData: FinancialCardData[] = [
    {
      title: 'Total Revenue',
      value: '$45,231.89',
      change: '+20.1%',
      changeType: 'positive',
      icon: DollarSign,
      description: `${currentOrganization?.name} - from last month`,
    },
    {
      title: 'Pending Contributions',
      value: '$12,234.00',
      change: '+4.3%',
      changeType: 'positive',
      icon: CreditCard,
      description: '23 contributions pending approval',
    },
    {
      title: 'Total Expenses',
      value: '$8,945.67',
      change: '-2.4%',
      changeType: 'negative',
      icon: Receipt,
      description: 'from last month',
    },
    {
      title: 'Available Balance',
      value: '$36,286.22',
      change: '+12.5%',
      changeType: 'positive',
      icon: Wallet,
      description: 'across all accounts',
    },
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
      {financialData.map((data) => {
        const Icon = data.icon
        const TrendIcon = data.changeType === 'positive' ? ArrowUpRight : ArrowDownRight

        return (
          <Card
            key={data.title}
            variant="interactive"
            className="group relative bg-white"
          >
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-caption font-medium text-muted-foreground uppercase tracking-wide">
                  {data.title}
                </CardTitle>
                <div className="p-2.5 bg-muted/50 rounded-lg group-hover:bg-primary/10 transition-all duration-200">
                  <Icon className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors duration-200" />
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Main Value */}
              <div className="text-display font-bold text-foreground tracking-tight">
                {data.value}
              </div>

              {/* Change Indicator and Badge */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className={cn(
                    "flex items-center gap-1.5 px-2.5 py-1.5 rounded-full text-xs font-semibold transition-all duration-200",
                    data.changeType === 'positive'
                      ? "bg-green-50 text-green-700 border border-green-200/50"
                      : "bg-red-50 text-red-700 border border-red-200/50"
                  )}>
                    <TrendIcon className="h-3.5 w-3.5" />
                    {data.change}
                  </div>
                </div>
                <Badge
                  variant="outline"
                  className={cn(
                    "text-xs font-medium border-0 px-2.5 py-1",
                    data.changeType === 'positive'
                      ? "bg-green-50 text-green-700"
                      : "bg-red-50 text-red-700"
                  )}
                >
                  {data.changeType === 'positive' ? 'Trending Up' : 'Trending Down'}
                </Badge>
              </div>

              {/* Description */}
              <p className="text-body text-muted-foreground leading-relaxed">
                {data.description}
              </p>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}
