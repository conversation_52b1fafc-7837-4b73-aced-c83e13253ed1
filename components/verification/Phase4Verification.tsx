'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import {
  CheckCircle,
  AlertCircle,
  Clock,
  Smartphone,
  Monitor,
  Tablet,
  Navigation,
  Settings,
  Users,
  BarChart3,
  Palette,
  Layout,
  Zap
} from 'lucide-react'
import { useOrganization } from '@/contexts/OrganizationContext'

interface VerificationItem {
  id: string
  title: string
  description: string
  status: 'completed' | 'in-progress' | 'pending'
  category: 'navigation' | 'dashboard' | 'settings' | 'members' | 'responsive'
  icon: React.ElementType
  details?: string[]
}

export function Phase4Verification() {
  const { currentOrganization } = useOrganization()

  const verificationItems: VerificationItem[] = [
    // Enhanced Navigation
    {
      id: 'nav-org-context',
      title: 'Organization-Aware Navigation',
      description: 'Navigation shows current organization context and role-based permissions',
      status: 'completed',
      category: 'navigation',
      icon: Navigation,
      details: [
        'Organization context indicator in sidebar',
        'Role-based menu item filtering',
        'Enhanced navigation descriptions',
        'Permission-based access control'
      ]
    },
    {
      id: 'nav-breadcrumbs',
      title: 'Enhanced Breadcrumbs',
      description: 'Improved breadcrumb navigation with organization scoping',
      status: 'completed',
      category: 'navigation',
      icon: Navigation,
      details: [
        'Organization-scoped breadcrumb paths',
        'Mobile-responsive truncation',
        'Interactive breadcrumb links',
        'Visual current page indicators'
      ]
    },

    // Dashboard Enhancements
    {
      id: 'dashboard-overview',
      title: 'Multi-Tenant Dashboard',
      description: 'Redesigned dashboard with organization-specific metrics',
      status: 'completed',
      category: 'dashboard',
      icon: BarChart3,
      details: [
        'Organization overview component',
        'Enhanced financial cards',
        'Role-based dashboard customization',
        'Responsive grid layout'
      ]
    },
    {
      id: 'dashboard-metrics',
      title: 'Organization Metrics',
      description: 'Key performance indicators and analytics',
      status: 'completed',
      category: 'dashboard',
      icon: BarChart3,
      details: [
        'Member count tracking',
        'Revenue goal progress',
        'KooLeknt processing metrics',
        'Response time monitoring'
      ]
    },

    // Settings Enhancements
    {
      id: 'settings-enhanced',
      title: 'Enhanced Organization Settings',
      description: 'Improved organization management interface',
      status: 'completed',
      category: 'settings',
      icon: Settings,
      details: [
        'Better organization information display',
        'Enhanced branding section',
        'Responsive layout design',
        'Improved visual hierarchy'
      ]
    },
    {
      id: 'settings-branding',
      title: 'Organization Branding',
      description: 'Customization options for organization appearance',
      status: 'completed',
      category: 'settings',
      icon: Palette,
      details: [
        'Color customization interface',
        'Logo upload placeholder',
        'Organization description',
        'Website URL configuration'
      ]
    },

    // Member Management
    {
      id: 'members-enhanced',
      title: 'Enhanced Member Management',
      description: 'Advanced member filtering and management capabilities',
      status: 'completed',
      category: 'members',
      icon: Users,
      details: [
        'Advanced search and filtering',
        'Role-based visual indicators',
        'Member activity tracking',
        'Bulk operations interface'
      ]
    },

    // Responsive Design
    {
      id: 'responsive-mobile',
      title: 'Mobile Optimization',
      description: 'Complete mobile-first responsive design',
      status: 'completed',
      category: 'responsive',
      icon: Smartphone,
      details: [
        'Mobile navigation patterns',
        'Touch-friendly interactions',
        'Responsive grid systems',
        'Mobile organization selector'
      ]
    },
    {
      id: 'responsive-tablet',
      title: 'Tablet Optimization',
      description: 'Optimized layouts for tablet viewports',
      status: 'completed',
      category: 'responsive',
      icon: Tablet,
      details: [
        'Tablet-specific grid layouts',
        'Adaptive component sizing',
        'Touch target optimization',
        'Responsive typography'
      ]
    },
    {
      id: 'responsive-desktop',
      title: 'Desktop Enhancement',
      description: 'Enhanced desktop experience with advanced layouts',
      status: 'completed',
      category: 'responsive',
      icon: Monitor,
      details: [
        'Multi-column dashboard layout',
        'Enhanced sidebar navigation',
        'Desktop-specific interactions',
        'Optimized content density'
      ]
    }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'in-progress':
        return <Clock className="h-4 w-4 text-yellow-600" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      completed: 'default',
      'in-progress': 'secondary',
      pending: 'outline'
    } as const

    const colors = {
      completed: 'bg-green-100 text-green-800 border-green-200',
      'in-progress': 'bg-yellow-100 text-yellow-800 border-yellow-200',
      pending: 'bg-gray-100 text-gray-800 border-gray-200'
    }

    return (
      <Badge variant={variants[status as keyof typeof variants]} className={colors[status as keyof typeof colors]}>
        {status.replace('-', ' ')}
      </Badge>
    )
  }

  const getCategoryIcon = (category: string) => {
    const icons = {
      navigation: Navigation,
      dashboard: BarChart3,
      settings: Settings,
      members: Users,
      responsive: Layout
    }

    const Icon = icons[category as keyof typeof icons] || Layout
    return <Icon className="h-5 w-5" />
  }

  const completedItems = verificationItems.filter(item => item.status === 'completed').length
  const totalItems = verificationItems.length
  const completionPercentage = Math.round((completedItems / totalItems) * 100)

  const categories = Array.from(new Set(verificationItems.map(item => item.category)))

  return (
    <div className="space-content">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-6 w-6 text-primary" />
            Phase 4: UI/UX Enhancement - Verification Report
          </CardTitle>
          <CardDescription>
            Comprehensive verification of Phase 4 implementation for {currentOrganization?.name || 'KooLek'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Overall Progress</span>
              <span className="text-sm text-muted-foreground">{completedItems}/{totalItems} completed</span>
            </div>
            <Progress value={completionPercentage} className="h-3" />
            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>{completedItems} Completed</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-yellow-600" />
                <span>{verificationItems.filter(item => item.status === 'in-progress').length} In Progress</span>
              </div>
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-gray-400" />
                <span>{verificationItems.filter(item => item.status === 'pending').length} Pending</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Category Breakdown */}
      <div className="responsive-grid-cards">
        {categories.map(category => {
          const categoryItems = verificationItems.filter(item => item.category === category)
          const categoryCompleted = categoryItems.filter(item => item.status === 'completed').length
          const categoryProgress = Math.round((categoryCompleted / categoryItems.length) * 100)

          return (
            <Card key={category}>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-lg">
                  {getCategoryIcon(category)}
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </CardTitle>
                <CardDescription>
                  {categoryCompleted}/{categoryItems.length} features completed
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Progress value={categoryProgress} className="h-2" />
                <div className="mt-2 text-sm text-muted-foreground">
                  {categoryProgress}% complete
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Detailed Verification Items */}
      <div className="space-content">
        <h3 className="text-h3">Detailed Verification</h3>
        <div className="space-y-4">
          {verificationItems.map(item => {
            const Icon = item.icon
            return (
              <Card key={item.id} className="transition-all hover:shadow-md">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3">
                      <div className="p-2 bg-primary/10 rounded-lg">
                        <Icon className="h-4 w-4 text-primary" />
                      </div>
                      <div>
                        <CardTitle className="text-base">{item.title}</CardTitle>
                        <CardDescription className="mt-1">
                          {item.description}
                        </CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(item.status)}
                      {getStatusBadge(item.status)}
                    </div>
                  </div>
                </CardHeader>
                {item.details && (
                  <CardContent className="pt-0">
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium text-muted-foreground">Implementation Details:</h4>
                      <ul className="space-y-1">
                        {item.details.map((detail, index) => (
                          <li key={index} className="flex items-center gap-2 text-sm">
                            <CheckCircle className="h-3 w-3 text-green-600 flex-shrink-0" />
                            {detail}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </CardContent>
                )}
              </Card>
            )
          })}
        </div>
      </div>
    </div>
  )
}
