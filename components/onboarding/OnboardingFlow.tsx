'use client'

import React, { useState } from 'react'
import { useOrganization } from '@/contexts/OrganizationContext'
import { api } from '@/lib/api'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import {
  CheckCircle,
  Circle,
  ArrowRight,
  ArrowLeft,
  Users,
  CreditCard,
  Receipt,
  BarChart3,
  Settings,
  Sparkles,
  X,
} from 'lucide-react'

interface OnboardingFlowProps {
  className?: string
  onComplete?: () => void
  onSkip?: () => void
}

const ONBOARDING_STEPS = [
  {
    id: 'welcome',
    title: 'Welcome to KooLek!',
    description: 'Let\'s get you started with your organization',
    icon: Sparkles,
    content: 'Welcome to your new KooLek organization! We\'ll guide you through the key features to help you manage your team\'s finances effectively.',
  },
  {
    id: 'team_introduction',
    title: 'Meet Your Team',
    description: 'Learn about team collaboration features',
    icon: Users,
    content: 'KooLek is designed for teams. You can invite members, assign roles, and collaborate on financial management. Each member can have different permissions based on their role.',
  },
  {
    id: 'first_contribution',
    title: 'Create Your First Contribution',
    description: 'Learn how to track contributions',
    icon: CreditCard,
    content: 'Contributions are financial payments made by team members. You can track who paid what, when, and for which purpose. All contributions can be categorized and verified.',
  },
  {
    id: 'first_expense',
    title: 'Record an Expense',
    description: 'Track team expenses',
    icon: Receipt,
    content: 'Expenses are costs incurred by the organization. Team members can submit expense reports with receipts, and admins can approve or reject them.',
  },
  {
    id: 'dashboard_tour',
    title: 'Explore the Dashboard',
    description: 'Navigate your financial overview',
    icon: BarChart3,
    content: 'Your dashboard provides a comprehensive view of your organization\'s financial health. Monitor KooLeknts, expenses, and generate reports.',
  },
  {
    id: 'organization_overview',
    title: 'Organization Settings',
    description: 'Customize your organization',
    icon: Settings,
    content: 'Configure your organization settings, including default categories, currency preferences, and team permissions.',
  },
]

export function OnboardingFlow({ className, onComplete, onSkip }: OnboardingFlowProps) {
  const { currentOrganization } = useOrganization()
  const [currentStepIndex, setCurrentStepIndex] = useState(0)

  // Get onboarding progress
  const {
    data: progress,
    isLoading,
    refetch,
  } = api.onboarding.getProgress.useQuery(
    { organizationId: currentOrganization?.id || '' },
    {
      enabled: !!currentOrganization?.id,
    }
  )

  // Complete step mutation
  const completeStepMutation = api.onboarding.completeStep.useMutation({
    onSuccess: () => {
      refetch()
    },
  })

  // Skip onboarding mutation
  const skipOnboardingMutation = api.onboarding.skip.useMutation({
    onSuccess: () => {
      onComplete?.()
    },
  })

  const currentStep = ONBOARDING_STEPS[currentStepIndex]
  const isLastStep = currentStepIndex === ONBOARDING_STEPS.length - 1

  const handleNext = async () => {
    if (!currentOrganization?.id) return

    // Mark current step as completed
    await completeStepMutation.mutateAsync({
      organizationId: currentOrganization.id,
      step: currentStep.id,
      data: { completedAt: new Date().toISOString() },
    })

    if (isLastStep) {
      onComplete?.()
    } else {
      setCurrentStepIndex(prev => prev + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(prev => prev - 1)
    }
  }

  const handleSkip = async () => {
    if (!currentOrganization?.id) return

    await skipOnboardingMutation.mutateAsync({
      organizationId: currentOrganization.id,
    })
  }

  const getStepStatus = (stepId: string) => {
    const stepProgress = progress?.steps.find(s => s.step === stepId)
    return stepProgress?.completed ? 'completed' : 'pending'
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading onboarding...</p>
        </div>
      </div>
    )
  }

  if (progress?.isCompleted) {
    return null // Don't show onboarding if already completed
  }

  return (
    <div className={className}>
      <Card className="max-w-2xl mx-auto">
        <CardHeader className="text-center">
          <div className="flex items-center justify-between mb-4">
            <Badge variant="secondary">
              Step {currentStepIndex + 1} of {ONBOARDING_STEPS.length}
            </Badge>
            <Button variant="ghost" size="sm" onClick={onSkip}>
              <X className="h-4 w-4" />
            </Button>
          </div>

          <Progress
            value={((currentStepIndex + 1) / ONBOARDING_STEPS.length) * 100}
            className="mb-6"
          />

          <div className="mb-4">
            <currentStep.icon className="h-12 w-12 text-blue-600 mx-auto mb-4" />
          </div>

          <CardTitle className="text-xl mb-2">{currentStep.title}</CardTitle>
          <CardDescription>{currentStep.description}</CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Step Content */}
          <div className="text-center">
            <p className="text-gray-700 leading-relaxed">
              {currentStep.content}
            </p>
          </div>

          {/* Step Progress Indicators */}
          <div className="flex items-center justify-center space-x-2">
            {ONBOARDING_STEPS.map((step, index) => {
              const status = getStepStatus(step.id)
              const isCurrent = index === currentStepIndex

              return (
                <div
                  key={step.id}
                  className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                    status === 'completed'
                      ? 'bg-green-100 border-green-500 text-green-600'
                      : isCurrent
                      ? 'bg-blue-100 border-blue-500 text-blue-600'
                      : 'bg-gray-100 border-gray-300 text-gray-400'
                  }`}
                >
                  {status === 'completed' ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <Circle className="h-4 w-4" />
                  )}
                </div>
              )
            })}
          </div>

          {/* Navigation */}
          <div className="flex items-center justify-between pt-4">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStepIndex === 0}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>

            <div className="flex items-center gap-2">
              <Button variant="ghost" onClick={handleSkip}>
                Skip Tour
              </Button>
              <Button
                onClick={handleNext}
                disabled={completeStepMutation.isPending}
              >
                {completeStepMutation.isPending ? (
                  'Saving...'
                ) : isLastStep ? (
                  'Complete'
                ) : (
                  <>
                    Next
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* Quick Actions for specific steps */}
          {currentStep.id === 'first_KooLeknt' && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">Try it now:</h4>
              <p className="text-sm text-blue-800 mb-3">
                Create your first KooLeknt to see how it works
              </p>
              <Button size="sm" variant="outline">
                <CreditCard className="h-4 w-4 mr-2" />
                Create KooLeknt
              </Button>
            </div>
          )}

          {currentStep.id === 'first_expense' && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h4 className="font-medium text-green-900 mb-2">Try it now:</h4>
              <p className="text-sm text-green-800 mb-3">
                Record your first expense with a receipt
              </p>
              <Button size="sm" variant="outline">
                <Receipt className="h-4 w-4 mr-2" />
                Add Expense
              </Button>
            </div>
          )}

          {currentStep.id === 'team_introduction' && (
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <h4 className="font-medium text-purple-900 mb-2">Try it now:</h4>
              <p className="text-sm text-purple-800 mb-3">
                Invite your first team member
              </p>
              <Button size="sm" variant="outline">
                <Users className="h-4 w-4 mr-2" />
                Invite Member
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
