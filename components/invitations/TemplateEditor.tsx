'use client'

import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import {
  FileText,
  Save,
  Info,
  Star,
  Code,
} from 'lucide-react'

interface TemplateFormData {
  name: string
  subject: string
  content: string
  isDefault: boolean
}

interface TemplateEditorProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  title: string
  formData: TemplateFormData
  onFormDataChange: (data: TemplateFormData) => void
  onSave: () => void
  isLoading: boolean
}

const AVAILABLE_VARIABLES = [
  { name: '{{organizationName}}', description: 'Name of the organization' },
  { name: '{{inviterName}}', description: 'Name of the person sending the invitation' },
  { name: '{{inviterEmail}}', description: 'Email of the person sending the invitation' },
  { name: '{{inviteeEmail}}', description: 'Email of the person being invited' },
  { name: '{{role}}', description: 'Role being assigned to the invitee' },
  { name: '{{invitationUrl}}', description: 'URL to accept the invitation' },
  { name: '{{expirationDate}}', description: 'When the invitation expires' },
  { name: '{{customMessage}}', description: 'Custom message from the inviter' },
]

const DEFAULT_TEMPLATE_CONTENT = `Hello,

You've been invited to join {{organizationName}} as a {{role}}.

{{inviterName}} ({{inviterEmail}}) has invited you to collaborate with the team.

{{#if customMessage}}
Personal message from {{inviterName}}:
"{{customMessage}}"
{{/if}}

To accept this invitation, please click the link below:
{{invitationUrl}}

This invitation will expire on {{expirationDate}}.

If you have any questions, feel free to reach out to {{inviterName}} at {{inviterEmail}}.

Best regards,
The {{organizationName}} Team`

export function TemplateEditor({
  open,
  onOpenChange,
  title,
  formData,
  onFormDataChange,
  onSave,
  isLoading,
}: TemplateEditorProps) {
  const handleInputChange = (field: keyof TemplateFormData, value: string | boolean) => {
    onFormDataChange({
      ...formData,
      [field]: value,
    })
  }

  const insertVariable = (variable: string) => {
    const textarea = document.getElementById('template-content') as HTMLTextAreaElement
    if (textarea) {
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const newContent =
        formData.content.substring(0, start) +
        variable +
        formData.content.substring(end)

      handleInputChange('content', newContent)

      // Set cursor position after the inserted variable
      setTimeout(() => {
        textarea.focus()
        textarea.setSelectionRange(start + variable.length, start + variable.length)
      }, 0)
    }
  }

  const useDefaultTemplate = () => {
    handleInputChange('content', DEFAULT_TEMPLATE_CONTENT)
  }

  const isFormValid = formData.name.trim() && formData.subject.trim() && formData.content.trim()

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {title}
          </DialogTitle>
          <DialogDescription>
            Create a custom email template for invitations with dynamic variables
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Template Form */}
          <div className="lg:col-span-2 space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="template-name">Template Name</Label>
                <Input
                  id="template-name"
                  placeholder="Welcome Template"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                />
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="is-default"
                  checked={formData.isDefault}
                  onCheckedChange={(checked) => handleInputChange('isDefault', checked)}
                />
                <Label htmlFor="is-default" className="flex items-center gap-2">
                  <Star className="h-4 w-4" />
                  Set as default template
                </Label>
              </div>
            </div>

            <div>
              <Label htmlFor="template-subject">Email Subject</Label>
              <Input
                id="template-subject"
                placeholder="You're invited to join {{organizationName}}"
                value={formData.subject}
                onChange={(e) => handleInputChange('subject', e.target.value)}
              />
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <Label htmlFor="template-content">Email Content</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={useDefaultTemplate}
                >
                  <Code className="h-4 w-4 mr-2" />
                  Use Default Template
                </Button>
              </div>
              <Textarea
                id="template-content"
                placeholder="Enter your email template content..."
                value={formData.content}
                onChange={(e) => handleInputChange('content', e.target.value)}
                rows={12}
                className="font-mono text-sm"
              />
            </div>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Info className="h-4 w-4" />
                  Template Guidelines
                </CardTitle>
              </CardHeader>
              <CardContent className="text-sm text-gray-600 space-y-2">
                <p>• Use variables like <code>{'{{organizationName}}'}</code> for dynamic content</p>
                <p>• Keep the subject line under 50 characters for better email client compatibility</p>
                <p>• Always include the <code>{'{{invitationUrl}}'}</code> variable for the acceptance link</p>
                <p>• Use conditional blocks like <code>{'{{#if customMessage}}'}</code> for optional content</p>
              </CardContent>
            </Card>
          </div>

          {/* Variables Panel */}
          <div className="space-y-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Available Variables</CardTitle>
                <CardDescription className="text-xs">
                  Click to insert into template
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                {AVAILABLE_VARIABLES.map((variable) => (
                  <div key={variable.name} className="space-y-1">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="w-full justify-start text-xs font-mono"
                      onClick={() => insertVariable(variable.name)}
                    >
                      {variable.name}
                    </Button>
                    <p className="text-xs text-gray-500 px-2">
                      {variable.description}
                    </p>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Preview Variables</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-xs">
                <div className="space-y-1">
                  <Badge variant="outline" className="text-xs">Sample Values</Badge>
                  <div className="space-y-1 text-gray-600">
                    <p><strong>Organization:</strong> Acme Corp</p>
                    <p><strong>Inviter:</strong> John Doe</p>
                    <p><strong>Role:</strong> Member</p>
                    <p><strong>Email:</strong> <EMAIL></p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            onClick={onSave}
            disabled={!isFormValid || isLoading}
          >
            <Save className="h-4 w-4 mr-2" />
            {isLoading ? 'Saving...' : 'Save Template'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
