'use client'

import React, { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { api } from '@/components/providers/TRPCProvider'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  CheckCircle,
  Building2,
  Users,
  Mail,
  AlertCircle,
  RefreshCw,
  ExternalLink,
} from 'lucide-react'

export function InvitationAcceptPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { data: session, status } = useSession()
  const token = searchParams.get('token')

  const [isAccepting, setIsAccepting] = useState(false)
  const [acceptResult, setAcceptResult] = useState<any>(null)

  // Get invitation details
  const {
    data: invitation,
    isLoading,
    error,
  } = api.invitations.getByToken.useQuery(
    { token: token || '' },
    {
      enabled: !!token,
      retry: false,
    }
  )

  // Accept invitation mutation
  const acceptMutation = api.invitations.accept.useMutation({
    onSuccess: (result) => {
      setAcceptResult(result)
      setIsAccepting(false)
    },
    onError: (error) => {
      console.error('Accept invitation error:', error)
      setIsAccepting(false)
    },
  })

  const handleAcceptInvitation = async () => {
    if (!token) return

    setIsAccepting(true)
    try {
      await acceptMutation.mutateAsync({ token })
    } catch (error) {
      // Error is handled by onError callback
    }
  }

  const handleSignIn = () => {
    router.push(`/signin?callbackUrl=${encodeURIComponent(window.location.href)}`)
  }

  const handleGoToDashboard = () => {
    if (acceptResult?.organization) {
      router.push(`/${acceptResult.organization.slug}/dashboard`)
    } else {
      router.push('/dashboard')
    }
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <RefreshCw className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Loading invitation...</h3>
              <p className="text-gray-600">Please wait while we verify your invitation.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Error state
  if (error || !invitation) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-red-600 mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2 text-red-600">Invalid Invitation</h3>
              <p className="text-gray-600 mb-4">
                This invitation link is invalid, expired, or has already been used.
              </p>
              <Button onClick={() => router.push('/')}>
                Go to Homepage
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Success state
  if (acceptResult?.success) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2 text-green-600">
                Welcome to {acceptResult.organization.name}!
              </h3>
              <p className="text-gray-600 mb-6">
                {acceptResult.message}
              </p>

              {acceptResult.requiresSignIn ? (
                <div className="space-y-3">
                  <p className="text-sm text-gray-600">
                    Please sign in to access your new organization.
                  </p>
                  <Button onClick={handleSignIn} className="w-full">
                    <Mail className="h-4 w-4 mr-2" />
                    Sign In
                  </Button>
                </div>
              ) : (
                <Button onClick={handleGoToDashboard} className="w-full">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Go to Dashboard
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Main invitation display
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 p-3 bg-blue-100 rounded-full w-fit">
            <Building2 className="h-8 w-8 text-blue-600" />
          </div>
          <CardTitle className="text-xl">You're Invited!</CardTitle>
          <CardDescription>
            You've been invited to join <strong>{invitation.organization.name}</strong> on KooLek
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Organization Info */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium">Organization Details</h4>
              <Badge variant="outline">
                {invitation.role.toLowerCase()}
              </Badge>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <Building2 className="h-4 w-4 text-gray-500" />
                <span>{invitation.organization.name}</span>
              </div>
              {invitation.organization.domain && (
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span>{invitation.organization.domain}</span>
                </div>
              )}
            </div>
          </div>

          {/* Role Description */}
          <div className="text-sm text-gray-600">
            <p className="font-medium mb-2">As a {invitation.role.toLowerCase()}, you'll be able to:</p>
            <ul className="space-y-1 ml-4">
              {invitation.role === 'OWNER' && (
                <>
                  <li>• Full administrative access</li>
                  <li>• Manage organization settings</li>
                  <li>• Invite and manage team members</li>
                  <li>• Access all financial data</li>
                </>
              )}
              {invitation.role === 'ADMIN' && (
                <>
                  <li>• Manage team members and roles</li>
                  <li>• Access all financial data</li>
                  <li>• Configure organization settings</li>
                  <li>• Approve KooLeknts and expenses</li>
                </>
              )}
              {invitation.role === 'MEMBER' && (
                <>
                  <li>• Create and manage contributions</li>
                  <li>• Submit expense reports</li>
                  <li>• View team financial data</li>
                  <li>• Participate in collections</li>
                </>
              )}
              {invitation.role === 'VIEWER' && (
                <>
                  <li>• View financial reports</li>
                  <li>• Access read-only data</li>
                  <li>• Monitor team activities</li>
                </>
              )}
            </ul>
          </div>

          {/* Personal Message */}
          {invitation.message && (
            <div className="bg-blue-50 border-l-4 border-blue-400 p-4">
              <p className="text-sm text-blue-800">
                <strong>Personal message:</strong> {invitation.message}
              </p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="space-y-3">
            {status === 'loading' ? (
              <Button disabled className="w-full">
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Checking authentication...
              </Button>
            ) : !session ? (
              <div className="space-y-3">
                <p className="text-sm text-gray-600 text-center">
                  You need to sign in to accept this invitation
                </p>
                <Button onClick={handleSignIn} className="w-full">
                  <Mail className="h-4 w-4 mr-2" />
                  Sign In to Accept
                </Button>
              </div>
            ) : (
              <Button
                onClick={handleAcceptInvitation}
                disabled={isAccepting || acceptMutation.isPending}
                className="w-full"
              >
                {isAccepting || acceptMutation.isPending ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Accepting invitation...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Accept Invitation
                  </>
                )}
              </Button>
            )}
          </div>

          {/* Error Display */}
          {acceptMutation.error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-red-600" />
                <p className="text-sm text-red-800">
                  {acceptMutation.error.message || 'Failed to accept invitation'}
                </p>
              </div>
            </div>
          )}

          {/* Footer */}
          <div className="text-center text-xs text-gray-500">
            <p>
              This invitation expires on{' '}
              {new Date(invitation.expiresAt).toLocaleDateString()}
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
