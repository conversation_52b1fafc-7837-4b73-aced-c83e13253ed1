'use client'

import React, { useState } from 'react'
import { useOrganization } from '@/contexts/OrganizationContext'
import { api } from '@/components/providers/TRPCProvider'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  UserPlus,
  Search,
  Filter,
  MoreHorizontal,
  Mail,
  Clock,
  CheckCircle,
  XCircle,
  Upload,
  Download,
  RefreshCw,
  BarChart3,
  FileText,
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { BulkInviteDialog } from './BulkInviteDialog'
import { InvitationTemplates } from './InvitationTemplates'

interface InvitationDashboardProps {
  className?: string
}

export function InvitationDashboard({ className }: InvitationDashboardProps) {
  const { currentOrganization } = useOrganization()
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [roleFilter, setRoleFilter] = useState<string>('all')
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false)
  const [isBulkInviteDialogOpen, setIsBulkInviteDialogOpen] = useState(false)
  const [isTemplatesDialogOpen, setIsTemplatesDialogOpen] = useState(false)
  const [selectedInvitations, setSelectedInvitations] = useState<string[]>([])
  const [showAnalytics, setShowAnalytics] = useState(false)

  // Form state for single invitation
  const [inviteForm, setInviteForm] = useState({
    email: '',
    role: 'MEMBER' as const,
    message: '',
  })

  // Fetch invitations with filters
  const {
    data: invitationsData,
    isLoading,
    refetch,
  } = api.invitations.getOrganizationInvitations.useQuery(
    {
      organizationId: currentOrganization?.id || '',
      search: searchTerm || undefined,
      status: statusFilter !== 'all' ? (statusFilter as any) : undefined,
      role: roleFilter !== 'all' ? (roleFilter as any) : undefined,
      limit: 50,
      offset: 0,
    },
    {
      enabled: !!currentOrganization?.id,
      refetchInterval: 30000, // Refetch every 30 seconds
    }
  )

  // Mutations
  const inviteMutation = api.invitations.invite.useMutation({
    onSuccess: () => {
      setIsInviteDialogOpen(false)
      setInviteForm({ email: '', role: 'MEMBER', message: '' })
      refetch()
    },
  })

  const resendMutation = api.invitations.resend.useMutation({
    onSuccess: () => refetch(),
  })

  const revokeMutation = api.invitations.revoke.useMutation({
    onSuccess: () => refetch(),
  })

  const bulkRevokeMutation = api.invitations.bulkRevoke.useMutation({
    onSuccess: () => {
      setSelectedInvitations([])
      refetch()
    },
  })

  const bulkResendMutation = api.invitations.bulkResend.useMutation({
    onSuccess: () => {
      setSelectedInvitations([])
      refetch()
    },
  })

  const handleInvite = async () => {
    if (!currentOrganization?.id || !inviteForm.email) return

    await inviteMutation.mutateAsync({
      organizationId: currentOrganization.id,
      email: inviteForm.email,
      role: inviteForm.role,
    })
  }

  const handleResend = async (invitationId: string) => {
    await resendMutation.mutateAsync({ invitationId })
  }

  const handleRevoke = async (invitationId: string) => {
    await revokeMutation.mutateAsync({ invitationId })
  }

  const handleBulkRevoke = async () => {
    if (selectedInvitations.length === 0) return
    if (confirm(`Are you sure you want to revoke ${selectedInvitations.length} invitations?`)) {
      await bulkRevokeMutation.mutateAsync({ invitationIds: selectedInvitations })
    }
  }

  const handleBulkResend = async () => {
    if (selectedInvitations.length === 0) return
    if (confirm(`Are you sure you want to resend ${selectedInvitations.length} invitations?`)) {
      await bulkResendMutation.mutateAsync({ invitationIds: selectedInvitations })
    }
  }

  const handleSelectAll = () => {
    if (selectedInvitations.length === invitationsData?.invitations.length) {
      setSelectedInvitations([])
    } else {
      setSelectedInvitations(invitationsData?.invitations.map(inv => inv.id) || [])
    }
  }

  const handleSelectInvitation = (invitationId: string) => {
    setSelectedInvitations(prev =>
      prev.includes(invitationId)
        ? prev.filter(id => id !== invitationId)
        : [...prev, invitationId]
    )
  }

  // Analytics calculations
  const analytics = invitationsData?.invitations ? {
    total: invitationsData.invitations.length,
    pending: invitationsData.invitations.filter(inv => inv.status === 'PENDING').length,
    accepted: invitationsData.invitations.filter(inv => inv.status === 'ACCEPTED').length,
    expired: invitationsData.invitations.filter(inv => inv.status === 'EXPIRED').length,
    revoked: invitationsData.invitations.filter(inv => inv.status === 'REVOKED').length,
    acceptanceRate: invitationsData.invitations.length > 0
      ? Math.round((invitationsData.invitations.filter(inv => inv.status === 'ACCEPTED').length / invitationsData.invitations.length) * 100)
      : 0,
  } : null

  const getStatusBadge = (status: string) => {
    const variants = {
      PENDING: { variant: 'secondary' as const, icon: Clock, color: 'text-yellow-600' },
      ACCEPTED: { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' },
      EXPIRED: { variant: 'destructive' as const, icon: XCircle, color: 'text-red-600' },
      REVOKED: { variant: 'outline' as const, icon: XCircle, color: 'text-gray-600' },
    }

    const config = variants[status as keyof typeof variants] || variants.PENDING
    const Icon = config.icon

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className={`h-3 w-3 ${config.color}`} />
        {status.toLowerCase()}
      </Badge>
    )
  }

  const getRoleBadge = (role: string) => {
    const colors = {
      OWNER: 'bg-purple-100 text-purple-800',
      ADMIN: 'bg-blue-100 text-blue-800',
      MEMBER: 'bg-green-100 text-green-800',
      VIEWER: 'bg-gray-100 text-gray-800',
    }

    return (
      <Badge variant="outline" className={colors[role as keyof typeof colors]}>
        {role.toLowerCase()}
      </Badge>
    )
  }

  if (!currentOrganization) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-muted-foreground">Please select an organization</p>
      </div>
    )
  }

  return (
    <div className={className}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Team Invitations</h1>
          <p className="text-sm text-gray-600">
            Manage invitations for {currentOrganization.name}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAnalytics(!showAnalytics)}
          >
            <BarChart3 className="h-4 w-4 mr-2" />
            {showAnalytics ? 'Hide' : 'Show'} Analytics
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsTemplatesDialogOpen(true)}
          >
            <FileText className="h-4 w-4 mr-2" />
            Templates
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsBulkInviteDialogOpen(true)}
          >
            <Upload className="h-4 w-4 mr-2" />
            Bulk Invite
          </Button>
          <Button onClick={() => setIsInviteDialogOpen(true)}>
            <UserPlus className="h-4 w-4 mr-2" />
            Invite Member
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex items-center gap-4 mb-6">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search by email..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[140px]">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="PENDING">Pending</SelectItem>
            <SelectItem value="ACCEPTED">Accepted</SelectItem>
            <SelectItem value="EXPIRED">Expired</SelectItem>
            <SelectItem value="REVOKED">Revoked</SelectItem>
          </SelectContent>
        </Select>
        <Select value={roleFilter} onValueChange={setRoleFilter}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="Role" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Roles</SelectItem>
            <SelectItem value="OWNER">Owner</SelectItem>
            <SelectItem value="ADMIN">Admin</SelectItem>
            <SelectItem value="MEMBER">Member</SelectItem>
            <SelectItem value="VIEWER">Viewer</SelectItem>
          </SelectContent>
        </Select>
        <Button variant="outline" size="sm" onClick={() => refetch()}>
          <RefreshCw className="h-4 w-4" />
        </Button>
      </div>

      {/* Analytics Section */}
      {showAnalytics && analytics && (
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Invitations</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.total}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Pending</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{analytics.pending}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Accepted</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{analytics.accepted}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Expired/Revoked</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{analytics.expired + analytics.revoked}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Acceptance Rate</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{analytics.acceptanceRate}%</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Bulk Actions Toolbar */}
      {selectedInvitations.length > 0 && (
        <div className="flex items-center justify-between bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-blue-900">
              {selectedInvitations.length} invitation{selectedInvitations.length > 1 ? 's' : ''} selected
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleBulkResend}
              disabled={bulkResendMutation.isLoading}
            >
              <Mail className="h-4 w-4 mr-2" />
              Resend
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleBulkRevoke}
              disabled={bulkRevokeMutation.isLoading}
            >
              <XCircle className="h-4 w-4 mr-2" />
              Revoke
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSelectedInvitations([])}
            >
              Clear Selection
            </Button>
          </div>
        </div>
      )}

      {/* Invitations Table */}
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[50px]">
                <input
                  type="checkbox"
                  checked={selectedInvitations.length === invitationsData?.invitations.length && invitationsData?.invitations.length > 0}
                  onChange={handleSelectAll}
                  className="rounded"
                />
              </TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Invited By</TableHead>
              <TableHead>Invited</TableHead>
              <TableHead>Expires</TableHead>
              <TableHead className="w-[50px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8">
                  <div className="flex items-center justify-center">
                    <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                    Loading invitations...
                  </div>
                </TableCell>
              </TableRow>
            ) : invitationsData?.invitations.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8">
                  <div className="text-center">
                    <UserPlus className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No invitations</h3>
                    <p className="text-gray-600 mb-4">
                      Get started by inviting team members to your organization.
                    </p>
                    <Button onClick={() => setIsInviteDialogOpen(true)}>
                      <UserPlus className="h-4 w-4 mr-2" />
                      Send First Invitation
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              invitationsData?.invitations.map((invitation) => (
                <TableRow key={invitation.id}>
                  <TableCell>
                    <input
                      type="checkbox"
                      checked={selectedInvitations.includes(invitation.id)}
                      onChange={() => handleSelectInvitation(invitation.id)}
                      className="rounded"
                    />
                  </TableCell>
                  <TableCell className="font-medium">{invitation.email}</TableCell>
                  <TableCell>{getRoleBadge(invitation.role)}</TableCell>
                  <TableCell>{getStatusBadge(invitation.status)}</TableCell>
                  <TableCell>
                    {invitation.inviter ? (
                      <div className="flex items-center gap-2">
                        <div className="h-6 w-6 rounded-full bg-gray-200 flex items-center justify-center text-xs">
                          {invitation.inviter.name?.[0] || invitation.inviter.email[0]}
                        </div>
                        <span className="text-sm">{invitation.inviter.name || invitation.inviter.email}</span>
                      </div>
                    ) : (
                      <span className="text-gray-400">System</span>
                    )}
                  </TableCell>
                  <TableCell className="text-sm text-gray-600">
                    {formatDistanceToNow(new Date(invitation.createdAt), { addSuffix: true })}
                  </TableCell>
                  <TableCell className="text-sm text-gray-600">
                    {formatDistanceToNow(new Date(invitation.expiresAt), { addSuffix: true })}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {invitation.status === 'PENDING' && (
                          <>
                            <DropdownMenuItem onClick={() => handleResend(invitation.id)}>
                              <Mail className="h-4 w-4 mr-2" />
                              Resend
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleRevoke(invitation.id)}
                              className="text-red-600"
                            >
                              <XCircle className="h-4 w-4 mr-2" />
                              Revoke
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Single Invite Dialog */}
      <Dialog open={isInviteDialogOpen} onOpenChange={setIsInviteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Invite Team Member</DialogTitle>
            <DialogDescription>
              Send an invitation to join {currentOrganization.name}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={inviteForm.email}
                onChange={(e) => setInviteForm(prev => ({ ...prev, email: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="role">Role</Label>
              <Select
                value={inviteForm.role}
                onValueChange={(value: any) => setInviteForm(prev => ({ ...prev, role: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="VIEWER">Viewer - Can view data</SelectItem>
                  <SelectItem value="MEMBER">Member - Can create and edit</SelectItem>
                  <SelectItem value="ADMIN">Admin - Can manage team</SelectItem>
                  <SelectItem value="OWNER">Owner - Full access</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="message">Personal Message (Optional)</Label>
              <Textarea
                id="message"
                placeholder="Add a personal message to the invitation..."
                value={inviteForm.message}
                onChange={(e) => setInviteForm(prev => ({ ...prev, message: e.target.value }))}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsInviteDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleInvite}
              disabled={!inviteForm.email || inviteMutation.isLoading}
            >
              {inviteMutation.isLoading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <Mail className="h-4 w-4 mr-2" />
                  Send Invitation
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Bulk Invite Dialog */}
      <BulkInviteDialog
        open={isBulkInviteDialogOpen}
        onOpenChange={setIsBulkInviteDialogOpen}
        onSuccess={() => refetch()}
      />

      {/* Invitation Templates Dialog */}
      <InvitationTemplates
        open={isTemplatesDialogOpen}
        onOpenChange={setIsTemplatesDialogOpen}
      />
    </div>
  )
}
