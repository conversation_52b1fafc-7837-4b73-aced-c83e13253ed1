'use client'

import React, { useState, useRef } from 'react'
import { useOrganization } from '@/contexts/OrganizationContext'
import { api } from '@/components/providers/TRPCProvider'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  Upload,
  Download,
  X,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  FileText,
  Users,
} from 'lucide-react'
import Papa from 'papaparse'

interface BulkInviteDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

interface InvitationRow {
  email: string
  role: 'OWNER' | 'ADMIN' | 'MEMBER' | 'VIEWER'
  message?: string
  status?: 'valid' | 'invalid' | 'duplicate'
  error?: string
}

export function BulkInviteDialog({ open, onOpenChange, onSuccess }: BulkInviteDialogProps) {
  const { currentOrganization } = useOrganization()
  const fileInputRef = useRef<HTMLInputElement>(null)

  const [step, setStep] = useState<'upload' | 'preview' | 'sending' | 'results'>('upload')
  const [invitations, setInvitations] = useState<InvitationRow[]>([])
  const [defaultRole, setDefaultRole] = useState<'OWNER' | 'ADMIN' | 'MEMBER' | 'VIEWER'>('MEMBER')
  const [defaultMessage, setDefaultMessage] = useState('')
  const [selectedTemplate, setSelectedTemplate] = useState<string>('')
  const [csvText, setCsvText] = useState('')
  const [sendingProgress, setSendingProgress] = useState(0)
  const [results, setResults] = useState<any>(null)

  // Fetch templates
  const { data: templates } = api.invitationTemplates.getOrganizationTemplates.useQuery(
    { organizationId: currentOrganization?.id || '' },
    { enabled: !!currentOrganization?.id && open }
  )

  // Mutations
  const bulkInviteMutation = api.invitations.bulkInvite.useMutation({
    onSuccess: (data) => {
      setResults(data)
      setStep('results')
      onSuccess?.()
    },
  })

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    Papa.parse(file, {
      header: true,
      complete: (results) => {
        const parsedInvitations = results.data
          .filter((row: any) => row.email && row.email.trim())
          .map((row: any) => ({
            email: row.email.trim().toLowerCase(),
            role: (row.role?.toUpperCase() as any) || defaultRole,
            message: row.message || defaultMessage,
            status: 'valid' as const,
          }))

        validateInvitations(parsedInvitations)
      },
      error: (error) => {
        console.error('CSV parsing error:', error)
      },
    })
  }

  const handleCsvTextParse = () => {
    if (!csvText.trim()) return

    Papa.parse(csvText, {
      header: true,
      complete: (results) => {
        const parsedInvitations = results.data
          .filter((row: any) => row.email && row.email.trim())
          .map((row: any) => ({
            email: row.email.trim().toLowerCase(),
            role: (row.role?.toUpperCase() as any) || defaultRole,
            message: row.message || defaultMessage,
            status: 'valid' as const,
          }))

        validateInvitations(parsedInvitations)
      },
    })
  }

  const handleManualEntry = () => {
    const emails = csvText
      .split('\n')
      .map(line => line.trim())
      .filter(line => line && line.includes('@'))

    const parsedInvitations = emails.map(email => ({
      email: email.toLowerCase(),
      role: defaultRole,
      message: defaultMessage,
      status: 'valid' as const,
    }))

    validateInvitations(parsedInvitations)
  }

  const validateInvitations = (invitations: InvitationRow[]) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    const validRoles = ['OWNER', 'ADMIN', 'MEMBER', 'VIEWER']
    const seenEmails = new Set<string>()

    const validatedInvitations = invitations.map(invitation => {
      let status: 'valid' | 'invalid' | 'duplicate' = 'valid'
      let error = ''

      // Check email format
      if (!emailRegex.test(invitation.email)) {
        status = 'invalid'
        error = 'Invalid email format'
      }
      // Check for duplicates
      else if (seenEmails.has(invitation.email)) {
        status = 'duplicate'
        error = 'Duplicate email'
      }
      // Check role
      else if (!validRoles.includes(invitation.role)) {
        status = 'invalid'
        error = 'Invalid role'
      }

      seenEmails.add(invitation.email)

      return {
        ...invitation,
        status,
        error,
      }
    })

    setInvitations(validatedInvitations)
    setStep('preview')
  }

  const handleSendInvitations = async () => {
    if (!currentOrganization?.id) return

    const validInvitations = invitations.filter(inv => inv.status === 'valid')
    if (validInvitations.length === 0) return

    setStep('sending')
    setSendingProgress(0)

    try {
      await bulkInviteMutation.mutateAsync({
        organizationId: currentOrganization.id,
        invitations: validInvitations.map(inv => ({
          email: inv.email,
          role: inv.role,
          message: inv.message,
        })),
        templateId: selectedTemplate || undefined,
      })
    } catch (error) {
      console.error('Bulk invite error:', error)
    }
  }

  const handleReset = () => {
    setStep('upload')
    setInvitations([])
    setCsvText('')
    setSendingProgress(0)
    setResults(null)
    setSelectedTemplate('')
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const downloadTemplate = () => {
    const csvContent = 'email,role,message\<EMAIL>,MEMBER,Welcome to our team!'
    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'invitation-template.csv'
    a.click()
    window.URL.revokeObjectURL(url)
  }

  const validInvitations = invitations.filter(inv => inv.status === 'valid')
  const invalidInvitations = invitations.filter(inv => inv.status !== 'valid')

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Bulk Invite Team Members</DialogTitle>
          <DialogDescription>
            Upload a CSV file or enter email addresses to invite multiple team members at once
          </DialogDescription>
        </DialogHeader>

        {step === 'upload' && (
          <div className="space-y-6">
            {/* Default Settings */}
            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label htmlFor="defaultRole">Default Role</Label>
                <Select value={defaultRole} onValueChange={(value: any) => setDefaultRole(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="VIEWER">Viewer</SelectItem>
                    <SelectItem value="MEMBER">Member</SelectItem>
                    <SelectItem value="ADMIN">Admin</SelectItem>
                    <SelectItem value="OWNER">Owner</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="template">Email Template (Optional)</Label>
                <Select value={selectedTemplate} onValueChange={setSelectedTemplate}>
                  <SelectTrigger>
                    <SelectValue placeholder="Use default template" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Default Template</SelectItem>
                    {templates?.map((template) => (
                      <SelectItem key={template.id} value={template.id}>
                        {template.name}
                        {template.isDefault && ' (Default)'}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="defaultMessage">Default Message (Optional)</Label>
                <Input
                  id="defaultMessage"
                  placeholder="Welcome to our team!"
                  value={defaultMessage}
                  onChange={(e) => setDefaultMessage(e.target.value)}
                />
              </div>
            </div>

            {/* Upload Options */}
            <div className="space-y-4">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Upload CSV File</h3>
                <p className="text-gray-600 mb-4">
                  Upload a CSV file with columns: email, role (optional), message (optional)
                </p>
                <div className="flex items-center justify-center gap-2">
                  <Button onClick={() => fileInputRef.current?.click()}>
                    <Upload className="h-4 w-4 mr-2" />
                    Choose File
                  </Button>
                  <Button variant="outline" onClick={downloadTemplate}>
                    <Download className="h-4 w-4 mr-2" />
                    Download Template
                  </Button>
                </div>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".csv"
                  onChange={handleFileUpload}
                  className="hidden"
                />
              </div>

              <div className="text-center text-gray-500">or</div>

              <div>
                <Label htmlFor="csvText">Paste CSV Data or Email List</Label>
                <Textarea
                  id="csvText"
                  placeholder="email,role,message&#10;<EMAIL>,MEMBER,Welcome!&#10;<EMAIL>,ADMIN&#10;&#10;Or just email addresses:&#10;<EMAIL>&#10;<EMAIL>"
                  value={csvText}
                  onChange={(e) => setCsvText(e.target.value)}
                  rows={8}
                />
                <div className="flex gap-2 mt-2">
                  <Button onClick={handleCsvTextParse} disabled={!csvText.trim()}>
                    Parse CSV
                  </Button>
                  <Button variant="outline" onClick={handleManualEntry} disabled={!csvText.trim()}>
                    Parse Email List
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {step === 'preview' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium">Review Invitations</h3>
                <p className="text-sm text-gray-600">
                  {validInvitations.length} valid, {invalidInvitations.length} invalid
                </p>
              </div>
              <Button variant="outline" onClick={handleReset}>
                <X className="h-4 w-4 mr-2" />
                Start Over
              </Button>
            </div>

            <div className="border rounded-lg max-h-96 overflow-y-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Email</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Message</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {invitations.map((invitation, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{invitation.email}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{invitation.role.toLowerCase()}</Badge>
                      </TableCell>
                      <TableCell className="max-w-xs truncate">
                        {invitation.message || '-'}
                      </TableCell>
                      <TableCell>
                        {invitation.status === 'valid' ? (
                          <Badge variant="default" className="bg-green-100 text-green-800">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Valid
                          </Badge>
                        ) : (
                          <Badge variant="destructive">
                            <AlertCircle className="h-3 w-3 mr-1" />
                            {invitation.error}
                          </Badge>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        )}

        {step === 'sending' && (
          <div className="space-y-6 text-center">
            <div>
              <RefreshCw className="h-12 w-12 text-blue-600 mx-auto mb-4 animate-spin" />
              <h3 className="text-lg font-medium mb-2">Sending Invitations</h3>
              <p className="text-gray-600">
                Please wait while we send {validInvitations.length} invitations...
              </p>
            </div>
            <Progress value={sendingProgress} className="w-full" />
          </div>
        )}

        {step === 'results' && results && (
          <div className="space-y-4">
            <div className="text-center">
              <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Invitations Sent</h3>
              <p className="text-gray-600">
                {results.success} successful, {results.errors} failed
              </p>
            </div>

            {results.errorDetails && results.errorDetails.length > 0 && (
              <div className="border rounded-lg p-4">
                <h4 className="font-medium mb-2 text-red-600">Failed Invitations</h4>
                <div className="space-y-2">
                  {results.errorDetails.map((error: any, index: number) => (
                    <div key={index} className="flex items-center justify-between text-sm">
                      <span>{error.email}</span>
                      <span className="text-red-600">{error.error}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        <DialogFooter>
          {step === 'preview' && (
            <>
              <Button variant="outline" onClick={handleReset}>
                Back
              </Button>
              <Button
                onClick={handleSendInvitations}
                disabled={validInvitations.length === 0 || bulkInviteMutation.isLoading}
              >
                <Users className="h-4 w-4 mr-2" />
                Send {validInvitations.length} Invitations
              </Button>
            </>
          )}
          {step === 'results' && (
            <Button onClick={() => onOpenChange(false)}>
              Done
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
