'use client'

import React, { useState } from 'react'
import { useOrganization } from '@/contexts/OrganizationContext'
import { api } from '@/components/providers/TRPCProvider'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  FileText,
  Plus,
  MoreHorizontal,
  <PERSON>,
  <PERSON><PERSON>,
  Trash2,
  <PERSON>,
  Star,
  <PERSON>Off,
  <PERSON>f<PERSON><PERSON><PERSON>,
} from 'lucide-react'
import { TemplateEditor } from './TemplateEditor'
import { TemplatePreview } from './TemplatePreview'

interface InvitationTemplatesProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

interface TemplateFormData {
  name: string
  subject: string
  content: string
  isDefault: boolean
}

export function InvitationTemplates({ open, onOpenChange }: InvitationTemplatesProps) {
  const { currentOrganization } = useOrganization()
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isPreviewDialogOpen, setIsPreviewDialogOpen] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null)
  const [formData, setFormData] = useState<TemplateFormData>({
    name: '',
    subject: '',
    content: '',
    isDefault: false,
  })

  // Fetch templates
  const {
    data: templates,
    isLoading,
    refetch,
  } = api.invitationTemplates.getOrganizationTemplates.useQuery(
    { organizationId: currentOrganization?.id || '' },
    { enabled: !!currentOrganization?.id && open }
  )

  // Mutations
  const createMutation = api.invitationTemplates.create.useMutation({
    onSuccess: () => {
      setIsCreateDialogOpen(false)
      resetForm()
      refetch()
    },
  })

  const updateMutation = api.invitationTemplates.update.useMutation({
    onSuccess: () => {
      setIsEditDialogOpen(false)
      resetForm()
      refetch()
    },
  })

  const deleteMutation = api.invitationTemplates.delete.useMutation({
    onSuccess: () => refetch(),
  })

  const duplicateMutation = api.invitationTemplates.duplicate.useMutation({
    onSuccess: () => refetch(),
  })

  const resetForm = () => {
    setFormData({
      name: '',
      subject: '',
      content: '',
      isDefault: false,
    })
    setSelectedTemplate(null)
  }

  const handleCreate = async () => {
    if (!currentOrganization?.id) return

    await createMutation.mutateAsync({
      organizationId: currentOrganization.id,
      ...formData,
    })
  }

  const handleUpdate = async () => {
    if (!selectedTemplate) return

    await updateMutation.mutateAsync({
      templateId: selectedTemplate.id,
      ...formData,
    })
  }

  const handleEdit = (template: any) => {
    setSelectedTemplate(template)
    setFormData({
      name: template.name,
      subject: template.subject,
      content: template.content,
      isDefault: template.isDefault,
    })
    setIsEditDialogOpen(true)
  }

  const handlePreview = (template: any) => {
    setSelectedTemplate(template)
    setIsPreviewDialogOpen(true)
  }

  const handleDuplicate = async (template: any) => {
    await duplicateMutation.mutateAsync({
      templateId: template.id,
      name: `${template.name} (Copy)`,
    })
  }

  const handleDelete = async (template: any) => {
    if (confirm('Are you sure you want to delete this template?')) {
      await deleteMutation.mutateAsync({ templateId: template.id })
    }
  }

  if (!currentOrganization) return null

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-6xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Invitation Templates
            </DialogTitle>
            <DialogDescription>
              Create and manage email templates for invitations to {currentOrganization.name}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Header Actions */}
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium">Templates</h3>
                <p className="text-sm text-gray-600">
                  {templates?.length || 0} templates available
                </p>
              </div>
              <Button onClick={() => setIsCreateDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create Template
              </Button>
            </div>

            {/* Templates Table */}
            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Subject</TableHead>
                    <TableHead>Default</TableHead>
                    <TableHead>Usage</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead className="w-[50px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <div className="flex items-center justify-center">
                          <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                          Loading templates...
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : templates?.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <div className="text-center">
                          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                          <h3 className="text-lg font-medium text-gray-900 mb-2">No templates</h3>
                          <p className="text-gray-600 mb-4">
                            Create your first invitation template to get started.
                          </p>
                          <Button onClick={() => setIsCreateDialogOpen(true)}>
                            <Plus className="h-4 w-4 mr-2" />
                            Create Template
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    templates?.map((template) => (
                      <TableRow key={template.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            {template.name}
                            {template.isDefault && (
                              <Star className="h-4 w-4 text-yellow-500 fill-current" />
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="max-w-xs truncate">
                          {template.subject}
                        </TableCell>
                        <TableCell>
                          {template.isDefault ? (
                            <Badge variant="default">Default</Badge>
                          ) : (
                            <Badge variant="outline">Custom</Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {template._count?.invitations || 0} uses
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {new Date(template.createdAt).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handlePreview(template)}>
                                <Eye className="h-4 w-4 mr-2" />
                                Preview
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleEdit(template)}>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleDuplicate(template)}>
                                <Copy className="h-4 w-4 mr-2" />
                                Duplicate
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleDelete(template)}
                                className="text-red-600"
                                disabled={template._count?.invitations > 0}
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Create Template Dialog */}
      <TemplateEditor
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        title="Create Invitation Template"
        formData={formData}
        onFormDataChange={setFormData}
        onSave={handleCreate}
        isLoading={createMutation.isLoading}
      />

      {/* Edit Template Dialog */}
      <TemplateEditor
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        title="Edit Invitation Template"
        formData={formData}
        onFormDataChange={setFormData}
        onSave={handleUpdate}
        isLoading={updateMutation.isLoading}
      />

      {/* Preview Template Dialog */}
      <TemplatePreview
        open={isPreviewDialogOpen}
        onOpenChange={setIsPreviewDialogOpen}
        template={selectedTemplate}
      />
    </>
  )
}
