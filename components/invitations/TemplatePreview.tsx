'use client'

import React, { useState } from 'react'
import { useOrganization } from '@/contexts/OrganizationContext'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Eye,
  Mail,
  User,
  Calendar,
  Link,
  RefreshCw,
} from 'lucide-react'

interface TemplatePreviewProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  template: any
}

interface PreviewData {
  organizationName: string
  inviterName: string
  inviterEmail: string
  inviteeEmail: string
  role: string
  customMessage: string
  invitationUrl: string
  expirationDate: string
}

const SAMPLE_ROLES = ['OWNER', 'ADMIN', 'MEMBER', 'VIEWER']

export function TemplatePreview({ open, onOpenChange, template }: TemplatePreviewProps) {
  const { currentOrganization } = useOrganization()

  const [previewData, setPreviewData] = useState<PreviewData>({
    organizationName: currentOrganization?.name || 'Acme Corp',
    inviterName: 'John Doe',
    inviterEmail: '<EMAIL>',
    inviteeEmail: '<EMAIL>',
    role: 'MEMBER',
    customMessage: 'Welcome to our team! We\'re excited to have you join us.',
    invitationUrl: 'https://KooLek.app/invitations/accept/abc123',
    expirationDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString(),
  })

  const renderTemplate = (content: string) => {
    if (!content) return ''

    let rendered = content

    // Replace simple variables
    rendered = rendered.replace(/\{\{organizationName\}\}/g, previewData.organizationName)
    rendered = rendered.replace(/\{\{inviterName\}\}/g, previewData.inviterName)
    rendered = rendered.replace(/\{\{inviterEmail\}\}/g, previewData.inviterEmail)
    rendered = rendered.replace(/\{\{inviteeEmail\}\}/g, previewData.inviteeEmail)
    rendered = rendered.replace(/\{\{role\}\}/g, previewData.role)
    rendered = rendered.replace(/\{\{invitationUrl\}\}/g, previewData.invitationUrl)
    rendered = rendered.replace(/\{\{expirationDate\}\}/g, previewData.expirationDate)
    rendered = rendered.replace(/\{\{customMessage\}\}/g, previewData.customMessage)

    // Handle conditional blocks for customMessage
    const customMessageRegex = /\{\{#if customMessage\}\}([\s\S]*?)\{\{\/if\}\}/g
    if (previewData.customMessage.trim()) {
      rendered = rendered.replace(customMessageRegex, '$1')
    } else {
      rendered = rendered.replace(customMessageRegex, '')
    }

    return rendered
  }

  const handlePreviewDataChange = (field: keyof PreviewData, value: string) => {
    setPreviewData(prev => ({
      ...prev,
      [field]: value,
    }))
  }

  if (!template) return null

  const renderedSubject = renderTemplate(template.subject)
  const renderedContent = renderTemplate(template.content)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Preview Template: {template.name}
          </DialogTitle>
          <DialogDescription>
            See how your template will look with sample data
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Preview Controls */}
          <div className="space-y-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Preview Data
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <Label htmlFor="org-name" className="text-xs">Organization Name</Label>
                  <Input
                    id="org-name"
                    className="h-8"
                    value={previewData.organizationName}
                    onChange={(e) => handlePreviewDataChange('organizationName', e.target.value)}
                  />
                </div>

                <div>
                  <Label htmlFor="inviter-name" className="text-xs">Inviter Name</Label>
                  <Input
                    id="inviter-name"
                    className="h-8"
                    value={previewData.inviterName}
                    onChange={(e) => handlePreviewDataChange('inviterName', e.target.value)}
                  />
                </div>

                <div>
                  <Label htmlFor="inviter-email" className="text-xs">Inviter Email</Label>
                  <Input
                    id="inviter-email"
                    className="h-8"
                    value={previewData.inviterEmail}
                    onChange={(e) => handlePreviewDataChange('inviterEmail', e.target.value)}
                  />
                </div>

                <div>
                  <Label htmlFor="invitee-email" className="text-xs">Invitee Email</Label>
                  <Input
                    id="invitee-email"
                    className="h-8"
                    value={previewData.inviteeEmail}
                    onChange={(e) => handlePreviewDataChange('inviteeEmail', e.target.value)}
                  />
                </div>

                <div>
                  <Label htmlFor="role" className="text-xs">Role</Label>
                  <Select value={previewData.role} onValueChange={(value) => handlePreviewDataChange('role', value)}>
                    <SelectTrigger className="h-8">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {SAMPLE_ROLES.map(role => (
                        <SelectItem key={role} value={role}>{role}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="custom-message" className="text-xs">Custom Message</Label>
                  <Input
                    id="custom-message"
                    className="h-8"
                    value={previewData.customMessage}
                    onChange={(e) => handlePreviewDataChange('customMessage', e.target.value)}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Template Info</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-xs">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Default:</span>
                  {template.isDefault ? (
                    <Badge variant="default">Yes</Badge>
                  ) : (
                    <Badge variant="outline">No</Badge>
                  )}
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Usage:</span>
                  <Badge variant="outline">
                    {template._count?.invitations || 0} times
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Created:</span>
                  <span>{new Date(template.createdAt).toLocaleDateString()}</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Email Preview */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  Email Preview
                </CardTitle>
              </CardHeader>
              <CardContent>
                {/* Email Header */}
                <div className="border rounded-lg p-4 bg-gray-50 mb-4">
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-gray-600">From:</span>
                      <span>{previewData.inviterName} &lt;{previewData.inviterEmail}&gt;</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-gray-600">To:</span>
                      <span>{previewData.inviteeEmail}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-gray-600">Subject:</span>
                      <span className="font-medium">{renderedSubject}</span>
                    </div>
                  </div>
                </div>

                {/* Email Body */}
                <div className="border rounded-lg p-6 bg-white min-h-[400px]">
                  <div className="prose prose-sm max-w-none">
                    <pre className="whitespace-pre-wrap font-sans text-sm leading-relaxed">
                      {renderedContent}
                    </pre>
                  </div>
                </div>

                {/* Email Footer */}
                <div className="mt-4 p-3 bg-gray-50 rounded-lg text-xs text-gray-600">
                  <div className="flex items-center gap-2 mb-2">
                    <Calendar className="h-3 w-3" />
                    <span>This invitation expires on {previewData.expirationDate}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Link className="h-3 w-3" />
                    <span>Invitation URL: {previewData.invitationUrl}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
