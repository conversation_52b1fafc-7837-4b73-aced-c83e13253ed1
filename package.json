{"name": "koolek-standalone", "version": "1.0.0", "description": "Multi-Tenant KooLek SaaS Platform", "private": true, "scripts": {"dev": "next dev -p 3002 --turbo", "build": "next build", "start": "next start -p 3002", "lint": "next lint", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:studio": "prisma studio", "db:migrate": "prisma migrate dev", "db:reset": "prisma migrate reset", "db:seed": "tsx prisma/seed.ts", "docker:start": "./scripts/docker-dev.sh start", "docker:start-tools": "./scripts/docker-dev.sh start-with-tools", "docker:stop": "./scripts/docker-dev.sh stop", "docker:restart": "./scripts/docker-dev.sh restart", "docker:logs": "./scripts/docker-dev.sh logs", "docker:status": "./scripts/docker-dev.sh status", "docker:reset-db": "./scripts/docker-dev.sh reset-db", "docker:backup-db": "./scripts/docker-dev.sh backup-db", "docker:health": "./scripts/health-check.sh", "dev:setup": "./scripts/docker-dev.sh start && npm run db:push && npm run db:seed"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "^5.0.1", "@mui/icons-material": "^5.14.19", "@mui/material": "^5.14.20", "@mui/x-date-pickers": "^6.18.2", "@prisma/client": "^5.7.1", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@tanstack/react-query": "^4.36.1", "@trpc/client": "^10.45.0", "@trpc/next": "^10.45.0", "@trpc/react-query": "^10.45.0", "@trpc/server": "^10.45.0", "@types/bcryptjs": "^2.4.6", "babel-plugin-react-compiler": "^19.1.0-rc.2", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^4.1.0", "dayjs": "^1.11.10", "formik": "^2.4.5", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.294.0", "next": "^15.0.3", "next-auth": "^5.0.0-beta.28", "nodemailer": "^6.9.7", "notistack": "^3.0.1", "papaparse": "^5.5.3", "react": "19.0.0-rc-02c0e824-20241028", "react-dom": "19.0.0-rc-02c0e824-20241028", "react-hook-form": "^7.56.4", "resend": "^2.1.0", "superjson": "^2.2.1", "tailwind-merge": "^2.1.0", "tailwindcss-animate": "^1.0.7", "yup": "^1.3.3", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.5", "@types/node": "^20", "@types/nodemailer": "^6.4.14", "@types/papaparse": "^5.3.14", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "^15.0.3", "postcss": "^8", "prisma": "^5.7.1", "tailwindcss": "^3.3.0", "tsx": "^4.6.2", "typescript": "^5"}}