import { z } from 'zod'
import { TRPCError } from '@trpc/server'
import { createTRPCRouter, protectedProcedure, auditedProcedure } from '@/lib/trpc'

const createOrganizationSchema = z.object({
  name: z.string().min(1, 'Organization name is required').max(100),
  slug: z.string().min(1, 'Slug is required').max(50).regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens'),
  domain: z.string().optional(),
})

const updateOrganizationSchema = z.object({
  id: z.string(),
  name: z.string().min(1).max(100).optional(),
  slug: z.string().min(1).max(50).regex(/^[a-z0-9-]+$/).optional(),
  domain: z.string().optional(),
  settings: z.record(z.any()).optional(),
})

const inviteUserSchema = z.object({
  organizationId: z.string(),
  email: z.string().email(),
  role: z.enum(['OWNER', 'ADMIN', 'MEMBER', 'VIEWER']).default('MEMBER'),
})

export const organizationRouter = createTRPCRouter({
  // Test mutation to debug input issues
  test: protectedProcedure
    .input(z.object({ message: z.string() }))
    .mutation(async ({ input }) => {
      console.log('Test mutation called with input:', input)
      return { success: true, received: input }
    }),

  // Get user's organizations
  getUserOrganizations: protectedProcedure
    .query(async ({ ctx }) => {
      const userOrganizations = await ctx.db.userOrganization.findMany({
        where: { userId: ctx.session.user.id },
        include: {
          organization: {
            include: {
              _count: {
                select: {
                  members: true,
                  contributions: true,
                  expenses: true,
                }
              }
            }
          }
        },
        orderBy: { joinedAt: 'desc' }
      })

      return userOrganizations.map(uo => ({
        ...uo.organization,
        role: uo.role,
        joinedAt: uo.joinedAt,
        memberCount: uo.organization._count.members,
        contributionCount: uo.organization._count.contributions,
        expenseCount: uo.organization._count.expenses,
      }))
    }),

  // Get organization by ID
  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      // Check if user has access to this organization
      const userOrg = await ctx.db.userOrganization.findFirst({
        where: {
          userId: ctx.session.user.id,
          organizationId: input.id,
        },
        include: {
          organization: {
            include: {
              _count: {
                select: {
                  members: true,
                  contributions: true,
                  expenses: true,
                }
              }
            }
          }
        }
      })

      if (!userOrg) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Organization not found or access denied',
        })
      }

      return {
        ...userOrg.organization,
        role: userOrg.role,
        joinedAt: userOrg.joinedAt,
        memberCount: userOrg.organization._count.members,
        contributionCount: userOrg.organization._count.contributions,
        expenseCount: userOrg.organization._count.expenses,
      }
    }),

  // Get organization by slug
  getBySlug: protectedProcedure
    .input(z.object({ slug: z.string() }))
    .query(async ({ ctx, input }) => {
      // Check if user has access to this organization
      const userOrg = await ctx.db.userOrganization.findFirst({
        where: {
          userId: ctx.session.user.id,
          organization: {
            slug: input.slug,
          },
        },
        include: {
          organization: {
            include: {
              _count: {
                select: {
                  members: true,
                  contributions: true,
                  expenses: true,
                }
              }
            }
          }
        }
      })

      if (!userOrg) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Organization not found or access denied',
        })
      }

      return {
        ...userOrg.organization,
        role: userOrg.role,
        joinedAt: userOrg.joinedAt,
        memberCount: userOrg.organization._count.members,
        contributionCount: userOrg.organization._count.contributions,
        expenseCount: userOrg.organization._count.expenses,
      }
    }),

  // Create organization
  create: protectedProcedure
    .input(createOrganizationSchema)
    .mutation(async ({ ctx, input }) => {
      const { name, slug, domain } = input

      // Check if slug is already taken
      const existingOrg = await ctx.db.organization.findUnique({
        where: { slug },
      })

      if (existingOrg) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: 'Organization slug already exists',
        })
      }

      // Create organization and add user as owner
      const organization = await ctx.db.organization.create({
        data: {
          name,
          slug,
          domain,
          members: {
            create: {
              userId: ctx.session.user.id,
              role: 'OWNER',
            },
          },
        },
        include: {
          _count: {
            select: {
              members: true,
              contributions: true,
              expenses: true,
            }
          }
        }
      })

      return {
        ...organization,
        role: 'OWNER' as const,
        joinedAt: new Date(),
        memberCount: organization._count.members,
        contributionCount: organization._count.contributions,
        expenseCount: organization._count.expenses,
      }
    }),

  // Update organization
  update: auditedProcedure
    .input(updateOrganizationSchema)
    .mutation(async ({ ctx, input }) => {
      // Check if user has admin access to this organization
      const userOrg = await ctx.db.userOrganization.findFirst({
        where: {
          userId: ctx.session.user.id,
          organizationId: input.id,
          role: { in: ['OWNER', 'ADMIN'] },
        },
      })

      if (!userOrg) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Insufficient permissions to update organization',
        })
      }

      // If updating slug, check if it's available
      if (input.slug) {
        const existingOrg = await ctx.db.organization.findFirst({
          where: {
            slug: input.slug,
            id: { not: input.id },
          },
        })

        if (existingOrg) {
          throw new TRPCError({
            code: 'CONFLICT',
            message: 'Organization slug already exists',
          })
        }
      }

      const { id, ...updateData } = input
      const organization = await ctx.db.organization.update({
        where: { id },
        data: updateData,
        include: {
          _count: {
            select: {
              members: true,
              contributions: true,
              expenses: true,
            }
          }
        }
      })

      return {
        ...organization,
        role: userOrg.role,
        joinedAt: userOrg.joinedAt,
        memberCount: organization._count.members,
        contributionCount: organization._count.contributions,
        expenseCount: organization._count.expenses,
      }
    }),

  // Delete organization
  delete: auditedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Check if user is owner of this organization
      const userOrg = await ctx.db.userOrganization.findFirst({
        where: {
          userId: ctx.session.user.id,
          organizationId: input.id,
          role: 'OWNER',
        },
      })

      if (!userOrg) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Only organization owners can delete organizations',
        })
      }

      await ctx.db.organization.delete({
        where: { id: input.id },
      })

      return { success: true }
    }),

  // Get organization members
  getMembers: protectedProcedure
    .input(z.object({ organizationId: z.string() }))
    .query(async ({ ctx, input }) => {
      // Check if user has access to this organization
      const userOrg = await ctx.db.userOrganization.findFirst({
        where: {
          userId: ctx.session.user.id,
          organizationId: input.organizationId,
        },
      })

      if (!userOrg) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Organization not found or access denied',
        })
      }

      const members = await ctx.db.userOrganization.findMany({
        where: { organizationId: input.organizationId },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
              lastLoginAt: true,
            },
          },
        },
        orderBy: { joinedAt: 'asc' }
      })

      return members.map(member => ({
        id: member.id,
        role: member.role,
        joinedAt: member.joinedAt,
        user: member.user,
      }))
    }),

  // Invite user to organization
  inviteUser: auditedProcedure
    .input(inviteUserSchema)
    .mutation(async ({ ctx, input }) => {
      // Check if user has admin access to this organization
      const userOrg = await ctx.db.userOrganization.findFirst({
        where: {
          userId: ctx.session.user.id,
          organizationId: input.organizationId,
          role: { in: ['OWNER', 'ADMIN'] },
        },
      })

      if (!userOrg) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Insufficient permissions to invite users',
        })
      }

      // Check if user is already a member
      const existingMember = await ctx.db.userOrganization.findFirst({
        where: {
          organizationId: input.organizationId,
          user: { email: input.email },
        },
      })

      if (existingMember) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: 'User is already a member of this organization',
        })
      }

      // Check if there's already a pending invitation
      const existingInvitation = await ctx.db.invitation.findFirst({
        where: {
          email: input.email,
          organizationId: input.organizationId,
          status: 'PENDING',
        },
      })

      if (existingInvitation) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: 'Invitation already sent to this email',
        })
      }

      // Create invitation
      const invitation = await ctx.db.invitation.create({
        data: {
          email: input.email,
          organizationId: input.organizationId,
          role: input.role,
          token: crypto.randomUUID(),
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        },
        include: {
          organization: {
            select: {
              name: true,
              slug: true,
            },
          },
        },
      })

      // TODO: Send invitation email

      return invitation
    }),
})
