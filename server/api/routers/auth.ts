import { z } from 'zod'
import { TRPCError } from '@trpc/server'
import { hash, compare } from 'bcryptjs'
import { createTRPCRouter, publicProcedure, protectedProcedure } from '@/lib/trpc'
import { sendPasswordResetEmail, sendVerificationEmail } from '@/lib/email'

const signUpSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100),
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
})

const resetPasswordSchema = z.object({
  email: z.string().email('Invalid email address'),
})

const confirmResetSchema = z.object({
  token: z.string(),
  password: z.string().min(8, 'Password must be at least 8 characters'),
})

const changePasswordSchema = z.object({
  currentPassword: z.string(),
  newPassword: z.string().min(8, 'Password must be at least 8 characters'),
})

const updateProfileSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  bio: z.string().max(500).optional(),
  timezone: z.string().optional(),
  locale: z.string().optional(),
  preferences: z.record(z.any()).optional(),
})

export const authRouter = createTRPCRouter({
  // Sign up new user
  signUp: publicProcedure
    .input(signUpSchema)
    .mutation(async ({ ctx, input }) => {
      // Check if user already exists
      const existingUser = await ctx.db.user.findUnique({
        where: { email: input.email },
      })

      if (existingUser) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: 'User with this email already exists',
        })
      }

      // Hash password
      const hashedPassword = await hash(input.password, 12)

      // Create user
      const user = await ctx.db.user.create({
        data: {
          name: input.name,
          email: input.email,
          password: hashedPassword,
        },
      })

      // Send verification email
      try {
        await sendVerificationEmail(user.email, user.name || 'User')
      } catch (error) {
        console.error('Failed to send verification email:', error)
        // Don't fail the signup if email fails
      }

      return {
        success: true,
        message: 'Account created successfully. Please check your email for verification.',
      }
    }),

  // Request password reset
  requestPasswordReset: publicProcedure
    .input(resetPasswordSchema)
    .mutation(async ({ ctx, input }) => {
      const user = await ctx.db.user.findUnique({
        where: { email: input.email },
      })

      if (!user) {
        // Don't reveal if user exists for security
        return {
          success: true,
          message: 'If an account with that email exists, a password reset link has been sent.',
        }
      }

      // Invalidate existing reset tokens
      await ctx.db.passwordReset.updateMany({
        where: {
          userId: user.id,
          used: false,
        },
        data: {
          used: true,
        },
      })

      // Create new reset token
      const resetToken = crypto.randomUUID()
      const expiresAt = new Date(Date.now() + 60 * 60 * 1000) // 1 hour

      await ctx.db.passwordReset.create({
        data: {
          userId: user.id,
          token: resetToken,
          expires: expiresAt,
        },
      })

      // Send reset email
      try {
        await sendPasswordResetEmail(user.email, user.name || 'User', resetToken)
      } catch (error) {
        console.error('Failed to send password reset email:', error)
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to send password reset email',
        })
      }

      return {
        success: true,
        message: 'If an account with that email exists, a password reset link has been sent.',
      }
    }),

  // Confirm password reset
  confirmPasswordReset: publicProcedure
    .input(confirmResetSchema)
    .mutation(async ({ ctx, input }) => {
      const resetRequest = await ctx.db.passwordReset.findUnique({
        where: { token: input.token },
        include: { user: true },
      })

      if (!resetRequest || resetRequest.used || resetRequest.expires < new Date()) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Invalid or expired reset token',
        })
      }

      // Hash new password
      const hashedPassword = await hash(input.password, 12)

      // Update user password and mark token as used
      await ctx.db.$transaction([
        ctx.db.user.update({
          where: { id: resetRequest.userId },
          data: { password: hashedPassword },
        }),
        ctx.db.passwordReset.update({
          where: { id: resetRequest.id },
          data: { used: true },
        }),
      ])

      return {
        success: true,
        message: 'Password reset successfully',
      }
    }),

  // Change password (authenticated)
  changePassword: protectedProcedure
    .input(changePasswordSchema)
    .mutation(async ({ ctx, input }) => {
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
      })

      if (!user || !user.password) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'User not found or no password set',
        })
      }

      // Verify current password
      const isCurrentPasswordValid = await compare(input.currentPassword, user.password)
      if (!isCurrentPasswordValid) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Current password is incorrect',
        })
      }

      // Hash new password
      const hashedPassword = await hash(input.newPassword, 12)

      // Update password
      await ctx.db.user.update({
        where: { id: user.id },
        data: { password: hashedPassword },
      })

      return {
        success: true,
        message: 'Password changed successfully',
      }
    }),

  // Get current user profile
  getProfile: protectedProcedure
    .query(async ({ ctx }) => {
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
          bio: true,
          timezone: true,
          locale: true,
          preferences: true,
          emailVerified: true,
          twoFactorEnabled: true,
          createdAt: true,
          lastLoginAt: true,
        },
      })

      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        })
      }

      return user
    }),

  // Update user profile
  updateProfile: protectedProcedure
    .input(updateProfileSchema)
    .mutation(async ({ ctx, input }) => {
      const user = await ctx.db.user.update({
        where: { id: ctx.session.user.id },
        data: input,
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
          bio: true,
          timezone: true,
          locale: true,
          preferences: true,
        },
      })

      return user
    }),

  // Resend verification email
  resendVerification: protectedProcedure
    .mutation(async ({ ctx }) => {
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
      })

      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        })
      }

      if (user.emailVerified) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Email is already verified',
        })
      }

      try {
        await sendVerificationEmail(user.email, user.name || 'User')
      } catch (error) {
        console.error('Failed to send verification email:', error)
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to send verification email',
        })
      }

      return {
        success: true,
        message: 'Verification email sent',
      }
    }),

  // Get user's organizations with roles
  getUserOrganizations: protectedProcedure
    .query(async ({ ctx }) => {
      const userOrganizations = await ctx.db.userOrganization.findMany({
        where: { userId: ctx.session.user.id },
        include: {
          organization: {
            include: {
              _count: {
                select: {
                  members: true,
                  contributions: true,
                  expenses: true,
                },
              },
            },
          },
        },
        orderBy: { joinedAt: 'desc' },
      })

      return userOrganizations.map(uo => ({
        ...uo.organization,
        role: uo.role,
        joinedAt: uo.joinedAt,
        memberCount: uo.organization._count.members,
        contributionCount: uo.organization._count.contributions,
        expenseCount: uo.organization._count.expenses,
      }))
    }),
})
