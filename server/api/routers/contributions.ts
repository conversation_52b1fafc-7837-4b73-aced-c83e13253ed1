import { z } from 'zod'
import { TRPCError } from '@trpc/server'
import { createTRPCRouter, protectedProcedure, createPermissionProcedure, validateOrganizationAccess } from '@/lib/trpc'
import { PERMISSIONS } from '@/lib/permissions'

// Validation schemas
const createContributionSchema = z.object({
  organizationId: z.string(),
  reference: z.string().min(1, 'Reference is required'),
  amount: z.number().positive('Amount must be positive'),
  category: z.string().min(1, 'Category is required'),
  description: z.string().optional(),
  year: z.number().int().min(2020).max(2030).optional(),
})

const updateContributionSchema = z.object({
  contributionId: z.string(),
  reference: z.string().min(1).optional(),
  amount: z.number().positive().optional(),
  category: z.string().min(1).optional(),
  description: z.string().optional(),
  status: z.enum(['PENDING', 'VERIFIED', 'REJECTED']).optional(),
})

const getContributionsSchema = z.object({
  organizationId: z.string(),
  year: z.number().int().optional(),
  status: z.enum(['PENDING', 'VERIFIED', 'REJECTED']).optional(),
  category: z.string().optional(),
  limit: z.number().min(1).max(100).default(50),
  offset: z.number().min(0).default(0),
})

const deleteContributionSchema = z.object({
  contributionId: z.string(),
})

export const contributionsRouter = createTRPCRouter({
  // Get all contributions for an organization
  getAll: protectedProcedure
    .input(getContributionsSchema)
    .query(async ({ ctx, input }) => {
      const { organizationId, year, status, category, limit, offset } = input

      // Validate organization access and permissions
      await validateOrganizationAccess(ctx, organizationId, PERMISSIONS.CONTRIBUTIONS.READ)

      // Build where clause
      const where: any = {
        organizationId,
      }

      if (year) where.year = year
      if (status) where.status = status
      if (category) where.category = category

      const [contributions, total] = await Promise.all([
        ctx.db.contribution.findMany({
          where,
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
          take: limit,
          skip: offset,
        }),
        ctx.db.contribution.count({ where }),
      ])

      return {
        contributions,
        total,
        hasMore: offset + limit < total,
      }
    }),

  // Get contribution by ID
  getById: createPermissionProcedure(PERMISSIONS.CONTRIBUTIONS.READ)
    .input(z.object({ contributionId: z.string() }))
    .query(async ({ ctx, input }) => {
      const contribution = await ctx.db.contribution.findFirst({
        where: {
          id: input.contributionId,
          organizationId: ctx.organizationId,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      })

      if (!contribution) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Contribution not found',
        })
      }

      return contribution
    }),

  // Create new contribution
  create: createPermissionProcedure(PERMISSIONS.CONTRIBUTIONS.CREATE)
    .input(createContributionSchema)
    .mutation(async ({ ctx, input }) => {
      const { organizationId, reference, amount, category, description, year } = input

      // Verify organization access
      if (organizationId !== ctx.organizationId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied to this organization',
        })
      }

      const contribution = await ctx.db.contribution.create({
        data: {
          organizationId,
          userId: ctx.session.user.id,
          reference,
          amount,
          category,
          description,
          year: year || new Date().getFullYear(),
          status: 'PENDING',
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      })

      return contribution
    }),

  // Update contribution
  update: createPermissionProcedure(PERMISSIONS.CONTRIBUTIONS.UPDATE)
    .input(updateContributionSchema)
    .mutation(async ({ ctx, input }) => {
      const { contributionId, ...updateData } = input

      // Check if contribution exists and user has access
      const existingContribution = await ctx.db.contribution.findFirst({
        where: {
          id: contributionId,
          organizationId: ctx.organizationId,
        },
      })

      if (!existingContribution) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Contribution not found',
        })
      }

      // Only allow users to update their own contributions unless they're admin/owner
      if (
        existingContribution.userId !== ctx.session.user.id &&
        !['OWNER', 'ADMIN'].includes(ctx.userRole)
      ) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'You can only update your own contributions',
        })
      }

      const contribution = await ctx.db.contribution.update({
        where: { id: contributionId },
        data: updateData,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      })

      return contribution
    }),

  // Delete contribution
  delete: createPermissionProcedure(PERMISSIONS.CONTRIBUTIONS.DELETE)
    .input(deleteContributionSchema)
    .mutation(async ({ ctx, input }) => {
      const { contributionId } = input

      // Check if contribution exists and user has access
      const existingContribution = await ctx.db.contribution.findFirst({
        where: {
          id: contributionId,
          organizationId: ctx.organizationId,
        },
      })

      if (!existingContribution) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Contribution not found',
        })
      }

      // Only allow users to delete their own contributions unless they're admin/owner
      if (
        existingContribution.userId !== ctx.session.user.id &&
        !['OWNER', 'ADMIN'].includes(ctx.userRole)
      ) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'You can only delete your own contributions',
        })
      }

      await ctx.db.contribution.delete({
        where: { id: contributionId },
      })

      return { success: true }
    }),

  // Get contribution statistics
  getStats: createPermissionProcedure(PERMISSIONS.CONTRIBUTIONS.READ)
    .input(z.object({
      organizationId: z.string(),
      year: z.number().int().optional(),
    }))
    .query(async ({ ctx, input }) => {
      const { organizationId, year } = input

      const where: any = { organizationId }
      if (year) where.year = year

      const [total, pending, verified, rejected, totalAmount] = await Promise.all([
        ctx.db.contribution.count({ where }),
        ctx.db.contribution.count({ where: { ...where, status: 'PENDING' } }),
        ctx.db.contribution.count({ where: { ...where, status: 'VERIFIED' } }),
        ctx.db.contribution.count({ where: { ...where, status: 'REJECTED' } }),
        ctx.db.contribution.aggregate({
          where: { ...where, status: 'VERIFIED' },
          _sum: { amount: true },
        }),
      ])

      return {
        total,
        pending,
        verified,
        rejected,
        totalAmount: totalAmount._sum.amount || 0,
      }
    }),
})
