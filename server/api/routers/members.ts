import { z } from 'zod'
import { TRPCError } from '@trpc/server'
import { createTRPCRouter, createAuditedPermissionProcedure } from '@/lib/trpc'
import { PERMISSIONS } from '@/lib/permissions'
import { OrganizationRole } from '@prisma/client'

const updateMemberRoleSchema = z.object({
  organizationId: z.string(),
  userId: z.string(),
  role: z.enum(['OWNER', 'ADMIN', 'MEMBER', 'VIEWER']),
})

const removeMemberSchema = z.object({
  organizationId: z.string(),
  userId: z.string(),
})

const transferOwnershipSchema = z.object({
  organizationId: z.string(),
  newOwnerId: z.string(),
})

export const membersRouter = createTRPCRouter({
  // Get organization members with detailed information
  getMembers: createAuditedPermissionProcedure(PERMISSIONS.MEMBERS_READ)
    .input(z.object({ organizationId: z.string() }))
    .query(async ({ ctx, input }) => {
      const members = await ctx.db.userOrganization.findMany({
        where: { organizationId: input.organizationId },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
              lastLoginAt: true,
              createdAt: true,
            },
          },
        },
        orderBy: [
          { role: 'asc' }, // Owners first, then admins, etc.
          { joinedAt: 'asc' },
        ],
      })

      return members.map(member => ({
        id: member.id,
        role: member.role,
        joinedAt: member.joinedAt,
        user: member.user,
        canEdit: member.role !== 'OWNER' || ctx.userRole === 'OWNER',
        canRemove: member.role !== 'OWNER' && member.userId !== ctx.session.user.id,
      }))
    }),

  // Update member role with comprehensive validation
  updateRole: createAuditedPermissionProcedure(PERMISSIONS.MEMBERS_UPDATE)
    .input(updateMemberRoleSchema)
    .mutation(async ({ ctx, input }) => {
      // Prevent self-role changes for non-owners
      if (input.userId === ctx.session.user.id && ctx.userRole !== 'OWNER') {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Cannot change your own role',
        })
      }

      // Get current member info
      const currentMember = await ctx.db.userOrganization.findFirst({
        where: {
          userId: input.userId,
          organizationId: input.organizationId,
        },
        include: {
          user: {
            select: { name: true, email: true },
          },
        },
      })

      if (!currentMember) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Member not found',
        })
      }

      // Prevent changing owner role unless you are the owner
      if (currentMember.role === 'OWNER' && ctx.userRole !== 'OWNER') {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Only owners can change owner roles',
        })
      }

      // Prevent setting owner role unless you are the current owner
      if (input.role === 'OWNER' && ctx.userRole !== 'OWNER') {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Only owners can assign owner role',
        })
      }

      // Update the member role
      const updatedMember = await ctx.db.userOrganization.update({
        where: { id: currentMember.id },
        data: { role: input.role },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      })

      return {
        id: updatedMember.id,
        role: updatedMember.role,
        joinedAt: updatedMember.joinedAt,
        user: updatedMember.user,
        previousRole: currentMember.role,
      }
    }),

  // Remove member from organization
  removeMember: createAuditedPermissionProcedure(PERMISSIONS.MEMBERS_DELETE)
    .input(removeMemberSchema)
    .mutation(async ({ ctx, input }) => {
      // Prevent self-removal
      if (input.userId === ctx.session.user.id) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Cannot remove yourself from the organization',
        })
      }

      // Get member info
      const member = await ctx.db.userOrganization.findFirst({
        where: {
          userId: input.userId,
          organizationId: input.organizationId,
        },
        include: {
          user: {
            select: { name: true, email: true },
          },
        },
      })

      if (!member) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Member not found',
        })
      }

      // Prevent removing owners unless you are an owner
      if (member.role === 'OWNER' && ctx.userRole !== 'OWNER') {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Only owners can remove other owners',
        })
      }

      // Check if this is the last owner
      if (member.role === 'OWNER') {
        const ownerCount = await ctx.db.userOrganization.count({
          where: {
            organizationId: input.organizationId,
            role: 'OWNER',
          },
        })

        if (ownerCount <= 1) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'Cannot remove the last owner. Transfer ownership first.',
          })
        }
      }

      // Remove the member
      await ctx.db.userOrganization.delete({
        where: { id: member.id },
      })

      return {
        success: true,
        removedMember: {
          name: member.user.name,
          email: member.user.email,
          role: member.role,
        },
      }
    }),

  // Transfer ownership to another member
  transferOwnership: createAuditedPermissionProcedure(PERMISSIONS.ORGANIZATION_TRANSFER)
    .input(transferOwnershipSchema)
    .mutation(async ({ ctx, input }) => {
      // Only current owners can transfer ownership
      if (ctx.userRole !== 'OWNER') {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Only owners can transfer ownership',
        })
      }

      // Get the target member
      const targetMember = await ctx.db.userOrganization.findFirst({
        where: {
          userId: input.newOwnerId,
          organizationId: input.organizationId,
        },
        include: {
          user: {
            select: { name: true, email: true },
          },
        },
      })

      if (!targetMember) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Target member not found',
        })
      }

      // Perform ownership transfer in transaction
      const result = await ctx.db.$transaction([
        // Demote current owner to admin
        ctx.db.userOrganization.update({
          where: {
            userId_organizationId: {
              userId: ctx.session.user.id,
              organizationId: input.organizationId,
            },
          },
          data: { role: 'ADMIN' },
        }),
        // Promote target member to owner
        ctx.db.userOrganization.update({
          where: { id: targetMember.id },
          data: { role: 'OWNER' },
        }),
      ])

      return {
        success: true,
        newOwner: {
          name: targetMember.user.name,
          email: targetMember.user.email,
        },
        previousOwner: {
          id: ctx.session.user.id,
          newRole: 'ADMIN',
        },
      }
    }),

  // Get member activity and statistics
  getMemberStats: createAuditedPermissionProcedure(PERMISSIONS.MEMBERS_READ)
    .input(z.object({ organizationId: z.string() }))
    .query(async ({ ctx, input }) => {
      const [totalMembers, recentActivity, roleDistribution] = await Promise.all([
        // Total member count
        ctx.db.userOrganization.count({
          where: { organizationId: input.organizationId },
        }),
        
        // Recent member activity (last 30 days)
        ctx.db.auditLog.findMany({
          where: {
            organizationId: input.organizationId,
            action: { in: ['MUTATION_MEMBERS_UPDATEROLE', 'MUTATION_MEMBERS_REMOVEMEMBER', 'MUTATION_INVITATIONS_INVITE'] },
            createdAt: { gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) },
          },
          include: {
            user: {
              select: { name: true, email: true },
            },
          },
          orderBy: { createdAt: 'desc' },
          take: 10,
        }),

        // Role distribution
        ctx.db.userOrganization.groupBy({
          by: ['role'],
          where: { organizationId: input.organizationId },
          _count: { role: true },
        }),
      ])

      return {
        totalMembers,
        recentActivity,
        roleDistribution: roleDistribution.map(r => ({
          role: r.role,
          count: r._count.role,
        })),
      }
    }),
})
