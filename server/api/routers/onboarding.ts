import { z } from 'zod'
import { TRPCError } from '@trpc/server'
import { createTRPCRouter, protectedProcedure } from '@/lib/trpc'

const updateOnboardingStepSchema = z.object({
  organizationId: z.string(),
  step: z.string(),
  completed: z.boolean(),
  data: z.record(z.any()).default({}),
})

const getOnboardingProgressSchema = z.object({
  organizationId: z.string(),
})

// Define onboarding steps and their order
const ONBOARDING_STEPS = [
  'welcome',
  'profile_setup',
  'organization_overview',
  'team_introduction',
  'first_contribution',
  'first_expense',
  'dashboard_tour',
  'completed',
] as const

export const onboardingRouter = createTRPCRouter({
  // Get user's onboarding progress for an organization
  getProgress: protectedProcedure
    .input(getOnboardingProgressSchema)
    .query(async ({ ctx, input }) => {
      // Check if user has access to this organization
      const userOrg = await ctx.db.userOrganization.findFirst({
        where: {
          userId: ctx.session.user.id,
          organizationId: input.organizationId,
        },
      })

      if (!userOrg) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied',
        })
      }

      const onboardingSteps = await ctx.db.userOnboarding.findMany({
        where: {
          userId: ctx.session.user.id,
          organizationId: input.organizationId,
        },
        orderBy: { createdAt: 'asc' },
      })

      // Create a map of completed steps
      const completedSteps = new Map(
        onboardingSteps.map(step => [step.step, step])
      )

      // Build progress with all steps
      const progress = ONBOARDING_STEPS.map((stepName, index) => {
        const stepData = completedSteps.get(stepName)
        return {
          step: stepName,
          order: index,
          completed: stepData?.completed || false,
          data: stepData?.data || {},
          completedAt: stepData?.completedAt,
          createdAt: stepData?.createdAt,
        }
      })

      // Calculate overall progress
      const completedCount = progress.filter(step => step.completed).length
      const totalSteps = ONBOARDING_STEPS.length
      const progressPercentage = Math.round((completedCount / totalSteps) * 100)

      // Find current step (first incomplete step)
      const currentStep = progress.find(step => !step.completed) || progress[progress.length - 1]

      return {
        steps: progress,
        currentStep: currentStep.step,
        completedCount,
        totalSteps,
        progressPercentage,
        isCompleted: completedCount === totalSteps,
      }
    }),

  // Update onboarding step
  updateStep: protectedProcedure
    .input(updateOnboardingStepSchema)
    .mutation(async ({ ctx, input }) => {
      // Check if user has access to this organization
      const userOrg = await ctx.db.userOrganization.findFirst({
        where: {
          userId: ctx.session.user.id,
          organizationId: input.organizationId,
        },
      })

      if (!userOrg) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied',
        })
      }

      // Validate step name
      if (!ONBOARDING_STEPS.includes(input.step as any)) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Invalid onboarding step',
        })
      }

      const onboardingStep = await ctx.db.userOnboarding.upsert({
        where: {
          userId_organizationId_step: {
            userId: ctx.session.user.id,
            organizationId: input.organizationId,
            step: input.step,
          },
        },
        update: {
          completed: input.completed,
          data: input.data,
          completedAt: input.completed ? new Date() : null,
        },
        create: {
          userId: ctx.session.user.id,
          organizationId: input.organizationId,
          step: input.step,
          completed: input.completed,
          data: input.data,
          completedAt: input.completed ? new Date() : null,
        },
      })

      return onboardingStep
    }),

  // Mark step as completed
  completeStep: protectedProcedure
    .input(z.object({
      organizationId: z.string(),
      step: z.string(),
      data: z.record(z.any()).default({}),
    }))
    .mutation(async ({ ctx, input }) => {
      return ctx.db.userOnboarding.upsert({
        where: {
          userId_organizationId_step: {
            userId: ctx.session.user.id,
            organizationId: input.organizationId,
            step: input.step,
          },
        },
        update: {
          completed: true,
          data: input.data,
          completedAt: new Date(),
        },
        create: {
          userId: ctx.session.user.id,
          organizationId: input.organizationId,
          step: input.step,
          completed: true,
          data: input.data,
          completedAt: new Date(),
        },
      })
    }),

  // Reset onboarding progress
  reset: protectedProcedure
    .input(getOnboardingProgressSchema)
    .mutation(async ({ ctx, input }) => {
      // Check if user has access to this organization
      const userOrg = await ctx.db.userOrganization.findFirst({
        where: {
          userId: ctx.session.user.id,
          organizationId: input.organizationId,
        },
      })

      if (!userOrg) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied',
        })
      }

      await ctx.db.userOnboarding.deleteMany({
        where: {
          userId: ctx.session.user.id,
          organizationId: input.organizationId,
        },
      })

      return { success: true }
    }),

  // Skip onboarding (mark all steps as completed)
  skip: protectedProcedure
    .input(getOnboardingProgressSchema)
    .mutation(async ({ ctx, input }) => {
      // Check if user has access to this organization
      const userOrg = await ctx.db.userOrganization.findFirst({
        where: {
          userId: ctx.session.user.id,
          organizationId: input.organizationId,
        },
      })

      if (!userOrg) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied',
        })
      }

      // Create all onboarding steps as completed
      const onboardingSteps = ONBOARDING_STEPS.map((step, index) => ({
        userId: ctx.session.user.id,
        organizationId: input.organizationId,
        step,
        completed: true,
        data: {},
        completedAt: new Date(),
      }))

      // Delete existing steps and create new ones
      await ctx.db.userOnboarding.deleteMany({
        where: {
          userId: ctx.session.user.id,
          organizationId: input.organizationId,
        },
      })

      await ctx.db.userOnboarding.createMany({
        data: onboardingSteps,
      })

      return { success: true }
    }),

  // Get onboarding statistics for organization (admin only)
  getOrganizationStats: protectedProcedure
    .input(z.object({ organizationId: z.string() }))
    .query(async ({ ctx, input }) => {
      // Check if user has admin access to this organization
      const userOrg = await ctx.db.userOrganization.findFirst({
        where: {
          userId: ctx.session.user.id,
          organizationId: input.organizationId,
          role: { in: ['OWNER', 'ADMIN'] },
        },
      })

      if (!userOrg) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Admin access required',
        })
      }

      // Get all members and their onboarding progress
      const members = await ctx.db.userOrganization.findMany({
        where: { organizationId: input.organizationId },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      })

      const onboardingData = await ctx.db.userOnboarding.findMany({
        where: { organizationId: input.organizationId },
      })

      // Calculate stats
      const totalMembers = members.length
      const membersWithProgress = new Set(onboardingData.map(o => o.userId)).size
      const completedMembers = members.filter(member => {
        const memberSteps = onboardingData.filter(o => o.userId === member.userId)
        const completedSteps = memberSteps.filter(o => o.completed).length
        return completedSteps === ONBOARDING_STEPS.length
      }).length

      const averageProgress = totalMembers > 0
        ? Math.round((onboardingData.filter(o => o.completed).length / (totalMembers * ONBOARDING_STEPS.length)) * 100)
        : 0

      return {
        totalMembers,
        membersWithProgress,
        completedMembers,
        averageProgress,
        totalSteps: ONBOARDING_STEPS.length,
      }
    }),
})
