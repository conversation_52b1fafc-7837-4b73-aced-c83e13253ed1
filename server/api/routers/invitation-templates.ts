import { z } from 'zod'
import { TRPCError } from '@trpc/server'
import { createTRPCRouter, protectedProcedure, createPermissionProcedure } from '@/lib/trpc'
import { PERMISSIONS } from '@/lib/permissions'

const createTemplateSchema = z.object({
  organizationId: z.string(),
  name: z.string().min(1).max(100),
  subject: z.string().min(1).max(200),
  content: z.string().min(1),
  isDefault: z.boolean().default(false),
  metadata: z.record(z.any()).default({}),
})

const updateTemplateSchema = z.object({
  templateId: z.string(),
  name: z.string().min(1).max(100).optional(),
  subject: z.string().min(1).max(200).optional(),
  content: z.string().min(1).optional(),
  isDefault: z.boolean().optional(),
  metadata: z.record(z.any()).optional(),
})

export const invitationTemplatesRouter = createTRPCRouter({
  // Get organization invitation templates
  getOrganizationTemplates: createPermissionProcedure(PERMISSIONS.MEMBERS_READ)
    .input(z.object({ organizationId: z.string() }))
    .query(async ({ ctx, input }) => {
      const templates = await ctx.db.invitationTemplate.findMany({
        where: { organizationId: input.organizationId },
        include: {
          _count: {
            select: {
              invitations: true,
            },
          },
        },
        orderBy: [
          { isDefault: 'desc' },
          { name: 'asc' },
        ],
      })

      return templates
    }),

  // Get template by ID
  getById: createPermissionProcedure(PERMISSIONS.MEMBERS_READ)
    .input(z.object({ templateId: z.string() }))
    .query(async ({ ctx, input }) => {
      const template = await ctx.db.invitationTemplate.findUnique({
        where: { id: input.templateId },
        include: {
          organization: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          _count: {
            select: {
              invitations: true,
            },
          },
        },
      })

      if (!template) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Template not found',
        })
      }

      // Check if user has access to this organization
      const userOrg = await ctx.db.userOrganization.findFirst({
        where: {
          userId: ctx.session.user.id,
          organizationId: template.organizationId,
        },
      })

      if (!userOrg) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied',
        })
      }

      return template
    }),

  // Create invitation template
  create: createPermissionProcedure(PERMISSIONS.MEMBERS_INVITE)
    .input(createTemplateSchema)
    .mutation(async ({ ctx, input }) => {
      // Check if name already exists in organization
      const existingTemplate = await ctx.db.invitationTemplate.findFirst({
        where: {
          organizationId: input.organizationId,
          name: input.name,
        },
      })

      if (existingTemplate) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: 'Template with this name already exists',
        })
      }

      // If setting as default, unset other defaults
      if (input.isDefault) {
        await ctx.db.invitationTemplate.updateMany({
          where: {
            organizationId: input.organizationId,
            isDefault: true,
          },
          data: { isDefault: false },
        })
      }

      const template = await ctx.db.invitationTemplate.create({
        data: input,
      })

      return template
    }),

  // Update invitation template
  update: createPermissionProcedure(PERMISSIONS.MEMBERS_INVITE)
    .input(updateTemplateSchema)
    .mutation(async ({ ctx, input }) => {
      const { templateId, ...updateData } = input

      const template = await ctx.db.invitationTemplate.findUnique({
        where: { id: templateId },
      })

      if (!template) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Template not found',
        })
      }

      // Check if user has access to this organization
      const userOrg = await ctx.db.userOrganization.findFirst({
        where: {
          userId: ctx.session.user.id,
          organizationId: template.organizationId,
        },
      })

      if (!userOrg) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied',
        })
      }

      // Check if name already exists (if updating name)
      if (updateData.name && updateData.name !== template.name) {
        const existingTemplate = await ctx.db.invitationTemplate.findFirst({
          where: {
            organizationId: template.organizationId,
            name: updateData.name,
            id: { not: templateId },
          },
        })

        if (existingTemplate) {
          throw new TRPCError({
            code: 'CONFLICT',
            message: 'Template with this name already exists',
          })
        }
      }

      // If setting as default, unset other defaults
      if (updateData.isDefault) {
        await ctx.db.invitationTemplate.updateMany({
          where: {
            organizationId: template.organizationId,
            isDefault: true,
            id: { not: templateId },
          },
          data: { isDefault: false },
        })
      }

      const updatedTemplate = await ctx.db.invitationTemplate.update({
        where: { id: templateId },
        data: updateData,
      })

      return updatedTemplate
    }),

  // Delete invitation template
  delete: createPermissionProcedure(PERMISSIONS.MEMBERS_INVITE)
    .input(z.object({ templateId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const template = await ctx.db.invitationTemplate.findUnique({
        where: { id: input.templateId },
        include: {
          _count: {
            select: {
              invitations: true,
            },
          },
        },
      })

      if (!template) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Template not found',
        })
      }

      // Check if user has access to this organization
      const userOrg = await ctx.db.userOrganization.findFirst({
        where: {
          userId: ctx.session.user.id,
          organizationId: template.organizationId,
        },
      })

      if (!userOrg) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied',
        })
      }

      // Check if template is in use
      if (template._count.invitations > 0) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Cannot delete template that is in use by invitations',
        })
      }

      await ctx.db.invitationTemplate.delete({
        where: { id: input.templateId },
      })

      return { success: true }
    }),

  // Duplicate invitation template
  duplicate: createPermissionProcedure(PERMISSIONS.MEMBERS_INVITE)
    .input(z.object({
      templateId: z.string(),
      name: z.string().min(1).max(100),
    }))
    .mutation(async ({ ctx, input }) => {
      const originalTemplate = await ctx.db.invitationTemplate.findUnique({
        where: { id: input.templateId },
      })

      if (!originalTemplate) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Template not found',
        })
      }

      // Check if user has access to this organization
      const userOrg = await ctx.db.userOrganization.findFirst({
        where: {
          userId: ctx.session.user.id,
          organizationId: originalTemplate.organizationId,
        },
      })

      if (!userOrg) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied',
        })
      }

      // Check if name already exists
      const existingTemplate = await ctx.db.invitationTemplate.findFirst({
        where: {
          organizationId: originalTemplate.organizationId,
          name: input.name,
        },
      })

      if (existingTemplate) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: 'Template with this name already exists',
        })
      }

      const duplicatedTemplate = await ctx.db.invitationTemplate.create({
        data: {
          organizationId: originalTemplate.organizationId,
          name: input.name,
          subject: originalTemplate.subject,
          content: originalTemplate.content,
          isDefault: false, // Duplicated templates are never default
          metadata: originalTemplate.metadata,
        },
      })

      return duplicatedTemplate
    }),
})
