import { z } from 'zod'
import { TRPCError } from '@trpc/server'
import { createTRPCRouter, publicProcedure, protectedProcedure, createPermissionProcedure, auditedProcedure, createAuditedPermissionProcedure } from '@/lib/trpc'
import { PERMISSIONS } from '@/lib/permissions'
import { sendInvitationEmail } from '@/lib/email'

const acceptInvitationSchema = z.object({
  token: z.string(),
})

const inviteUserSchema = z.object({
  organizationId: z.string(),
  email: z.string().email(),
  role: z.enum(['OWNER', 'ADMIN', 'MEMBER', 'VIEWER']).default('MEMBER'),
})

const updateInvitationSchema = z.object({
  invitationId: z.string(),
  role: z.enum(['OWNER', 'ADMIN', 'MEMBER', 'VIEWER']).optional(),
  status: z.enum(['PENDING', 'ACCEPTED', 'EXPIRED', 'REVOKED']).optional(),
})

const bulkInviteSchema = z.object({
  organizationId: z.string(),
  invitations: z.array(z.object({
    email: z.string().email(),
    role: z.enum(['OWNER', 'ADMIN', 'MEMBER', 'VIEWER']).default('MEMBER'),
    message: z.string().optional(),
  })),
  templateId: z.string().optional(),
})

const invitationFiltersSchema = z.object({
  organizationId: z.string(),
  status: z.enum(['PENDING', 'ACCEPTED', 'EXPIRED', 'REVOKED']).optional(),
  role: z.enum(['OWNER', 'ADMIN', 'MEMBER', 'VIEWER']).optional(),
  search: z.string().optional(),
  batchId: z.string().optional(),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0),
})

export const invitationsRouter = createTRPCRouter({
  // Get invitation details (public - for invitation acceptance page)
  getByToken: publicProcedure
    .input(z.object({ token: z.string() }))
    .query(async ({ ctx, input }) => {
      const invitation = await ctx.db.invitation.findUnique({
        where: { token: input.token },
        include: {
          organization: {
            select: {
              id: true,
              name: true,
              slug: true,
              domain: true,
            },
          },
        },
      })

      if (!invitation) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Invitation not found',
        })
      }

      if (invitation.status !== 'PENDING') {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Invitation is no longer valid',
        })
      }

      if (invitation.expiresAt < new Date()) {
        // Mark as expired
        await ctx.db.invitation.update({
          where: { id: invitation.id },
          data: { status: 'EXPIRED' },
        })

        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Invitation has expired',
        })
      }

      return {
        id: invitation.id,
        email: invitation.email,
        role: invitation.role,
        organization: invitation.organization,
        expiresAt: invitation.expiresAt,
      }
    }),

  // Accept invitation (public - can be used before authentication)
  accept: publicProcedure
    .input(acceptInvitationSchema)
    .mutation(async ({ ctx, input }) => {
      const invitation = await ctx.db.invitation.findUnique({
        where: { token: input.token },
        include: {
          organization: true,
        },
      })

      if (!invitation) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Invitation not found',
        })
      }

      if (invitation.status !== 'PENDING') {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Invitation is no longer valid',
        })
      }

      if (invitation.expiresAt < new Date()) {
        await ctx.db.invitation.update({
          where: { id: invitation.id },
          data: { status: 'EXPIRED' },
        })

        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Invitation has expired',
        })
      }

      // Find or create user
      let user = await ctx.db.user.findUnique({
        where: { email: invitation.email },
      })

      if (!user) {
        // Create user account for the invitation
        user = await ctx.db.user.create({
          data: {
            email: invitation.email,
            name: invitation.email.split('@')[0], // Default name from email
            emailVerified: new Date(), // Auto-verify for invited users
          },
        })
      }

      // Check if user is already a member
      const existingMembership = await ctx.db.userOrganization.findFirst({
        where: {
          userId: user.id,
          organizationId: invitation.organizationId,
        },
      })

      if (existingMembership) {
        // Update invitation status
        await ctx.db.invitation.update({
          where: { id: invitation.id },
          data: {
            status: 'ACCEPTED',
            acceptedAt: new Date(),
          },
        })

        return {
          success: true,
          message: 'You are already a member of this organization',
          organization: invitation.organization,
          requiresSignIn: !ctx.session,
        }
      }

      // Add user to organization
      await ctx.db.$transaction([
        ctx.db.userOrganization.create({
          data: {
            userId: user.id,
            organizationId: invitation.organizationId,
            role: invitation.role,
          },
        }),
        ctx.db.invitation.update({
          where: { id: invitation.id },
          data: {
            status: 'ACCEPTED',
            acceptedAt: new Date(),
          },
        }),
      ])

      return {
        success: true,
        message: 'Successfully joined the organization',
        organization: invitation.organization,
        requiresSignIn: !ctx.session,
      }
    }),

  // Get organization invitations with filtering and pagination
  getOrganizationInvitations: createPermissionProcedure(PERMISSIONS.MEMBERS_READ)
    .input(invitationFiltersSchema)
    .query(async ({ ctx, input }) => {
      const where: any = { organizationId: input.organizationId }

      // Apply filters
      if (input.status) where.status = input.status
      if (input.role) where.role = input.role
      if (input.batchId) where.batchId = input.batchId
      if (input.search) {
        where.email = { contains: input.search, mode: 'insensitive' }
      }

      const [invitations, total] = await Promise.all([
        ctx.db.invitation.findMany({
          where,
          include: {
            inviter: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            template: {
              select: {
                id: true,
                name: true,
              },
            },
          },
          orderBy: [
            { batchId: 'desc' },
            { batchIndex: 'asc' },
            { createdAt: 'desc' },
          ],
          take: input.limit,
          skip: input.offset,
        }),
        ctx.db.invitation.count({ where }),
      ])

      return {
        invitations,
        total,
        hasMore: input.offset + input.limit < total,
      }
    }),

  // Invite user to organization
  invite: createAuditedPermissionProcedure(PERMISSIONS.MEMBERS_INVITE)
    .input(inviteUserSchema)
    .mutation(async ({ ctx, input }) => {
      // Check if user is already a member
      const existingMember = await ctx.db.userOrganization.findFirst({
        where: {
          organizationId: input.organizationId,
          user: { email: input.email },
        },
      })

      if (existingMember) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: 'User is already a member of this organization',
        })
      }

      // Check if there's already a pending invitation
      const existingInvitation = await ctx.db.invitation.findFirst({
        where: {
          email: input.email,
          organizationId: input.organizationId,
          status: 'PENDING',
        },
      })

      if (existingInvitation) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: 'Invitation already sent to this email',
        })
      }

      // Create invitation
      const invitation = await ctx.db.invitation.create({
        data: {
          email: input.email,
          organizationId: input.organizationId,
          role: input.role,
          token: crypto.randomUUID(),
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        },
        include: {
          organization: {
            select: {
              name: true,
              slug: true,
            },
          },
        },
      })

      // Send invitation email
      try {
        await sendInvitationEmail(
          input.email,
          invitation.organization.name,
          ctx.session.user.name || 'Team member',
          invitation.token,
          input.role
        )
      } catch (error) {
        console.error('Failed to send invitation email:', error)
        // Don't fail the invitation creation if email fails
      }

      return {
        id: invitation.id,
        email: invitation.email,
        role: invitation.role,
        status: invitation.status,
        expiresAt: invitation.expiresAt,
        createdAt: invitation.createdAt,
      }
    }),

  // Update invitation
  update: createAuditedPermissionProcedure(PERMISSIONS.MEMBERS_UPDATE)
    .input(updateInvitationSchema)
    .mutation(async ({ ctx, input }) => {
      const { invitationId, ...updateData } = input

      const invitation = await ctx.db.invitation.update({
        where: { id: invitationId },
        data: updateData,
      })

      return invitation
    }),

  // Revoke invitation
  revoke: createAuditedPermissionProcedure(PERMISSIONS.MEMBERS_UPDATE)
    .input(z.object({ invitationId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const invitation = await ctx.db.invitation.update({
        where: { id: input.invitationId },
        data: { status: 'REVOKED' },
      })

      return invitation
    }),

  // Resend invitation
  resend: createAuditedPermissionProcedure(PERMISSIONS.MEMBERS_INVITE)
    .input(z.object({ invitationId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const invitation = await ctx.db.invitation.findUnique({
        where: { id: input.invitationId },
        include: {
          organization: {
            select: {
              name: true,
              slug: true,
            },
          },
        },
      })

      if (!invitation) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Invitation not found',
        })
      }

      if (invitation.status !== 'PENDING') {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Can only resend pending invitations',
        })
      }

      // Update expiration date
      const updatedInvitation = await ctx.db.invitation.update({
        where: { id: invitation.id },
        data: {
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        },
      })

      // Resend invitation email
      try {
        await sendInvitationEmail(
          invitation.email,
          invitation.organization.name,
          ctx.session.user.name || 'Team member',
          invitation.token,
          invitation.role
        )
      } catch (error) {
        console.error('Failed to resend invitation email:', error)
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to send invitation email',
        })
      }

      return updatedInvitation
    }),

  // Bulk invite users to organization
  bulkInvite: createAuditedPermissionProcedure(PERMISSIONS.MEMBERS_INVITE)
    .input(bulkInviteSchema)
    .mutation(async ({ ctx, input }) => {
      const batchId = crypto.randomUUID()
      const results = []
      const errors = []

      // Validate all emails first
      for (const invitation of input.invitations) {
        // Check if user is already a member
        const existingMember = await ctx.db.userOrganization.findFirst({
          where: {
            organizationId: input.organizationId,
            user: { email: invitation.email },
          },
        })

        if (existingMember) {
          errors.push({
            email: invitation.email,
            error: 'User is already a member of this organization',
          })
          continue
        }

        // Check if there's already a pending invitation
        const existingInvitation = await ctx.db.invitation.findFirst({
          where: {
            email: invitation.email,
            organizationId: input.organizationId,
            status: 'PENDING',
          },
        })

        if (existingInvitation) {
          errors.push({
            email: invitation.email,
            error: 'Invitation already sent to this email',
          })
          continue
        }

        results.push(invitation)
      }

      // Create invitations for valid emails
      const createdInvitations = []
      for (let i = 0; i < results.length; i++) {
        const invitation = results[i]

        try {
          const createdInvitation = await ctx.db.invitation.create({
            data: {
              email: invitation.email,
              organizationId: input.organizationId,
              role: invitation.role,
              token: crypto.randomUUID(),
              expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
              invitedBy: ctx.session.user.id,
              message: invitation.message,
              templateId: input.templateId,
              batchId,
              batchIndex: i,
              emailSentAt: new Date(),
            },
            include: {
              organization: {
                select: {
                  name: true,
                  slug: true,
                },
              },
            },
          })

          // Send invitation email
          try {
            await sendInvitationEmail(
              invitation.email,
              createdInvitation.organization.name,
              ctx.session.user.name || 'Team member',
              createdInvitation.token,
              invitation.role
            )
            createdInvitations.push(createdInvitation)
          } catch (emailError) {
            console.error('Failed to send invitation email:', emailError)
            // Mark invitation as created but email failed
            await ctx.db.invitation.update({
              where: { id: createdInvitation.id },
              data: { emailSentAt: null },
            })
            errors.push({
              email: invitation.email,
              error: 'Invitation created but email failed to send',
            })
          }
        } catch (error) {
          console.error('Failed to create invitation:', error)
          errors.push({
            email: invitation.email,
            error: 'Failed to create invitation',
          })
        }
      }

      return {
        batchId,
        success: createdInvitations.length,
        errors: errors.length,
        invitations: createdInvitations,
        errorDetails: errors,
      }
    }),

  // Get bulk invitation batch status
  getBatchStatus: createPermissionProcedure(PERMISSIONS.MEMBERS_READ)
    .input(z.object({
      organizationId: z.string(),
      batchId: z.string(),
    }))
    .query(async ({ ctx, input }) => {
      const invitations = await ctx.db.invitation.findMany({
        where: {
          organizationId: input.organizationId,
          batchId: input.batchId,
        },
        include: {
          inviter: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: { batchIndex: 'asc' },
      })

      const stats = {
        total: invitations.length,
        pending: invitations.filter(i => i.status === 'PENDING').length,
        accepted: invitations.filter(i => i.status === 'ACCEPTED').length,
        expired: invitations.filter(i => i.status === 'EXPIRED').length,
        revoked: invitations.filter(i => i.status === 'REVOKED').length,
        emailsSent: invitations.filter(i => i.emailSentAt).length,
      }

      return {
        invitations,
        stats,
      }
    }),

  // Bulk revoke invitations
  bulkRevoke: createAuditedPermissionProcedure(PERMISSIONS.MEMBERS_UPDATE)
    .input(z.object({ invitationIds: z.array(z.string()) }))
    .mutation(async ({ ctx, input }) => {
      const updatedInvitations = await ctx.db.invitation.updateMany({
        where: {
          id: { in: input.invitationIds },
          status: 'PENDING', // Only revoke pending invitations
        },
        data: { status: 'REVOKED' },
      })

      return {
        success: true,
        count: updatedInvitations.count,
      }
    }),

  // Bulk resend invitations
  bulkResend: createAuditedPermissionProcedure(PERMISSIONS.MEMBERS_INVITE)
    .input(z.object({ invitationIds: z.array(z.string()) }))
    .mutation(async ({ ctx, input }) => {
      const invitations = await ctx.db.invitation.findMany({
        where: {
          id: { in: input.invitationIds },
          status: 'PENDING',
        },
        include: {
          organization: {
            select: {
              name: true,
              slug: true,
            },
          },
        },
      })

      const results = []
      const errors = []

      for (const invitation of invitations) {
        try {
          // Update expiration date
          await ctx.db.invitation.update({
            where: { id: invitation.id },
            data: {
              expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
              emailSentAt: new Date(),
            },
          })

          // Resend invitation email
          await sendInvitationEmail(
            invitation.email,
            invitation.organization.name,
            ctx.session.user.name || 'Team member',
            invitation.token,
            invitation.role
          )

          results.push(invitation.id)
        } catch (error) {
          console.error('Failed to resend invitation:', error)
          errors.push({
            invitationId: invitation.id,
            email: invitation.email,
            error: 'Failed to resend invitation',
          })
        }
      }

      return {
        success: results.length,
        errors: errors.length,
        errorDetails: errors,
      }
    }),
})
