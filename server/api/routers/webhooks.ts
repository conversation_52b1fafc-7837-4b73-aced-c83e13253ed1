import { z } from 'zod'
import { TRPCError } from '@trpc/server'
import { createTRPCRouter, protectedProcedure, createPermissionProcedure } from '@/lib/trpc'
import { PERMISSIONS } from '@/lib/permissions'

const createWebhookSchema = z.object({
  organizationId: z.string(),
  url: z.string().url(),
  events: z.array(z.string()).min(1),
  secret: z.string().optional(),
})

const updateWebhookSchema = z.object({
  webhookId: z.string(),
  url: z.string().url().optional(),
  events: z.array(z.string()).min(1).optional(),
  secret: z.string().optional(),
  isActive: z.boolean().optional(),
})

// Available webhook events
export const WEBHOOK_EVENTS = [
  'invitation.created',
  'invitation.accepted',
  'invitation.expired',
  'invitation.revoked',
  'member.joined',
  'member.left',
  'member.role_changed',
  'contribution.created',
  'contribution.verified',
  'contribution.rejected',
  'expense.created',
  'expense.approved',
  'expense.rejected',
  'organization.updated',
] as const

export const webhooksRouter = createTRPCRouter({
  // Get organization webhooks
  getOrganizationWebhooks: createPermissionProcedure(PERMISSIONS.ORGANIZATION_UPDATE)
    .input(z.object({ organizationId: z.string() }))
    .query(async ({ ctx, input }) => {
      const webhooks = await ctx.db.webhook.findMany({
        where: { organizationId: input.organizationId },
        include: {
          _count: {
            select: {
              deliveries: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      })

      return webhooks
    }),

  // Get webhook by ID
  getById: createPermissionProcedure(PERMISSIONS.ORGANIZATION_UPDATE)
    .input(z.object({ webhookId: z.string() }))
    .query(async ({ ctx, input }) => {
      const webhook = await ctx.db.webhook.findUnique({
        where: { id: input.webhookId },
        include: {
          organization: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          _count: {
            select: {
              deliveries: true,
            },
          },
        },
      })

      if (!webhook) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Webhook not found',
        })
      }

      // Check if user has access to this organization
      const userOrg = await ctx.db.userOrganization.findFirst({
        where: {
          userId: ctx.session.user.id,
          organizationId: webhook.organizationId,
        },
      })

      if (!userOrg) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied',
        })
      }

      return webhook
    }),

  // Create webhook
  create: createPermissionProcedure(PERMISSIONS.ORGANIZATION_UPDATE)
    .input(createWebhookSchema)
    .mutation(async ({ ctx, input }) => {
      // Validate events
      const invalidEvents = input.events.filter(event =>
        !WEBHOOK_EVENTS.includes(event as any)
      )

      if (invalidEvents.length > 0) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: `Invalid webhook events: ${invalidEvents.join(', ')}`,
        })
      }

      const webhook = await ctx.db.webhook.create({
        data: {
          organizationId: input.organizationId,
          url: input.url,
          events: input.events,
          secret: input.secret || crypto.randomUUID(),
        },
      })

      return webhook
    }),

  // Update webhook
  update: createPermissionProcedure(PERMISSIONS.ORGANIZATION_UPDATE)
    .input(updateWebhookSchema)
    .mutation(async ({ ctx, input }) => {
      const { webhookId, ...updateData } = input

      const webhook = await ctx.db.webhook.findUnique({
        where: { id: webhookId },
      })

      if (!webhook) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Webhook not found',
        })
      }

      // Check if user has access to this organization
      const userOrg = await ctx.db.userOrganization.findFirst({
        where: {
          userId: ctx.session.user.id,
          organizationId: webhook.organizationId,
        },
      })

      if (!userOrg) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied',
        })
      }

      // Validate events if provided
      if (updateData.events) {
        const invalidEvents = updateData.events.filter(event =>
          !WEBHOOK_EVENTS.includes(event as any)
        )

        if (invalidEvents.length > 0) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: `Invalid webhook events: ${invalidEvents.join(', ')}`,
          })
        }
      }

      const updatedWebhook = await ctx.db.webhook.update({
        where: { id: webhookId },
        data: updateData,
      })

      return updatedWebhook
    }),

  // Delete webhook
  delete: createPermissionProcedure(PERMISSIONS.ORGANIZATION_UPDATE)
    .input(z.object({ webhookId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const webhook = await ctx.db.webhook.findUnique({
        where: { id: input.webhookId },
      })

      if (!webhook) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Webhook not found',
        })
      }

      // Check if user has access to this organization
      const userOrg = await ctx.db.userOrganization.findFirst({
        where: {
          userId: ctx.session.user.id,
          organizationId: webhook.organizationId,
        },
      })

      if (!userOrg) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied',
        })
      }

      await ctx.db.webhook.delete({
        where: { id: input.webhookId },
      })

      return { success: true }
    }),

  // Test webhook
  test: createPermissionProcedure(PERMISSIONS.ORGANIZATION_UPDATE)
    .input(z.object({
      webhookId: z.string(),
      payload: z.any().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const webhook = await ctx.db.webhook.findUnique({
        where: { id: input.webhookId },
        include: {
          organization: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
      })

      if (!webhook) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Webhook not found',
        })
      }

      // Check if user has access to this organization
      const userOrg = await ctx.db.userOrganization.findFirst({
        where: {
          userId: ctx.session.user.id,
          organizationId: webhook.organizationId,
        },
      })

      if (!userOrg) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied',
        })
      }

      // Use provided payload or create default test payload
      const testPayload = input.payload || {
        event: 'webhook.test',
        timestamp: new Date().toISOString(),
        data: {
          webhook_id: webhook.id,
          organization: webhook.organization,
          test: true,
        },
      }

      try {
        // Send test webhook (you would implement the actual webhook delivery service)
        const response = await fetch(webhook.url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Webhook-Secret': webhook.secret || '',
            'User-Agent': 'KooLek-Webhooks/1.0',
          },
          body: JSON.stringify(testPayload),
        })

        // Log the delivery
        await ctx.db.webhookDelivery.create({
          data: {
            webhookId: webhook.id,
            event: 'webhook.test',
            payload: testPayload,
            response: {
              status: response.status,
              statusText: response.statusText,
              headers: Object.fromEntries(response.headers.entries()),
            },
            status: response.ok ? 'success' : 'failed',
          },
        })

        return {
          success: response.ok,
          status: response.status,
          statusText: response.statusText,
        }
      } catch (error) {
        // Log the failed delivery
        await ctx.db.webhookDelivery.create({
          data: {
            webhookId: webhook.id,
            event: 'webhook.test',
            payload: testPayload,
            response: {
              error: error instanceof Error ? error.message : 'Unknown error',
            },
            status: 'failed',
          },
        })

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to send test webhook',
        })
      }
    }),

  // Get webhook deliveries
  getDeliveries: createPermissionProcedure(PERMISSIONS.ORGANIZATION_UPDATE)
    .input(z.object({
      webhookId: z.string(),
      limit: z.number().min(1).max(100).default(20),
      offset: z.number().min(0).default(0),
    }))
    .query(async ({ ctx, input }) => {
      const webhook = await ctx.db.webhook.findUnique({
        where: { id: input.webhookId },
      })

      if (!webhook) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Webhook not found',
        })
      }

      // Check if user has access to this organization
      const userOrg = await ctx.db.userOrganization.findFirst({
        where: {
          userId: ctx.session.user.id,
          organizationId: webhook.organizationId,
        },
      })

      if (!userOrg) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied',
        })
      }

      const [deliveries, total] = await Promise.all([
        ctx.db.webhookDelivery.findMany({
          where: { webhookId: input.webhookId },
          orderBy: { createdAt: 'desc' },
          take: input.limit,
          skip: input.offset,
        }),
        ctx.db.webhookDelivery.count({
          where: { webhookId: input.webhookId },
        }),
      ])

      return {
        deliveries,
        total,
        hasMore: input.offset + input.limit < total,
      }
    }),

  // Get available webhook events
  getAvailableEvents: createPermissionProcedure(PERMISSIONS.ORGANIZATION_READ)
    .query(() => {
      return WEBHOOK_EVENTS.map(event => ({
        value: event,
        label: event.replace(/\./g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        description: getEventDescription(event),
      }))
    }),
})

function getEventDescription(event: string): string {
  const descriptions: Record<string, string> = {
    'invitation.created': 'Triggered when a new invitation is sent',
    'invitation.accepted': 'Triggered when an invitation is accepted',
    'invitation.expired': 'Triggered when an invitation expires',
    'invitation.revoked': 'Triggered when an invitation is revoked',
    'member.joined': 'Triggered when a new member joins the organization',
    'member.left': 'Triggered when a member leaves the organization',
    'member.role_changed': 'Triggered when a member\'s role is changed',
    'contribution.created': 'Triggered when a new contribution is created',
    'contribution.verified': 'Triggered when a contribution is verified',
    'contribution.rejected': 'Triggered when a contribution is rejected',
    'expense.created': 'Triggered when a new expense is created',
    'expense.approved': 'Triggered when an expense is approved',
    'expense.rejected': 'Triggered when an expense is rejected',
    'organization.updated': 'Triggered when organization settings are updated',
  }

  return descriptions[event] || 'No description available'
}
