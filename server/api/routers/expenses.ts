import { z } from 'zod'
import { TRPCError } from '@trpc/server'
import { createTRPCRouter, protectedProcedure, createPermissionProcedure } from '@/lib/trpc'
import { PERMISSIONS } from '@/lib/permissions'

// Validation schemas
const createExpenseSchema = z.object({
  organizationId: z.string(),
  amount: z.number().positive('Amount must be positive'),
  category: z.string().min(1, 'Category is required'),
  description: z.string().optional(),
  year: z.number().int().min(2020).max(2030).optional(),
  attachments: z.array(z.string()).default([]),
})

const updateExpenseSchema = z.object({
  expenseId: z.string(),
  amount: z.number().positive().optional(),
  category: z.string().min(1).optional(),
  description: z.string().optional(),
  status: z.enum(['PENDING', 'APPROVED', 'REJECTED']).optional(),
  attachments: z.array(z.string()).optional(),
})

const getExpensesSchema = z.object({
  organizationId: z.string(),
  year: z.number().int().optional(),
  status: z.enum(['PENDING', 'APPROVED', 'REJECTED']).optional(),
  category: z.string().optional(),
  limit: z.number().min(1).max(100).default(50),
  offset: z.number().min(0).default(0),
})

const deleteExpenseSchema = z.object({
  expenseId: z.string(),
})

const approveExpenseSchema = z.object({
  expenseId: z.string(),
  status: z.enum(['APPROVED', 'REJECTED']),
})

export const expensesRouter = createTRPCRouter({
  // Get all expenses for an organization
  getAll: createPermissionProcedure(PERMISSIONS.EXPENSES_READ)
    .input(getExpensesSchema)
    .query(async ({ ctx, input }) => {
      const { organizationId, year, status, category, limit, offset } = input

      // Build where clause
      const where: any = {
        organizationId,
      }

      if (year) where.year = year
      if (status) where.status = status
      if (category) where.category = category

      const [expenses, total] = await Promise.all([
        ctx.db.expense.findMany({
          where,
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
          take: limit,
          skip: offset,
        }),
        ctx.db.expense.count({ where }),
      ])

      return {
        expenses,
        total,
        hasMore: offset + limit < total,
      }
    }),

  // Get expense by ID
  getById: createPermissionProcedure(PERMISSIONS.EXPENSES_READ)
    .input(z.object({ expenseId: z.string() }))
    .query(async ({ ctx, input }) => {
      const expense = await ctx.db.expense.findFirst({
        where: {
          id: input.expenseId,
          organizationId: ctx.organizationId,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      })

      if (!expense) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Expense not found',
        })
      }

      return expense
    }),

  // Create new expense
  create: createPermissionProcedure(PERMISSIONS.EXPENSES_CREATE)
    .input(createExpenseSchema)
    .mutation(async ({ ctx, input }) => {
      const { organizationId, amount, category, description, year, attachments } = input

      // Verify organization access
      if (organizationId !== ctx.organizationId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied to this organization',
        })
      }

      const expense = await ctx.db.expense.create({
        data: {
          organizationId,
          userId: ctx.session.user.id,
          amount,
          category,
          description,
          year: year || new Date().getFullYear(),
          status: 'PENDING',
          attachments: JSON.stringify(attachments),
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      })

      return expense
    }),

  // Update expense
  update: createPermissionProcedure(PERMISSIONS.EXPENSES_UPDATE)
    .input(updateExpenseSchema)
    .mutation(async ({ ctx, input }) => {
      const { expenseId, ...updateData } = input

      // Check if expense exists and user has access
      const existingExpense = await ctx.db.expense.findFirst({
        where: {
          id: expenseId,
          organizationId: ctx.organizationId,
        },
      })

      if (!existingExpense) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Expense not found',
        })
      }

      // Only allow users to update their own expenses unless they're admin/owner
      if (
        existingExpense.userId !== ctx.session.user.id &&
        !['OWNER', 'ADMIN'].includes(ctx.userRole)
      ) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'You can only update your own expenses',
        })
      }

      // Prepare update data
      const updatePayload: any = {}
      if (updateData.amount !== undefined) updatePayload.amount = updateData.amount
      if (updateData.category !== undefined) updatePayload.category = updateData.category
      if (updateData.description !== undefined) updatePayload.description = updateData.description
      if (updateData.status !== undefined) updatePayload.status = updateData.status
      if (updateData.attachments !== undefined) updatePayload.attachments = JSON.stringify(updateData.attachments)

      const expense = await ctx.db.expense.update({
        where: { id: expenseId },
        data: updatePayload,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      })

      return expense
    }),

  // Delete expense
  delete: createPermissionProcedure(PERMISSIONS.EXPENSES_DELETE)
    .input(deleteExpenseSchema)
    .mutation(async ({ ctx, input }) => {
      const { expenseId } = input

      // Check if expense exists and user has access
      const existingExpense = await ctx.db.expense.findFirst({
        where: {
          id: expenseId,
          organizationId: ctx.organizationId,
        },
      })

      if (!existingExpense) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Expense not found',
        })
      }

      // Only allow users to delete their own expenses unless they're admin/owner
      if (
        existingExpense.userId !== ctx.session.user.id &&
        !['OWNER', 'ADMIN'].includes(ctx.userRole)
      ) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'You can only delete your own expenses',
        })
      }

      await ctx.db.expense.delete({
        where: { id: expenseId },
      })

      return { success: true }
    }),

  // Approve/Reject expense (Admin/Owner only)
  approve: createPermissionProcedure(PERMISSIONS.EXPENSES_APPROVE)
    .input(approveExpenseSchema)
    .mutation(async ({ ctx, input }) => {
      const { expenseId, status } = input

      // Check if expense exists
      const existingExpense = await ctx.db.expense.findFirst({
        where: {
          id: expenseId,
          organizationId: ctx.organizationId,
        },
      })

      if (!existingExpense) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Expense not found',
        })
      }

      const expense = await ctx.db.expense.update({
        where: { id: expenseId },
        data: {
          status,
          verifiedAt: new Date(),
          verifiedBy: ctx.session.user.id,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      })

      return expense
    }),

  // Get expense statistics
  getStats: createPermissionProcedure(PERMISSIONS.EXPENSES_READ)
    .input(z.object({ 
      organizationId: z.string(),
      year: z.number().int().optional(),
    }))
    .query(async ({ ctx, input }) => {
      const { organizationId, year } = input

      const where: any = { organizationId }
      if (year) where.year = year

      const [
        totalExpenses,
        pendingExpenses,
        approvedExpenses,
        rejectedExpenses,
        totalAmount,
        approvedAmount,
      ] = await Promise.all([
        ctx.db.expense.count({ where }),
        ctx.db.expense.count({ where: { ...where, status: 'PENDING' } }),
        ctx.db.expense.count({ where: { ...where, status: 'APPROVED' } }),
        ctx.db.expense.count({ where: { ...where, status: 'REJECTED' } }),
        ctx.db.expense.aggregate({
          where,
          _sum: { amount: true },
        }),
        ctx.db.expense.aggregate({
          where: { ...where, status: 'APPROVED' },
          _sum: { amount: true },
        }),
      ])

      return {
        totalExpenses,
        pendingExpenses,
        approvedExpenses,
        rejectedExpenses,
        totalAmount: totalAmount._sum.amount || 0,
        approvedAmount: approvedAmount._sum.amount || 0,
      }
    }),
})
