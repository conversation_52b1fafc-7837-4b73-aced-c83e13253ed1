import { createTRPCRouter } from '@/lib/trpc'
import { organizationRouter } from './routers/organization'
import { authRouter } from './routers/auth'
import { invitationsRouter } from './routers/invitations'
import { invitationTemplatesRouter } from './routers/invitation-templates'
import { onboardingRouter } from './routers/onboarding'
import { webhooksRouter } from './routers/webhooks'
import { membersRouter } from './routers/members'
import { contributionsRouter } from './routers/contributions'
import { expensesRouter } from './routers/expenses'

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
  auth: authRouter,
  organization: organizationRouter,
  invitations: invitationsRouter,
  invitationTemplates: invitationTemplatesRouter,
  onboarding: onboardingRouter,
  webhooks: webhooksRouter,
  members: membersRouter,
  contributions: contributionsRouter,
  expenses: expensesRouter,
})

// export type definition of API
export type AppRouter = typeof appRouter
