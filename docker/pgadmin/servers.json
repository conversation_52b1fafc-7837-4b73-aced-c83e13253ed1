{"Servers": {"1": {"Name": "KooLek Development", "Group": "Servers", "Host": "postgres", "Port": 5432, "MaintenanceDB": "koolek_db", "Username": "koolek_user", "Password": "koolek_password", "SSLMode": "prefer", "SSLCert": "<STORAGE_DIR>/.postgresql/postgresql.crt", "SSLKey": "<STORAGE_DIR>/.postgresql/postgresql.key", "SSLCompression": 0, "Timeout": 10, "UseSSHTunnel": 0, "TunnelHost": "", "TunnelPort": "22", "TunnelUsername": "", "TunnelAuthentication": 0}}}