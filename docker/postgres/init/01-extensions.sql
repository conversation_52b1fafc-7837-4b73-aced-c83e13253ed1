-- Enable required PostgreSQL extensions for KooLek
-- This script runs automatically when the container starts for the first time

-- UUID extension for generating UUIDs
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Crypto extension for password hashing and security
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Additional useful extensions for a SaaS platform
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create indexes for better performance
-- These will be created by Prisma migrations, but we can prepare the database

-- Set timezone to UTC for consistency
SET timezone = 'UTC';

-- Create a function to update updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Log successful initialization
DO $$
BEGIN
    RAISE NOTICE 'KooLek PostgreSQL database initialized successfully with extensions';
END $$;
