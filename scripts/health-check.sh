#!/bin/bash

# KooLek Docker Health Check Script
# Verifies that all Docker services are running correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a service is running
check_service() {
    local service_name=$1
    local container_name=$2

    if docker ps --format "table {{.Names}}" | grep -q "^${container_name}$"; then
        print_success "${service_name} is running"
        return 0
    else
        print_error "${service_name} is not running"
        return 1
    fi
}

# Function to check database connectivity
check_database() {
    print_status "Checking PostgreSQL connectivity..."

    if docker-compose exec -T postgres pg_isready -U KooLek_user -d KooLek_db > /dev/null 2>&1; then
        print_success "PostgreSQL is accepting connections"

        # Check if extensions are installed
        if docker-compose exec -T postgres psql -U KooLek_user -d KooLek_db -c "SELECT extname FROM pg_extension WHERE extname IN ('uuid-ossp', 'pgcrypto');" | grep -q "uuid-ossp"; then
            print_success "Required PostgreSQL extensions are installed"
        else
            print_warning "Some PostgreSQL extensions may be missing"
        fi

        return 0
    else
        print_error "PostgreSQL is not accepting connections"
        return 1
    fi
}

# Function to check Redis connectivity
check_redis() {
    print_status "Checking Redis connectivity..."

    if docker-compose exec -T redis redis-cli ping > /dev/null 2>&1; then
        print_success "Redis is responding to ping"
        return 0
    else
        print_error "Redis is not responding"
        return 1
    fi
}

# Function to check port availability
check_ports() {
    print_status "Checking port availability..."

    local ports=("5433:PostgreSQL" "6380:Redis" "5050:pgAdmin" "8081:Redis Commander")
    local all_good=true

    for port_info in "${ports[@]}"; do
        local port=$(echo $port_info | cut -d: -f1)
        local service=$(echo $port_info | cut -d: -f2)

        if lsof -i :$port > /dev/null 2>&1; then
            print_success "Port $port ($service) is in use"
        else
            if [[ "$service" == "pgAdmin" || "$service" == "Redis Commander" ]]; then
                print_warning "Port $port ($service) is not in use (optional service)"
            else
                print_error "Port $port ($service) is not in use"
                all_good=false
            fi
        fi
    done

    if $all_good; then
        return 0
    else
        return 1
    fi
}

# Function to check environment variables
check_environment() {
    print_status "Checking environment configuration..."

    if [ -f ".env" ]; then
        print_success ".env file exists"

        # Check critical environment variables
        if grep -q "DATABASE_URL.*localhost:5433" .env; then
            print_success "DATABASE_URL is configured for Docker"
        else
            print_warning "DATABASE_URL may not be configured for Docker"
        fi

        if grep -q "REDIS_URL.*localhost:6380" .env; then
            print_success "REDIS_URL is configured for Docker"
        else
            print_warning "REDIS_URL may not be configured for Docker"
        fi

    else
        print_error ".env file not found"
        return 1
    fi
}

# Function to check Docker and Docker Compose
check_docker() {
    print_status "Checking Docker installation..."

    if command -v docker > /dev/null 2>&1; then
        print_success "Docker is installed"

        if docker info > /dev/null 2>&1; then
            print_success "Docker daemon is running"
        else
            print_error "Docker daemon is not running"
            return 1
        fi
    else
        print_error "Docker is not installed"
        return 1
    fi

    if command -v docker-compose > /dev/null 2>&1; then
        print_success "Docker Compose is installed"
    else
        print_error "Docker Compose is not installed"
        return 1
    fi
}

# Function to show service status
show_status() {
    print_status "Docker Compose service status:"
    docker-compose ps
    echo ""
}

# Main health check function
main() {
    echo "KooLek Docker Environment Health Check"
    echo "====================================="
    echo ""

    local overall_status=0

    # Check Docker installation
    if ! check_docker; then
        overall_status=1
    fi
    echo ""

    # Check environment configuration
    if ! check_environment; then
        overall_status=1
    fi
    echo ""

    # Show service status
    show_status

    # Check individual services
    if ! check_service "PostgreSQL" "koolek-postgres"; then
        overall_status=1
    fi

    if ! check_service "Redis" "koolek-redis"; then
        overall_status=1
    fi
    echo ""

    # Check connectivity
    if ! check_database; then
        overall_status=1
    fi

    if ! check_redis; then
        overall_status=1
    fi
    echo ""

    # Check ports
    if ! check_ports; then
        overall_status=1
    fi
    echo ""

    # Final status
    if [ $overall_status -eq 0 ]; then
        print_success "All health checks passed! 🎉"
        echo ""
        print_status "Your KooLek development environment is ready!"
        print_status "Next steps:"
        echo "  1. Run 'npm run db:push' to apply database schema"
        echo "  2. Run 'npm run db:seed' to seed initial data"
        echo "  3. Run 'npm run dev' to start the application"
    else
        print_error "Some health checks failed! ❌"
        echo ""
        print_status "Please check the errors above and:"
        echo "  1. Ensure Docker is running"
        echo "  2. Run 'npm run docker:start' to start services"
        echo "  3. Check your .env configuration"
    fi

    return $overall_status
}

# Run the health check
main "$@"
