/**
 * Migration Script: Single-Tenant to Multi-Tenant
 *
 * This script migrates existing KooLek data from a single-tenant structure
 * to the new multi-tenant architecture.
 *
 * IMPORTANT: Run this script only once and backup your data first!
 */

import { PrismaClient } from '@prisma/client'
import { hash } from 'bcryptjs'

const prisma = new PrismaClient()

interface LegacyUser {
  id: number
  staff_id: string
  name: string
  email: string
  role: string
  department?: string
  position?: string
  monthly_contribution?: number
  status: string
}

interface LegacyKooLeknt {
  id: number
  user_id: number
  reference: string
  amount: number
  category: string
  description?: string
  status: string
  year: number
  created_at: string
  updated_at: string
  verified_at?: string
  verified_by?: number
}

interface LegacyExpense {
  id: number
  user_id: number
  amount: number
  category: string
  description?: string
  status: string
  year: number
  created_at: string
  updated_at: string
  verified_at?: string
  verified_by?: number
}

async function migrateToMultiTenant() {
  console.log('🚀 Starting migration to multi-tenant architecture...')

  try {
    // Step 1: Create default organization for existing data
    console.log('📦 Creating default organization...')

    const defaultOrg = await prisma.organization.create({
      data: {
        name: 'KooLek Organization',
        slug: 'koolek-org',
        settings: {
          monthlyContributionDefault: 25,
          currency: 'USD',
          timezone: 'UTC',
        },
        metadata: {
          migratedFrom: 'single-tenant',
          migrationDate: new Date().toISOString(),
        },
      },
    })

    console.log(`✅ Created organization: ${defaultOrg.name} (${defaultOrg.id})`)

    // Step 2: Migrate existing users (if any exist in legacy format)
    console.log('👥 Migrating users...')

    // This would typically read from your existing user data
    // For now, we'll create a default admin user if none exists
    const existingUsers = await prisma.user.findMany()

    if (existingUsers.length === 0) {
      const adminUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'System Administrator',
          password: await hash('admin123', 12),
          emailVerified: new Date(),
        },
      })

      // Add admin to organization as owner
      await prisma.userOrganization.create({
        data: {
          userId: adminUser.id,
          organizationId: defaultOrg.id,
          role: 'OWNER',
        },
      })

      console.log(`✅ Created admin user: ${adminUser.email}`)
    } else {
      // Add existing users to the default organization
      for (const user of existingUsers) {
        const existingMembership = await prisma.userOrganization.findFirst({
          where: {
            userId: user.id,
            organizationId: defaultOrg.id,
          },
        })

        if (!existingMembership) {
          await prisma.userOrganization.create({
            data: {
              userId: user.id,
              organizationId: defaultOrg.id,
              role: 'MEMBER',
            },
          })
          console.log(`✅ Added user ${user.email} to organization`)
        }
      }
    }

    // Step 3: Create default categories
    console.log('📂 Creating default categories...')

    const contributionCategories = [
      'Monthly Contribution',
      'Special Assessment',
      'Event Fee',
      'Membership Fee',
      'Other',
    ]

    const expenseCategories = [
      'Food & Beverage',
      'Transportation',
      'Office Supplies',
      'Equipment',
      'Training',
      'Marketing',
      'Utilities',
      'Maintenance',
      'Others',
    ]

    for (const categoryName of contributionCategories) {
      await prisma.category.upsert({
        where: {
          organizationId_name_type: {
            organizationId: defaultOrg.id,
            name: categoryName,
            type: 'CONTRIBUTION',
          },
        },
        update: {},
        create: {
          organizationId: defaultOrg.id,
          name: categoryName,
          type: 'CONTRIBUTION',
          isDefault: true,
        },
      })
    }

    for (const categoryName of expenseCategories) {
      await prisma.category.upsert({
        where: {
          organizationId_name_type: {
            organizationId: defaultOrg.id,
            name: categoryName,
            type: 'EXPENSE',
          },
        },
        update: {},
        create: {
          organizationId: defaultOrg.id,
          name: categoryName,
          type: 'EXPENSE',
          isDefault: true,
        },
      })
    }

    console.log('✅ Created default categories')

    // Step 4: Migrate existing contributions (if any)
    console.log('💳 Migrating contributions...')

    // Check if there are any existing contributions that need organizationId
    const contributionsWithoutOrg = await prisma.contribution.findMany({
      where: {
        organizationId: null,
      },
    })

    if (contributionsWithoutOrg.length > 0) {
      await prisma.contribution.updateMany({
        where: {
          organizationId: null,
        },
        data: {
          organizationId: defaultOrg.id,
        },
      })
      console.log(`✅ Updated ${contributionsWithoutOrg.length} contributions with organization ID`)
    }

    // Step 5: Migrate existing expenses (if any)
    console.log('🧾 Migrating expenses...')

    const expensesWithoutOrg = await prisma.expense.findMany({
      where: {
        organizationId: null,
      },
    })

    if (expensesWithoutOrg.length > 0) {
      await prisma.expense.updateMany({
        where: {
          organizationId: null,
        },
        data: {
          organizationId: defaultOrg.id,
        },
      })
      console.log(`✅ Updated ${expensesWithoutOrg.length} expenses with organization ID`)
    }

    // Step 6: Create a sample collection
    console.log('📊 Creating sample collection...')

    const currentYear = new Date().getFullYear()
    await prisma.collection.create({
      data: {
        organizationId: defaultOrg.id,
        name: `${currentYear} Annual Collection`,
        amount: 0,
        description: 'Migrated collection from single-tenant system',
        year: currentYear,
        status: 'active',
      },
    })

    console.log('✅ Created sample collection')

    console.log('\n🎉 Migration completed successfully!')
    console.log('\n📋 Migration Summary:')
    console.log(`   • Organization created: ${defaultOrg.name}`)
    console.log(`   • Users migrated: ${existingUsers.length}`)
    console.log(`   • Contribution categories: ${contributionCategories.length}`)
    console.log(`   • Expense categories: ${expenseCategories.length}`)
    console.log(`   • Contributions updated: ${contributionsWithoutOrg.length}`)
    console.log(`   • Expenses updated: ${expensesWithoutOrg.length}`)

    console.log('\n🔐 Default Admin Account:')
    console.log('   Email: <EMAIL>')
    console.log('   Password: admin123')
    console.log('   Organization: koolek-org')

    console.log('\n⚠️  Important Next Steps:')
    console.log('   1. Change the default admin password')
    console.log('   2. Update organization settings')
    console.log('   3. Invite team members')
    console.log('   4. Test all functionality')
    console.log('   5. Update your application configuration')

  } catch (error) {
    console.error('❌ Migration failed:', error)
    throw error
  }
}

// Run migration if this script is executed directly
if (require.main === module) {
  migrateToMultiTenant()
    .then(async () => {
      await prisma.$disconnect()
      process.exit(0)
    })
    .catch(async (error) => {
      console.error(error)
      await prisma.$disconnect()
      process.exit(1)
    })
}

export { migrateToMultiTenant }
