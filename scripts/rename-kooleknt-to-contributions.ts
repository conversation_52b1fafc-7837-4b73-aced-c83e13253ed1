/**
 * Migration Script: Rename KooLeknts to Contributions
 *
 * This script renames the KooLeknts table to contributions and updates
 * all related references to use the new naming convention.
 *
 * IMPORTANT: Backup your data before running this script!
 */

import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function renameKooLekntsToContributions() {
  console.log('🚀 Starting KooLeknts to Contributions migration...')

  try {
    // Step 1: Check if KooLeknts table exists
    console.log('🔍 Checking current database structure...')
    
    const tableExists = await prisma.$queryRaw`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'KooLeknts'
      );
    `
    
    if (!Array.isArray(tableExists) || !tableExists[0]?.exists) {
      console.log('ℹ️  KooLeknts table not found. Checking for contributions table...')
      
      const contributionsExists = await prisma.$queryRaw`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = 'contributions'
        );
      `
      
      if (Array.isArray(contributionsExists) && contributionsExists[0]?.exists) {
        console.log('✅ Contributions table already exists. Migration may have already been completed.')
        return
      } else {
        console.log('❌ Neither KooLeknts nor contributions table found. Please check your database.')
        return
      }
    }

    console.log('📋 Found KooLeknts table. Starting migration...')

    // Step 2: Rename the table
    console.log('🔄 Renaming KooLeknts table to contributions...')
    await prisma.$executeRaw`ALTER TABLE "KooLeknts" RENAME TO "contributions";`
    console.log('✅ Table renamed successfully')

    // Step 3: Update enum values if needed
    console.log('🔄 Updating enum values...')
    
    // Check if the enum type exists and update it
    await prisma.$executeRaw`
      DO $$ 
      BEGIN
        -- Rename enum type if it exists
        IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'KooLekntStatus') THEN
          ALTER TYPE "KooLekntStatus" RENAME TO "ContributionStatus";
        END IF;
        
        -- Update CategoryType enum value if it exists
        IF EXISTS (SELECT 1 FROM pg_enum WHERE enumlabel = 'KooLekNT') THEN
          UPDATE pg_enum SET enumlabel = 'CONTRIBUTION' 
          WHERE enumlabel = 'KooLekNT' 
          AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'CategoryType');
        END IF;
      END $$;
    `
    console.log('✅ Enum values updated successfully')

    // Step 4: Update any foreign key constraint names if needed
    console.log('🔄 Updating constraint names...')
    await prisma.$executeRaw`
      DO $$ 
      BEGIN
        -- Update foreign key constraint names to match new table name
        IF EXISTS (
          SELECT 1 FROM information_schema.table_constraints 
          WHERE constraint_name LIKE '%KooLeknts%' 
          AND table_name = 'contributions'
        ) THEN
          -- Rename constraints that reference the old table name
          ALTER TABLE contributions 
          RENAME CONSTRAINT "KooLeknts_organizationId_fkey" 
          TO "contributions_organizationId_fkey";
          
          ALTER TABLE contributions 
          RENAME CONSTRAINT "KooLeknts_userId_fkey" 
          TO "contributions_userId_fkey";
        END IF;
      EXCEPTION
        WHEN undefined_object THEN
          -- Constraints might not exist or have different names
          NULL;
      END $$;
    `
    console.log('✅ Constraint names updated successfully')

    // Step 5: Update index names
    console.log('🔄 Updating index names...')
    await prisma.$executeRaw`
      DO $$ 
      BEGIN
        -- Rename indexes to match new table name
        IF EXISTS (SELECT 1 FROM pg_indexes WHERE indexname LIKE '%KooLeknts%') THEN
          ALTER INDEX "KooLeknts_organizationId_status_idx" 
          RENAME TO "contributions_organizationId_status_idx";
          
          ALTER INDEX "KooLeknts_organizationId_year_idx" 
          RENAME TO "contributions_organizationId_year_idx";
        END IF;
      EXCEPTION
        WHEN undefined_object THEN
          -- Indexes might not exist or have different names
          NULL;
      END $$;
    `
    console.log('✅ Index names updated successfully')

    // Step 6: Verify the migration
    console.log('🔍 Verifying migration...')
    
    const contributionCount = await prisma.$queryRaw`
      SELECT COUNT(*) as count FROM contributions;
    `
    
    console.log(`✅ Migration completed successfully!`)
    console.log(`📊 Found ${Array.isArray(contributionCount) ? contributionCount[0]?.count || 0 : 0} records in contributions table`)

    console.log('\n🎉 Migration Summary:')
    console.log('   • Table renamed: KooLeknts → contributions')
    console.log('   • Enum renamed: KooLekntStatus → ContributionStatus')
    console.log('   • CategoryType updated: KooLekNT → CONTRIBUTION')
    console.log('   • Foreign key constraints updated')
    console.log('   • Index names updated')

    console.log('\n⚠️  Next Steps:')
    console.log('   1. Generate new Prisma client: npm run db:generate')
    console.log('   2. Restart your development server')
    console.log('   3. Test the application functionality')
    console.log('   4. Update any remaining code references')

  } catch (error) {
    console.error('❌ Migration failed:', error)
    console.error('\n🔧 Troubleshooting:')
    console.error('   1. Check database connection')
    console.error('   2. Ensure you have proper database permissions')
    console.error('   3. Verify table structure manually')
    console.error('   4. Check for any active transactions or locks')
    throw error
  }
}

// Run migration if this script is executed directly
if (require.main === module) {
  renameKooLekntsToContributions()
    .then(async () => {
      await prisma.$disconnect()
      console.log('\n✅ Migration completed and database connection closed.')
      process.exit(0)
    })
    .catch(async (error) => {
      console.error(error)
      await prisma.$disconnect()
      process.exit(1)
    })
}

export { renameKooLekntsToContributions }
