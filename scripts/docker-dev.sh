#!/bin/bash

# KooLek Docker Development Environment Manager
# This script helps manage the Docker development environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to start the development environment
start_dev() {
    print_status "Starting KooLek development environment..."
    check_docker

    # Start core services (postgres and redis)
    docker-compose up -d postgres redis

    print_status "Waiting for services to be healthy..."

    # Wait for PostgreSQL to be ready
    until docker-compose exec postgres pg_isready -U koolek_user -d koolek_db > /dev/null 2>&1; do
        print_status "Waiting for PostgreSQL to be ready..."
        sleep 2
    done

    # Wait for Redis to be ready
    until docker-compose exec redis redis-cli ping > /dev/null 2>&1; do
        print_status "Waiting for Redis to be ready..."
        sleep 2
    done

    print_success "Development environment is ready!"
    print_status "PostgreSQL: localhost:5433"
    print_status "Redis: localhost:6380"
    print_status ""
    print_status "To start with management tools, run: $0 start-with-tools"
}

# Function to start with management tools
start_with_tools() {
    print_status "Starting KooLek development environment with management tools..."
    check_docker

    # Start all services including tools
    docker-compose --profile tools up -d

    print_status "Waiting for services to be healthy..."
    sleep 10

    print_success "Development environment with tools is ready!"
    print_status "PostgreSQL: localhost:5433"
    print_status "Redis: localhost:6380"
    print_status "pgAdmin: http://localhost:5050 (<EMAIL> / admin123)"
    print_status "Redis Commander: http://localhost:8081 (admin / admin123)"
}

# Function to stop the development environment
stop_dev() {
    print_status "Stopping KooLek development environment..."
    docker-compose down
    print_success "Development environment stopped!"
}

# Function to restart the development environment
restart_dev() {
    print_status "Restarting KooLek development environment..."
    stop_dev
    start_dev
}

# Function to view logs
logs() {
    if [ -z "$2" ]; then
        docker-compose logs -f
    else
        docker-compose logs -f "$2"
    fi
}

# Function to reset the database
reset_db() {
    print_warning "This will destroy all data in the database!"
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Resetting database..."
        docker-compose down postgres
        docker volume rm koolek-standalone_postgres_data 2>/dev/null || true
        docker-compose up -d postgres

        # Wait for PostgreSQL to be ready
        until docker-compose exec postgres pg_isready -U koolek_user -d koolek_db > /dev/null 2>&1; do
            print_status "Waiting for PostgreSQL to be ready..."
            sleep 2
        done

        print_success "Database reset complete!"
        print_status "Run 'npm run db:push' to apply schema and 'npm run db:seed' to seed data"
    else
        print_status "Database reset cancelled."
    fi
}

# Function to backup database
backup_db() {
    print_status "Creating database backup..."
    BACKUP_FILE="backup_$(date +%Y%m%d_%H%M%S).sql"
    docker-compose exec postgres pg_dump -U koolek_user koolek_db > "$BACKUP_FILE"
    print_success "Database backup created: $BACKUP_FILE"
}

# Function to show status
status() {
    print_status "KooLek Development Environment Status:"
    docker-compose ps
}

# Function to show help
show_help() {
    echo "KooLek Docker Development Environment Manager"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start              Start development environment (PostgreSQL + Redis)"
    echo "  start-with-tools   Start with management tools (pgAdmin + Redis Commander)"
    echo "  stop               Stop development environment"
    echo "  restart            Restart development environment"
    echo "  logs [service]     View logs (all services or specific service)"
    echo "  status             Show status of all services"
    echo "  reset-db           Reset database (destroys all data)"
    echo "  backup-db          Create database backup"
    echo "  help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start"
    echo "  $0 logs postgres"
    echo "  $0 reset-db"
}

# Main script logic
case "${1:-help}" in
    start)
        start_dev
        ;;
    start-with-tools)
        start_with_tools
        ;;
    stop)
        stop_dev
        ;;
    restart)
        restart_dev
        ;;
    logs)
        logs "$@"
        ;;
    status)
        status
        ;;
    reset-db)
        reset_db
        ;;
    backup-db)
        backup_db
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
