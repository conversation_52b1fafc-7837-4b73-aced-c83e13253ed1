# Phase 2: Authentication & Authorization Setup Guide

## 🎯 Overview

This guide walks you through setting up Phase 2 of the KooLek multi-tenant SaaS platform. Building upon Phase 1's core multi-tenancy, Phase 2 adds:

- ✅ NextAuth.js v5 with async APIs and enhanced session management
- ✅ Multi-provider authentication (Google OAuth + Credentials)
- ✅ Role-Based Access Control (RBAC) system
- ✅ Organization-scoped security middleware
- ✅ Email-based invitations and password reset
- ✅ Enhanced user profile management
- ✅ Audit logging and security features

## 📋 Prerequisites

- Phase 1 Core Multi-Tenancy completed
- PostgreSQL database configured
- Email service provider (Resend recommended)
- Google OAuth credentials (optional but recommended)

## 🚀 Installation Steps

### 1. Install New Dependencies

```bash
# Install authentication and email dependencies
npm install @auth/prisma-adapter nodemailer resend
npm install -D @types/nodemailer
```

### 2. Database Schema Updates

```bash
# Generate new Prisma client with updated schema
npm run db:generate

# Push schema changes to database
npm run db:push

# Re-seed database with enhanced user data
npm run db:seed
```

### 3. Environment Configuration

Update your `.env.local` file with the new required variables:

```bash
# NextAuth.js v5 Configuration
NEXTAUTH_SECRET="your-super-secret-key-here-min-32-chars"
NEXTAUTH_URL="http://localhost:3002"

# Google OAuth (Optional but recommended)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Email Service (Required for invitations and password reset)
RESEND_API_KEY="re_your-resend-api-key"
FROM_EMAIL="<EMAIL>"

# App Configuration
NEXT_PUBLIC_APP_URL="http://localhost:3002"
```

### 4. Google OAuth Setup (Optional)

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `http://localhost:3002/api/auth/callback/google` (development)
   - `https://yourdomain.com/api/auth/callback/google` (production)

### 5. Email Service Setup

#### Option A: Resend (Recommended)
1. Sign up at [Resend](https://resend.com)
2. Get your API key from the dashboard
3. Verify your domain for production use

#### Option B: Other providers
- Update `lib/email.ts` to use your preferred email service
- Ensure SMTP credentials are configured

## 🧪 Testing the Enhanced Authentication

### 1. Start Development Server

```bash
npm run dev
```

### 2. Test Authentication Features

#### Basic Authentication
- Visit `/auth/signin` for the sign-in page
- Test with demo accounts:
  - **Admin**: <EMAIL> / admin123
  - **Member**: <EMAIL> / member123

#### Google OAuth
- Click "Sign in with Google" button
- Complete OAuth flow
- Verify user is created and logged in

#### Password Reset
- Go to sign-in page and click "Forgot password?"
- Enter email address
- Check email for reset link
- Complete password reset flow

### 3. Test RBAC System

#### Permission-Based UI
```tsx
import { PermissionGate, AdminOnly, OwnerOnly } from '@/components/auth/PermissionGate'
import { PERMISSIONS } from '@/lib/permissions'

// Only show to users with specific permission
<PermissionGate permission={PERMISSIONS.MEMBERS_INVITE}>
  <InviteUserButton />
</PermissionGate>

// Only show to admins
<AdminOnly>
  <AdminPanel />
</AdminOnly>

// Only show to organization owners
<OwnerOnly>
  <DeleteOrganizationButton />
</OwnerOnly>
```

#### API-Level Permissions
```tsx
// tRPC procedures automatically enforce permissions
const inviteUser = api.invitations.invite.useMutation()
const deleteOrg = api.organization.delete.useMutation()
```

### 4. Test Organization Switching

- Switch between organizations using the selector
- Verify session updates with organization context
- Check that data is properly scoped to selected organization

### 5. Test Invitation System

#### Send Invitations
- Go to organization settings
- Invite new users with different roles
- Check email delivery

#### Accept Invitations
- Open invitation email
- Click accept link
- Complete account creation or sign-in
- Verify user is added to organization

## 🔧 Development Commands

```bash
# Development with enhanced features
npm run dev

# Database operations
npm run db:generate    # Generate Prisma client
npm run db:push       # Push schema changes
npm run db:studio     # Open Prisma Studio
npm run db:seed       # Seed with enhanced data

# Type checking
npm run type-check

# Build and start
npm run build
npm start
```

## 📁 New File Structure

Phase 2 adds these new files and enhancements:

```
koolek-standalone/
├── 📁 app/
│   └── api/
│       ├── auth/[...nextauth]/route.ts    # NextAuth.js v5 handler
│       └── trpc/[trpc]/route.ts          # tRPC API handler
├── 📁 lib/
│   ├── auth.ts                           # Enhanced NextAuth.js config
│   ├── permissions.ts                    # RBAC system
│   ├── email.ts                          # Email service
│   └── trpc.ts                          # Enhanced tRPC with middleware
├── 📁 server/api/routers/
│   ├── auth.ts                          # Authentication router
│   └── invitations.ts                   # Invitation management
├── 📁 components/
│   ├── auth/
│   │   └── PermissionGate.tsx           # Permission-based UI components
│   └── providers/
│       ├── SessionProvider.tsx          # NextAuth.js session provider
│       └── TRPCProvider.tsx            # tRPC client provider
├── 📁 contexts/
│   └── OrganizationContext.tsx          # Enhanced with session integration
└── middleware.ts                        # Route protection middleware
```

## 🔍 Verification Checklist

### Authentication Features
- [ ] Sign in with email/password works
- [ ] Google OAuth sign-in works (if configured)
- [ ] Password reset flow works
- [ ] Email verification works
- [ ] User profile management works

### Authorization Features
- [ ] Role-based permissions work correctly
- [ ] UI components respect user permissions
- [ ] API endpoints enforce proper authorization
- [ ] Organization switching updates session context
- [ ] Middleware protects routes appropriately

### Invitation System
- [ ] Can send invitations to new users
- [ ] Invitation emails are delivered
- [ ] Invitation acceptance flow works
- [ ] Users are properly added to organizations
- [ ] Role assignments work correctly

### Security Features
- [ ] Audit logging captures user actions
- [ ] Session management works across browser tabs
- [ ] Organization data isolation is maintained
- [ ] CSRF protection is active
- [ ] Rate limiting is functional (if configured)

## 🐛 Troubleshooting

### Authentication Issues

#### NextAuth.js Session Problems
```bash
# Clear Next.js cache
rm -rf .next

# Check environment variables
echo $NEXTAUTH_SECRET
echo $NEXTAUTH_URL
```

#### Google OAuth Issues
- Verify redirect URIs in Google Console
- Check client ID and secret
- Ensure Google+ API is enabled

### Email Issues

#### Resend API Problems
```bash
# Test API key
curl -X POST 'https://api.resend.com/emails' \
  -H 'Authorization: Bearer YOUR_API_KEY' \
  -H 'Content-Type: application/json'
```

#### Email Not Delivered
- Check spam folder
- Verify domain configuration
- Check Resend dashboard for delivery status

### Permission Issues

#### Users Can't Access Features
- Check user's role in organization
- Verify permission definitions in `lib/permissions.ts`
- Check tRPC middleware logs

#### API Calls Failing
```bash
# Check browser network tab for error details
# Look for 403 Forbidden responses
# Verify organization context in session
```

### Database Issues

#### Migration Problems
```bash
# Reset database if needed
npm run db:reset

# Re-run migrations
npm run db:push
npm run db:seed
```

## 🎯 Next Steps

After completing Phase 2, you're ready for:

- **Phase 3**: Invitation System Enhancement (bulk invitations, advanced user management)
- **Phase 4**: UI/UX Enhancement (App Router migration, advanced components)
- **Phase 5**: Advanced Features (Organization branding, analytics, compliance)

## 📞 Support

If you encounter issues:

1. Check the troubleshooting section above
2. Review error logs in the terminal and browser console
3. Verify all environment variables are set correctly
4. Ensure database schema is up to date
5. Check that all dependencies are installed

## 🎉 Success!

If you can:
- Sign in with multiple providers
- Switch organizations with proper session updates
- Send and accept invitations
- See permission-based UI changes
- Access audit logs

Then Phase 2 is complete! You now have a **enterprise-grade authentication and authorization system** with NextAuth.js v5, comprehensive RBAC, and secure multi-tenant architecture! 🎉
