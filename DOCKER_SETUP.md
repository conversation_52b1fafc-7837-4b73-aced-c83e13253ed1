# KooLek Docker Development Setup

This document provides comprehensive instructions for setting up and using the Docker-based development environment for KooLek.

## 🐳 Overview

The Docker setup includes:
- **PostgreSQL 15** with required extensions (uuid-ossp, pgcrypto)
- **Redis 7** for caching and session storage
- **pgAdmin** for database management (optional)
- **Redis Commander** for Redis management (optional)

## 📋 Prerequisites

- Docker Desktop installed and running
- Docker Compose v2.0+ (included with Docker Desktop)
- Node.js 18+ for running the Next.js application

## 🚀 Quick Start

### 1. Start the Development Environment

```bash
# Start PostgreSQL and Redis
npm run docker:start

# OR start with management tools (pgAdmin + Redis Commander)
npm run docker:start-tools

# OR use the script directly
./scripts/docker-dev.sh start
```

### 2. Initialize the Database

```bash
# Apply Prisma schema to the database
npm run db:push

# Seed the database with initial data
npm run db:seed

# OR do both in one command
npm run dev:setup
```

### 3. Start the Next.js Application

```bash
npm run dev
```

Your application will be available at:
- **Next.js App**: http://localhost:3002
- **pgAdmin**: http://localhost:5050 (if started with tools)
- **Redis Commander**: http://localhost:8081 (if started with tools)

## 🛠️ Available Commands

### NPM Scripts

```bash
# Docker management
npm run docker:start          # Start PostgreSQL + Redis
npm run docker:start-tools    # Start with management tools
npm run docker:stop           # Stop all services
npm run docker:restart        # Restart all services
npm run docker:status         # Show service status
npm run docker:logs           # View all logs
npm run docker:reset-db       # Reset database (destroys data)
npm run docker:backup-db      # Create database backup

# Complete development setup
npm run dev:setup             # Start Docker + apply schema + seed data
```

### Direct Script Usage

```bash
# Start services
./scripts/docker-dev.sh start
./scripts/docker-dev.sh start-with-tools

# Management
./scripts/docker-dev.sh stop
./scripts/docker-dev.sh restart
./scripts/docker-dev.sh status

# Logs
./scripts/docker-dev.sh logs           # All services
./scripts/docker-dev.sh logs postgres # Specific service

# Database operations
./scripts/docker-dev.sh reset-db
./scripts/docker-dev.sh backup-db
```

## 🔧 Service Configuration

### PostgreSQL
- **Host**: localhost
- **Port**: 5433
- **Database**: koolek_db
- **Username**: koolek_user
- **Password**: koolek_password
- **Extensions**: uuid-ossp, pgcrypto, pg_stat_statements, pg_trgm

### Redis
- **Host**: localhost
- **Port**: 6380
- **Configuration**: Optimized for development with persistence

### pgAdmin (Optional)
- **URL**: http://localhost:5050
- **Email**: <EMAIL>
- **Password**: admin123
- **Pre-configured** with KooLek database connection

### Redis Commander (Optional)
- **URL**: http://localhost:8081
- **Username**: admin
- **Password**: admin123

## 📁 Directory Structure

```
docker/
├── postgres/
│   ├── init/
│   │   └── 01-extensions.sql    # Database initialization
│   └── conf/
│       └── postgresql.conf      # PostgreSQL configuration
├── redis/
│   └── redis.conf               # Redis configuration
└── pgadmin/
    └── servers.json             # pgAdmin server configuration

scripts/
└── docker-dev.sh               # Development environment manager
```

## 🔍 Troubleshooting

### Services Won't Start

1. **Check Docker is running**:
   ```bash
   docker info
   ```

2. **Check port conflicts**:
   ```bash
   # Check if ports are in use
   lsof -i :5433  # PostgreSQL
   lsof -i :6380  # Redis
   lsof -i :5050  # pgAdmin
   ```

3. **View service logs**:
   ```bash
   npm run docker:logs
   # OR for specific service
   docker-compose logs postgres
   ```

### Database Connection Issues

1. **Verify services are healthy**:
   ```bash
   npm run docker:status
   ```

2. **Test database connection**:
   ```bash
   docker-compose exec postgres pg_isready -U koolek_user -d koolek_db
   ```

3. **Check environment variables**:
   Ensure `.env` has the correct `DATABASE_URL`:
   ```
   DATABASE_URL="postgresql://koolek_user:koolek_password@localhost:5433/koolek_db"
   ```

### Reset Everything

If you encounter persistent issues:

```bash
# Stop all services
npm run docker:stop

# Remove all volumes (destroys data)
docker-compose down -v

# Remove Docker images (optional)
docker-compose down --rmi all

# Start fresh
npm run dev:setup
```

## 🔒 Security Notes

### Development Environment
- Default passwords are used for convenience
- Services are exposed on localhost only
- Not suitable for production use

### Production Considerations
- Change all default passwords
- Use environment variables for secrets
- Configure proper network security
- Enable SSL/TLS connections
- Set up proper backup strategies

## 📊 Performance Optimization

### PostgreSQL
- Configured with development-optimized settings
- Query logging enabled for debugging
- Statistics collection enabled

### Redis
- Persistence enabled with AOF and RDB
- Memory optimization configured
- Slow query logging enabled

## 🔄 Data Persistence

### Volumes
- `postgres_data`: PostgreSQL data directory
- `redis_data`: Redis data directory
- `pgadmin_data`: pgAdmin configuration

### Backups
```bash
# Create database backup
npm run docker:backup-db

# Manual backup
docker-compose exec postgres pg_dump -U koolek_user koolek_db > backup.sql

# Restore from backup
docker-compose exec -T postgres psql -U koolek_user koolek_db < backup.sql
```

## 🚀 Next Steps

1. **Start Development**:
   ```bash
   npm run dev:setup
   npm run dev
   ```

2. **Access Management Tools**:
   - pgAdmin: http://localhost:5050
   - Redis Commander: http://localhost:8081

3. **Begin Coding**:
   - Database schema is ready
   - Multi-tenant structure in place
   - Development tools available

## 📚 Additional Resources

- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Redis Documentation](https://redis.io/documentation)
- [Prisma Documentation](https://www.prisma.io/docs/)

---

**Happy Coding! 🎉**
