import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { SessionProvider } from '@/components/providers/SessionProvider'
import { TRPCProvider } from '@/components/providers/TRPCProvider'
import { OrganizationProvider } from '@/contexts/OrganizationContext'
import { Toaster } from '@/components/ui/toaster'

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
})

export const metadata: Metadata = {
  title: 'KooLek - Multi-Tenant Financial Management',
  description: 'Modern multi-tenant SaaS platform for financial management, KooLeknts, and expense tracking.',
  keywords: ['financial management', 'KooLeknts', 'expenses', 'multi-tenant', 'SaaS'],
  authors: [{ name: 'KooLek Team' }],
  viewport: 'width=device-width, initial-scale=1',
  themeColor: '#2563eb',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={inter.variable}>
      <body className="font-inter antialiased bg-background text-foreground">
        <SessionProvider>
          <TRPCProvider>
            <OrganizationProvider>
              {children}
              <Toaster />
            </OrganizationProvider>
          </TRPCProvider>
        </SessionProvider>
      </body>
    </html>
  )
}
