@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Enhanced KooLek Design System - Notion-inspired */

    /* Core Colors */
    --background: 0 0% 100%;
    --foreground: 220 13% 18%;
    --card: 0 0% 100%;
    --card-foreground: 220 13% 18%;
    --popover: 0 0% 100%;
    --popover-foreground: 220 13% 18%;

    /* Primary - KooLek Blue */
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;

    /* Secondary & Muted */
    --secondary: 220 14.3% 95.9%;
    --secondary-foreground: 220 8.9% 46.1%;
    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;

    /* Accent */
    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220 8.9% 46.1%;

    /* Semantic Colors */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --success: 142 76% 36%;
    --success-foreground: 210 40% 98%;
    --warning: 38 92% 50%;
    --warning-foreground: 220 13% 18%;

    /* Borders & Inputs */
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 221.2 83.2% 53.3%;

    /* Enhanced Radius */
    --radius: 0.5rem;

    /* Notion-inspired Neutral Palette */
    --gray-50: 220 14% 96%;
    --gray-100: 220 13% 91%;
    --gray-200: 220 13% 87%;
    --gray-300: 220 9% 78%;
    --gray-400: 220 9% 46%;
    --gray-500: 220 9% 46%;
    --gray-600: 220 13% 35%;
    --gray-700: 220 13% 26%;
    --gray-800: 220 13% 18%;
    --gray-900: 220 13% 9%;

    /* Enhanced Shadows */
    --shadow-subtle: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-small: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-medium: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-large: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Typography Scale */
  .text-display {
    @apply text-5xl font-extrabold tracking-tight;
  }

  .text-h1 {
    @apply text-4xl font-bold tracking-tight;
  }

  .text-h2 {
    @apply text-3xl font-semibold tracking-tight;
  }

  .text-h3 {
    @apply text-2xl font-semibold tracking-tight;
  }

  .text-h4 {
    @apply text-xl font-semibold tracking-tight;
  }

  .text-body {
    @apply text-base font-normal;
  }

  .text-small {
    @apply text-sm font-normal;
  }

  .text-caption {
    @apply text-xs font-medium;
  }

  /* Layout Components */
  .container-app {
    @apply container mx-auto px-4 sm:px-6 lg:px-8;
  }

  .page-header {
    @apply border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60;
  }

  .sidebar {
    @apply border-r bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60;
  }

  /* Card Components */
  .card-elevated {
    @apply bg-card text-card-foreground shadow-lg border rounded-lg;
  }

  .card-interactive {
    @apply transition-all duration-200 hover:shadow-md hover:scale-[1.02] cursor-pointer;
  }

  /* Status Indicators */
  .status-success {
    @apply bg-green-50 text-green-700 border-green-200;
  }

  .status-warning {
    @apply bg-yellow-50 text-yellow-700 border-yellow-200;
  }

  .status-error {
    @apply bg-red-50 text-red-700 border-red-200;
  }

  .status-info {
    @apply bg-blue-50 text-blue-700 border-blue-200;
  }

  /* Navigation Components */
  .nav-item {
    @apply flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-all hover:bg-accent hover:text-accent-foreground;
  }

  .nav-item-active {
    @apply bg-primary text-primary-foreground hover:bg-primary/90;
  }

  /* Form Components */
  .form-section {
    @apply space-y-6 rounded-lg border bg-card p-6;
  }

  .form-group {
    @apply space-y-2;
  }

  /* Animation Classes */
  .animate-fade-in {
    @apply animate-in fade-in duration-200;
  }

  .animate-slide-in {
    @apply animate-in slide-in-from-left-4 duration-300;
  }

  .animate-scale-in {
    @apply animate-in zoom-in-95 duration-200;
  }
}

@layer utilities {
  /* Spacing Utilities */
  .space-section {
    @apply space-y-8;
  }

  .space-content {
    @apply space-y-6;
  }

  .space-form {
    @apply space-y-4;
  }

  .space-tight {
    @apply space-y-2;
  }

  /* Responsive Utilities */
  .responsive-grid {
    @apply grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }

  .responsive-flex {
    @apply flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between;
  }

  /* Enhanced Responsive Patterns */
  .responsive-grid-auto {
    @apply grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }

  .responsive-grid-cards {
    @apply grid gap-6 grid-cols-1 md:grid-cols-2 xl:grid-cols-3;
  }

  .responsive-grid-dashboard {
    @apply grid gap-6 grid-cols-1 lg:grid-cols-3 xl:grid-cols-4;
  }

  .responsive-stack {
    @apply flex flex-col gap-4 md:flex-row md:items-center;
  }

  .responsive-sidebar {
    @apply fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0;
  }

  .responsive-main {
    @apply flex-1 lg:pl-64;
  }

  /* Mobile-first responsive text */
  .text-responsive-sm {
    @apply text-sm sm:text-base;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl lg:text-2xl;
  }

  .text-responsive-xl {
    @apply text-xl sm:text-2xl lg:text-3xl xl:text-4xl;
  }

  /* Touch-friendly utilities */
  .touch-target {
    @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
  }

  .touch-friendly {
    @apply p-3 sm:p-2;
  }

  /* Focus Utilities */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  .focus-visible-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
  }
}

/* Custom Scrollbar */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted-foreground)) transparent;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: hsl(var(--muted-foreground));
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--foreground));
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }

  .print-break {
    page-break-after: always;
  }
}
