'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { useSession } from 'next-auth/react'
import { api } from '@/components/providers/TRPCProvider'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Building2, ArrowLeft, CheckCircle, AlertCircle } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

export default function CreateOrganizationPage() {
  const router = useRouter()
  const { data: session } = useSession()
  const { toast } = useToast()

  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    domain: '',
  })
  const [isCreating, setIsCreating] = useState(false)
  const [error, setError] = useState('')

  const createMutation = api.organization.create.useMutation({
    onSuccess: (organization) => {
      toast({
        title: 'Organization created!',
        description: `${organization.name} has been created successfully.`,
      })
      router.push(`/${organization.slug}/dashboard`)
    },
    onError: (error) => {
      setError(error.message)
      toast({
        variant: 'destructive',
        title: 'Failed to create organization',
        description: error.message,
      })
    },
  })

  const testMutation = api.organization.test.useMutation({
    onSuccess: (data) => {
      console.log('Test mutation success:', data)
      toast({
        title: 'Test successful!',
        description: `Received: ${JSON.stringify(data)}`,
      })
    },
    onError: (error) => {
      console.error('Test mutation error:', error)
      toast({
        variant: 'destructive',
        title: 'Test failed!',
        description: error.message,
      })
    },
  })

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  const handleNameChange = (name: string) => {
    setFormData(prev => ({
      ...prev,
      name,
      slug: prev.slug || generateSlug(name),
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsCreating(true)
    setError('')

    try {
      const payload = {
        name: formData.name,
        slug: formData.slug,
        domain: formData.domain || undefined,
      }

      await createMutation.mutateAsync(payload)
    } catch (error) {
      // Error is handled by onError callback
    } finally {
      setIsCreating(false)
    }
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-muted-foreground">Please sign in to create an organization.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      <div className="container-app py-12">
        <div className="max-w-2xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <Link href="/organizations">
              <Button variant="ghost" className="mb-4">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Organizations
              </Button>
            </Link>

            <div className="text-center">
              <div className="flex items-center justify-center gap-3 mb-6">
                <div className="p-3 bg-primary/10 rounded-lg">
                  <Building2 className="h-8 w-8 text-primary" />
                </div>
                <h1 className="text-3xl font-bold">Create Organization</h1>
              </div>
              <p className="text-muted-foreground text-lg">
                Set up your new organization and start managing KooLeknts and expenses
              </p>
            </div>
          </div>

          {/* Form */}
          <Card>
            <CardHeader>
              <CardTitle>Organization Details</CardTitle>
              <CardDescription>
                Provide basic information about your organization. You can update these details later.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="name">Organization Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleNameChange(e.target.value)}
                    placeholder="Acme Inc."
                    required
                    disabled={isCreating}
                  />
                  <p className="text-xs text-muted-foreground">
                    The display name for your organization
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="slug">URL Slug *</Label>
                  <Input
                    id="slug"
                    value={formData.slug}
                    onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                    placeholder="acme-inc"
                    pattern="^[a-z0-9-]+$"
                    title="Only lowercase letters, numbers, and hyphens allowed"
                    required
                    disabled={isCreating}
                  />
                  <p className="text-xs text-muted-foreground">
                    This will be used in your organization's URL: <code>/{formData.slug}</code>
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="domain">Domain (Optional)</Label>
                  <Input
                    id="domain"
                    value={formData.domain}
                    onChange={(e) => setFormData(prev => ({ ...prev, domain: e.target.value }))}
                    placeholder="acme.com"
                    disabled={isCreating}
                  />
                  <p className="text-xs text-muted-foreground">
                    Users with this domain can be automatically invited to your organization
                  </p>
                </div>

                {error && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <div className="flex gap-3 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => router.push('/organizations')}
                    disabled={isCreating}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                  <Button
                    type="button"
                    variant="secondary"
                    onClick={() => testMutation.mutate({ message: "test input" })}
                    disabled={isCreating}
                  >
                    Test Input
                  </Button>
                  <Button
                    type="submit"
                    disabled={isCreating || !formData.name || !formData.slug}
                    className="flex-1"
                  >
                    {isCreating ? 'Creating...' : 'Create Organization'}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>

          {/* Info Card */}
          <Card className="mt-6">
            <CardContent className="pt-6">
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                <div>
                  <h4 className="font-medium mb-1">What happens next?</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• You'll be set as the organization owner</li>
                    <li>• You can invite team members to join</li>
                    <li>• Start managing KooLeknts and expenses right away</li>
                    <li>• Customize organization settings and branding</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
