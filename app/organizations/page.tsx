'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useSession, signOut } from 'next-auth/react'
import { useOrganization } from '@/contexts/OrganizationContext'
import { OrganizationSelector } from '@/components/organization/OrganizationSelector'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Building2, Users, Plus, ArrowRight, Settings } from 'lucide-react'
import Link from 'next/link'

export default function OrganizationsPage() {
  const router = useRouter()
  const { data: session, status } = useSession()
  const { organizations, currentOrganization, isLoading, switchOrganization } = useOrganization()

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/signin')
    }
  }, [status, router])

  // Removed automatic redirect to allow users to choose organization
  // Users can manually select an organization from the list

  if (status === 'loading' || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto" />
          <p className="text-muted-foreground">Loading organizations...</p>
        </div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  const handleOrganizationSelect = async (orgId: string) => {
    try {
      await switchOrganization(orgId)
      const selectedOrg = organizations?.find(org => org.id === orgId)
      if (selectedOrg) {
        router.push(`/${selectedOrg.slug}/dashboard`)
      }
    } catch (error) {
      console.error('Failed to switch organization:', error)
    }
  }

  const handleSignOut = async () => {
    await signOut({ callbackUrl: '/signin' })
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      <div className="container-app py-12">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="flex items-center justify-center gap-3 mb-6">
              <div className="p-3 bg-primary/10 rounded-lg">
                <Building2 className="h-8 w-8 text-primary" />
              </div>
              <h1 className="text-3xl font-bold">KooLek</h1>
            </div>
            <h2 className="text-2xl font-semibold mb-4">
              Welcome back, {session.user?.name}
            </h2>
            <p className="text-muted-foreground text-lg">
              Select an organization to continue or create a new one
            </p>
          </div>

          {/* Organizations Grid */}
          {organizations && organizations.length > 0 ? (
            <div className="space-y-8">
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {organizations.map((org) => (
                  <Card
                    key={org.id}
                    className="card-interactive hover:border-primary/50 cursor-pointer"
                    onClick={() => handleOrganizationSelect(org.id)}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-3">
                          <div className="p-2 bg-primary/10 rounded-lg">
                            <Building2 className="h-5 w-5 text-primary" />
                          </div>
                          <div>
                            <CardTitle className="text-lg">{org.name}</CardTitle>
                            <CardDescription className="text-sm">
                              {org.domain || 'No domain set'}
                            </CardDescription>
                          </div>
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          {org.role.toLowerCase()}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Users className="h-4 w-4" />
                          <span>{org.memberCount || 0} members</span>
                        </div>
                        <ArrowRight className="h-4 w-4 text-muted-foreground" />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* Create New Organization */}
              <div className="text-center">
                <Card className="border-dashed border-2 hover:border-primary/50 transition-colors">
                  <CardContent className="py-12">
                    <div className="space-y-4">
                      <div className="p-3 bg-muted rounded-lg w-fit mx-auto">
                        <Plus className="h-8 w-8 text-muted-foreground" />
                      </div>
                      <div>
                        <h3 className="font-semibold mb-2">Create New Organization</h3>
                        <p className="text-muted-foreground text-sm mb-4">
                          Start a new organization and invite your team members
                        </p>
                        <Link href="/organizations/create">
                          <Button>
                            <Plus className="h-4 w-4 mr-2" />
                            Create Organization
                          </Button>
                        </Link>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          ) : (
            /* No Organizations - First Time User */
            <div className="text-center">
              <Card className="max-w-md mx-auto">
                <CardContent className="py-12">
                  <div className="space-y-6">
                    <div className="p-4 bg-muted rounded-lg w-fit mx-auto">
                      <Building2 className="h-12 w-12 text-muted-foreground" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold mb-2">Welcome to KooLek!</h3>
                      <p className="text-muted-foreground mb-6">
                        Get started by creating your first organization or wait for an invitation from your team.
                      </p>
                      <div className="space-y-3">
                        <Link href="/organizations/create">
                          <Button className="w-full">
                            <Plus className="h-4 w-4 mr-2" />
                            Create Your First Organization
                          </Button>
                        </Link>
                        <p className="text-sm text-muted-foreground">
                          Already have an invitation? Check your email for the invitation link.
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* User Menu */}
          <div className="mt-12 text-center">
            <div className="inline-flex items-center gap-4 p-4 bg-card rounded-lg border">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-primary">
                    {session.user?.name?.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div className="text-left">
                  <p className="font-medium text-sm">{session.user?.name}</p>
                  <p className="text-xs text-muted-foreground">{session.user?.email}</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="ghost" size="sm">
                  <Settings className="h-4 w-4 mr-2" />
                  Profile
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleSignOut}
                >
                  Sign Out
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
