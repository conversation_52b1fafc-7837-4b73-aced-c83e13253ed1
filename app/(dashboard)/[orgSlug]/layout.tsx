'use client'

import { useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useOrganization } from '@/contexts/OrganizationContext'

export default function OrganizationLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const params = useParams()
  const router = useRouter()
  const { currentOrganization, organizations, switchOrganization, isLoading } = useOrganization()
  const orgSlug = params.orgSlug as string

  useEffect(() => {
    if (!isLoading && organizations) {
      const targetOrg = organizations.find(org => org.slug === orgSlug)
      
      if (!targetOrg) {
        // Organization not found, redirect to organizations page
        router.push('/organizations')
        return
      }

      if (!currentOrganization || currentOrganization.slug !== orgSlug) {
        // Switch to the target organization
        switchOrganization(targetOrg.id)
      }
    }
  }, [orgSlug, currentOrganization, organizations, isLoading, switchOrganization, router])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto" />
          <p className="text-muted-foreground">Loading organization...</p>
        </div>
      </div>
    )
  }

  if (!currentOrganization || currentOrganization.slug !== orgSlug) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto" />
          <p className="text-muted-foreground">Switching organization...</p>
        </div>
      </div>
    )
  }

  return <>{children}</>
}
