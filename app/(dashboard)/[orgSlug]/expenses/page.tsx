'use client'

import Link from 'next/link'
import { useOrganization } from '@/contexts/OrganizationContext'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Receipt, Plus, Filter, Download } from 'lucide-react'

export default function ExpensesPage() {
  const { currentOrganization } = useOrganization()

  if (!currentOrganization) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto" />
          <p className="text-muted-foreground">Loading expenses...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-section">
      {/* Page Header */}
      <div className="responsive-flex">
        <div>
          <h1 className="text-h1">Expenses</h1>
          <p className="text-muted-foreground">
            Track expenses for {currentOrganization.name}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button asChild>
            <Link href={`/${currentOrganization.slug}/expenses/new`}>
              <Plus className="h-4 w-4 mr-2" />
              New Expense
            </Link>
          </Button>
        </div>
      </div>

      {/* Expenses Content */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Receipt className="h-5 w-5" />
            Expense Records
          </CardTitle>
          <CardDescription>
            Track and manage all expense transactions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <Receipt className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No expenses yet</h3>
            <p className="text-muted-foreground mb-4">
              Start by recording your first expense
            </p>
            <Button asChild>
              <Link href={`/${currentOrganization.slug}/expenses/new`}>
                <Plus className="h-4 w-4 mr-2" />
                Add Expense
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
