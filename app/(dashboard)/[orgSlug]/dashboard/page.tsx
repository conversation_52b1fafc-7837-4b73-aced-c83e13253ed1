'use client'

import { DashboardOverview } from '@/components/dashboard/DashboardOverview'
import { FinancialCards } from '@/components/dashboard/FinancialCards'
import { RecentActivity } from '@/components/dashboard/RecentActivity'
import { QuickActions } from '@/components/dashboard/QuickActions'
import { OrganizationOverview } from '@/components/dashboard/OrganizationOverview'
import { useOrganization } from '@/contexts/OrganizationContext'

export default function DashboardPage() {
  const { currentOrganization } = useOrganization()

  if (!currentOrganization) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="text-center space-y-6">
          <div className="animate-spin rounded-full h-10 w-10 border-2 border-primary border-t-transparent mx-auto" />
          <div className="space-y-2">
            <p className="text-lg font-medium">Loading dashboard</p>
            <p className="text-sm text-muted-foreground">Please wait while we prepare your workspace</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container-app py-8 space-section">
        {/* Page Header - Enhanced with Notion-like styling */}
        <div className="space-y-6">
          <div className="responsive-flex">
            <div className="space-y-3">
              <h1 className="text-display font-bold tracking-tight text-foreground">
                Dashboard
              </h1>
              <p className="text-body-lg text-muted-foreground">
                Welcome back to <span className="font-medium text-foreground">{currentOrganization.name}</span>
              </p>
            </div>
            <QuickActions />
          </div>
        </div>

        {/* Financial Overview Cards - Enhanced with better spacing */}
        <div className="space-y-6">
          <div className="space-y-2">
            <h2 className="text-h2 font-semibold tracking-tight text-foreground">
              Financial Overview
            </h2>
            <p className="text-body text-muted-foreground">
              Key metrics and performance indicators for your organization
            </p>
          </div>
          <FinancialCards />
        </div>

        {/* Main Content Grid - Enhanced responsive layout */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
          {/* Left Column - Organization Overview */}
          <div className="lg:col-span-4 xl:col-span-3 space-y-6">
            <OrganizationOverview />
          </div>

          {/* Center Column - Dashboard Overview */}
          <div className="lg:col-span-5 xl:col-span-6 space-y-6">
            <DashboardOverview />
          </div>

          {/* Right Column - Recent Activity */}
          <div className="lg:col-span-3 xl:col-span-3 space-y-6">
            <RecentActivity />
          </div>
        </div>
      </div>
    </div>
  )
}
