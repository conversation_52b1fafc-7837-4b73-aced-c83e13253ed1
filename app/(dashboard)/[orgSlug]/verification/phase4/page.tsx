'use client'

import { Phase4Verification } from '@/components/verification/Phase4Verification'
import { useOrganization } from '@/contexts/OrganizationContext'

export default function Phase4VerificationPage() {
  const { currentOrganization } = useOrganization()

  if (!currentOrganization) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto" />
          <p className="text-muted-foreground">Loading verification...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-section">
      <Phase4Verification />
    </div>
  )
}
