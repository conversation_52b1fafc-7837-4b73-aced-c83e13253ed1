'use client'

import { useOrganization } from '@/contexts/OrganizationContext'
import { ContributionForm } from '@/components/forms/ContributionForm'
import { ArrowLeft } from 'lucide-react'
import { Button } from '@/components/ui/button'
import Link from 'next/link'

export default function NewContributionPage() {
  const { currentOrganization } = useOrganization()

  if (!currentOrganization) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto" />
          <p className="text-muted-foreground">Loading organization...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-section">
      {/* Page Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button variant="ghost" size="sm" asChild>
          <Link href={`/${currentOrganization.slug}/contributions`}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Contributions
          </Link>
        </Button>
      </div>

      <div className="space-y-6">
        <div className="text-center space-y-2">
          <h1 className="text-h1">New Contribution</h1>
          <p className="text-muted-foreground">
            Create a new contribution record for {currentOrganization.name}
          </p>
        </div>

        {/* Contribution Form */}
        <ContributionForm mode="create" />
      </div>
    </div>
  )
}
