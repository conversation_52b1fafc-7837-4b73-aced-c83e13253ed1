'use client'

import { useOrganization } from '@/contexts/OrganizationContext'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { CreditCard, Plus, Filter, Download } from 'lucide-react'
import Link from 'next/link'
import { api } from '@/components/providers/TRPCProvider'

export default function ContributionsPage() {
  const { currentOrganization } = useOrganization()

  // Fetch contributions data
  const { data: contributionsData, isLoading } = api.contributions.getAll.useQuery(
    {
      organizationId: currentOrganization?.id || '',
    },
    {
      enabled: !!currentOrganization?.id,
    }
  )

  if (!currentOrganization) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto" />
          <p className="text-muted-foreground">Loading organization...</p>
        </div>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto" />
          <p className="text-muted-foreground">Loading contributions...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-section">
      {/* Page Header */}
      <div className="responsive-flex">
        <div>
          <h1 className="text-h1">Contributions</h1>
          <p className="text-muted-foreground">
            Manage contributions for {currentOrganization.name}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button asChild>
            <Link href={`/${currentOrganization.slug}/contributions/new`}>
              <Plus className="h-4 w-4 mr-2" />
              New Contribution
            </Link>
          </Button>
        </div>
      </div>

      {/* Contributions Content */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Contribution Records
          </CardTitle>
          <CardDescription>
            Track and manage all contribution transactions
          </CardDescription>
        </CardHeader>
        <CardContent>
          {!contributionsData?.contributions?.length ? (
            <div className="text-center py-12">
              <CreditCard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No contributions yet</h3>
              <p className="text-muted-foreground mb-4">
                Start by creating your first contribution record
              </p>
              <Button asChild>
                <Link href={`/${currentOrganization.slug}/contributions/new`}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Contribution
                </Link>
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                {contributionsData.total} contribution{contributionsData.total !== 1 ? 's' : ''} found
              </p>
              {/* TODO: Add contribution list/table component here */}
              <div className="text-center py-8 text-muted-foreground">
                Contribution list component will be implemented next
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
