'use client'

import { MemberManagement } from '@/components/organization/MemberManagement'
import { useOrganization } from '@/contexts/OrganizationContext'

export default function MembersPage() {
  const { currentOrganization } = useOrganization()

  if (!currentOrganization) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto" />
          <p className="text-muted-foreground">Loading members...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-section">
      <MemberManagement />
    </div>
  )
}
