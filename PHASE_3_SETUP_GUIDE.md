# Phase 3 Setup Guide: Testing the Advanced Invitation System

## 🚀 Quick Start

### 1. Database Setup

**Option A: Local PostgreSQL**
```bash
# Install PostgreSQL (macOS)
brew install postgresql
brew services start postgresql

# Create database
createdb koolek_db

# Update .env with your database URL
DATABASE_URL="postgresql://username:password@localhost:5432/koolek_db"
```

**Option B: Docker PostgreSQL**
```bash
# Run PostgreSQL in Docker
docker run --name koolek-postgres \
  -e POSTGRES_DB=koolek_db \
  -e POSTGRES_USER=koolek \
  -e POSTGRES_PASSWORD=password \
  -p 5432:5432 \
  -d postgres:15

# Update .env
DATABASE_URL="postgresql://koolek:password@localhost:5432/koolek_db"
```

### 2. Environment Configuration

Update your `.env` file with the following:

```env
# Database (Required)
DATABASE_URL="postgresql://username:password@localhost:5432/koolek_db"

# NextAuth.js (Required)
NEXTAUTH_SECRET="your-super-secret-key-here-min-32-chars-long"
NEXTAUTH_URL="http://localhost:3002"

# Email Service (Required for invitations)
RESEND_API_KEY="re_your-resend-api-key"
FROM_EMAIL="<EMAIL>"

# App Configuration
NEXT_PUBLIC_APP_URL="http://localhost:3002"

# Optional: OAuth
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
```

### 3. Database Migration

```bash
# Generate Prisma client
npx prisma generate

# Push schema to database
npx prisma db push

# Seed with demo data
npm run db:seed
```

### 4. Install Dependencies

```bash
# Install new dependencies
npm install --legacy-peer-deps

# Or if you prefer yarn
yarn install
```

### 5. Start Development Server

```bash
npm run dev
# or
yarn dev
```

Visit `http://localhost:3002` to see the application.

## 🧪 Testing the New Features

### 1. Invitation Management Dashboard

**Access**: Navigate to `/[orgSlug]/members/invitations`

**Test Features**:
- ✅ Send single invitations
- ✅ Upload CSV for bulk invitations
- ✅ Filter by status and role
- ✅ Resend/revoke invitations
- ✅ Real-time status updates

**Sample CSV for Testing**:
```csv
email,role,message
<EMAIL>,MEMBER,Welcome to our team!
<EMAIL>,ADMIN,Looking forward to working with you
<EMAIL>,VIEWER,
```

### 2. Bulk Invitation Flow

**Test Steps**:
1. Click "Bulk Invite" button
2. Download the CSV template
3. Upload a CSV file with test emails
4. Review the validation results
5. Send invitations
6. Check the results summary

### 3. Invitation Acceptance

**Test Flow**:
1. Send an invitation to a test email
2. Copy the invitation token from the database
3. Visit `/invitations/accept?token=YOUR_TOKEN`
4. Test both authenticated and unauthenticated flows

### 4. Member Management

**Access**: Navigate to `/[orgSlug]/members`

**Test Features**:
- ✅ View all organization members
- ✅ Change member roles
- ✅ Remove members (except owners)
- ✅ Search and filter members

### 5. User Onboarding

**Test Flow**:
1. Create a new organization
2. The onboarding flow should appear automatically
3. Navigate through all steps
4. Test skip functionality
5. Check progress tracking

### 6. Webhook System

**Access**: Navigate to `/[orgSlug]/settings/webhooks`

**Test Features**:
- ✅ Create webhook endpoints
- ✅ Subscribe to events
- ✅ Test webhook delivery
- ✅ View delivery logs

## 🔧 Development Tools

### Prisma Studio
```bash
npx prisma studio
```
Access at `http://localhost:5555` to view and edit database records.

### Database Reset
```bash
# Reset database and reseed
npx prisma migrate reset
npm run db:seed
```

### Type Checking
```bash
npm run type-check
```

### Linting
```bash
npm run lint
```

## 📊 Testing Checklist

### Core Invitation Features
- [ ] Single invitation creation
- [ ] Bulk CSV upload
- [ ] Email validation
- [ ] Role assignment
- [ ] Invitation expiration
- [ ] Resend functionality
- [ ] Revoke functionality

### User Experience
- [ ] Responsive design on mobile
- [ ] Loading states
- [ ] Error handling
- [ ] Success notifications
- [ ] Form validation

### Security
- [ ] Organization data isolation
- [ ] Permission-based access
- [ ] Token validation
- [ ] CSRF protection

### Performance
- [ ] Fast page loads
- [ ] Efficient database queries
- [ ] Proper caching
- [ ] Optimistic updates

## 🐛 Common Issues & Solutions

### Database Connection Issues
```bash
# Check if PostgreSQL is running
brew services list | grep postgresql

# Restart PostgreSQL
brew services restart postgresql
```

### Prisma Client Issues
```bash
# Regenerate Prisma client
npx prisma generate

# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install --legacy-peer-deps
```

### Email Service Issues
- Ensure RESEND_API_KEY is valid
- Check FROM_EMAIL domain verification
- Test with a simple email first

### TypeScript Errors
```bash
# Check for type errors
npm run type-check

# Clear Next.js cache
rm -rf .next
npm run dev
```

## 📈 Performance Monitoring

### Key Metrics to Watch
- Page load times (target: <200ms)
- Database query performance
- Email delivery success rate
- User onboarding completion rate

### Monitoring Tools
- Browser DevTools Network tab
- Prisma query logging
- Next.js built-in analytics
- Custom metrics in components

## 🎯 Next Steps

After testing Phase 3:

1. **Gather Feedback**: Test with real users
2. **Performance Optimization**: Identify bottlenecks
3. **Bug Fixes**: Address any issues found
4. **Documentation**: Update user guides
5. **Phase 4 Planning**: Advanced features and integrations

## 📞 Support

If you encounter issues:

1. Check the console for error messages
2. Verify environment variables
3. Ensure database is running
4. Check network connectivity
5. Review the implementation summary

**Happy Testing! 🚀**
