# KooLek Standalone - Quick Start Guide

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. **Navigate to the project directory:**
   ```bash
   cd koolek-standalone
   ```

2. **Run the setup script (recommended):**
   ```bash
   chmod +x setup.sh
   ./setup.sh
   ```

   **Or install manually:**
   ```bash
   npm install
   mkdir -p public/uploads
   ```

3. **Start the development server:**
   ```bash
   npm run dev
   ```

4. **Open your browser:**
   Navigate to [http://localhost:3002](http://localhost:3002)

## 🔐 Demo Login Credentials

### Admin Account
- **Email:** <EMAIL>
- **Password:** admin123
- **Access:** Full system access including user management, verification, and settings

### User Account
- **Email:** <EMAIL>
- **Password:** user123
- **Access:** Basic user features (contributions, expenses, personal dashboard)

## 📱 Features Overview

### For All Users
- **Dashboard:** Financial overview with summary cards and recent transactions
- **Contributions:** Submit contribution requests with file attachments
- **Expenses:** Track business expenses with receipt uploads
- **Mobile Responsive:** Optimized for both desktop and mobile devices

### For Managers/Admins
- **Pending Verification:** Approve or reject contribution requests
- **Collections:** Monitor contribution collections from staff
- **Reports:** Financial summaries and analytics

### For Admins Only
- **User Management:** Add, edit, and manage staff members
- **Settings:** System configuration and year setup
- **Access Control:** Role-based permission management

## 🎯 Key Features

### Contribution Processing
- Submit contribution requests with reference numbers
- File attachment support for receipts
- Real-time status tracking (pending, verified, rejected)
- Admin approval workflow

### Expense Management
- Create and manage expense records
- Upload receipt attachments (images, PDFs)
- Categorize expenses by type
- Year-based organization

### User Management
- Staff profile management
- Role assignment (Admin, Manager, User)
- Monthly contribution tracking
- Department organization

### Dashboard Analytics
- Total collection summaries
- Expense tracking
- Pending amount monitoring
- Recent transaction history

## 🛠️ Development

### Available Scripts
```bash
npm run dev     # Start development server (port 3002)
npm run build   # Build for production
npm run start   # Start production server
npm run lint    # Run ESLint
```

### Project Structure
```
koolek-standalone/
├── src/
│   ├── components/         # Reusable UI components
│   ├── pages/             # Next.js pages
│   ├── store/             # Redux store and slices
│   ├── services/          # API services
│   ├── utils/             # Utility functions
│   ├── contexts/          # React contexts
│   └── styles/            # Global styles
├── public/                # Static assets
└── ...config files
```

### Technology Stack
- **Frontend:** Next.js 14, React 18
- **UI Framework:** Material-UI (MUI), Tailwind CSS
- **State Management:** Redux Toolkit
- **Forms:** Formik with Yup validation
- **HTTP Client:** Axios
- **Date Handling:** Day.js, Moment.js

## 🔧 Configuration

### Environment Variables
The application uses the following environment variables (configured in `.env.local`):

```env
NEXT_PUBLIC_API_URL=http://localhost:3001/api
NEXTAUTH_SECRET=your-secret-key-here
NEXTAUTH_URL=http://localhost:3002
```

### Mock Data
The application currently uses mock data for demonstration purposes. All API calls are simulated through the `apiService` class.

## 📊 Demo Data

The application comes with pre-configured demo data including:
- Sample users with different roles
- Mock contribution transactions
- Example expense records
- Financial summaries

## 🚀 Production Deployment

1. **Build the application:**
   ```bash
   npm run build
   ```

2. **Start the production server:**
   ```bash
   npm run start
   ```

3. **Configure environment variables** for your production environment

## 🔒 Security Features

- JWT-based authentication
- Role-based access control
- Protected routes
- Input validation
- File upload restrictions

## 📱 Mobile Support

The application is fully responsive with:
- Mobile-optimized layouts
- Touch-friendly interfaces
- Floating action buttons
- Responsive tables and forms

## 🆘 Troubleshooting

### Common Issues

1. **Port 3002 already in use:**
   ```bash
   # Kill the process using port 3002
   lsof -ti:3002 | xargs kill -9
   ```

2. **Dependencies installation fails:**
   ```bash
   # Clear npm cache and reinstall
   npm cache clean --force
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **Build errors:**
   ```bash
   # Check Node.js version (requires 18+)
   node -v
   ```

## 📞 Support

For issues or questions:
1. Check the console for error messages
2. Verify all dependencies are installed correctly
3. Ensure Node.js version is 18 or higher
4. Check that port 3002 is available

## 🎉 Next Steps

1. Explore the different user roles and their capabilities
2. Test the contribution and expense workflows
3. Try the admin verification process
4. Customize the application for your specific needs

Happy exploring! 🚀
