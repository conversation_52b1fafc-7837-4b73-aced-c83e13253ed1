# Database (Docker PostgreSQL)
DATABASE_URL="postgresql://koolek_user:koolek_password@localhost:5433/koolek_db"

# NextAuth.js v5 Configuration
NEXTAUTH_SECRET="your-super-secret-key-here-min-32-chars"
NEXTAUTH_URL="http://localhost:3002"

# OAuth Providers (Optional but recommended)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Email Service (Required for invitations and password reset)
RESEND_API_KEY="re_your-resend-api-key"
FROM_EMAIL="<EMAIL>"

# App Configuration
NEXT_PUBLIC_APP_URL="http://localhost:3002"

# Redis (Docker Redis for caching and rate limiting)
REDIS_URL="redis://localhost:6380"

# Optional: Sentry (for error monitoring)
SENTRY_DSN=""

# Optional: Analytics
NEXT_PUBLIC_ANALYTICS_ID=""
