'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { api } from '@/components/providers/TRPCProvider'

export interface Organization {
  id: string
  name: string
  slug: string
  domain?: string
  settings: Record<string, any>
  metadata: Record<string, any>
  createdAt: Date
  updatedAt: Date
  role: 'OWNER' | 'ADMIN' | 'MEMBER' | 'VIEWER'
  joinedAt: Date
  memberCount: number
  contributionCount: number
  expenseCount: number
}

interface OrganizationContextType {
  currentOrganization: Organization | null
  organizations: Organization[]
  isLoading: boolean
  error: string | null
  switchOrganization: (organizationId: string) => Promise<void>
  refreshOrganizations: () => Promise<void>
  createOrganization: (data: { name: string; slug: string; domain?: string }) => Promise<Organization>
}

const OrganizationContext = createContext<OrganizationContextType | undefined>(undefined)

export function useOrganization() {
  const context = useContext(OrganizationContext)
  if (context === undefined) {
    throw new Error('useOrganization must be used within an OrganizationProvider')
  }
  return context
}

interface OrganizationProviderProps {
  children: React.ReactNode
}

export function OrganizationProvider({ children }: OrganizationProviderProps) {
  const { data: session, status } = useSession()
  const [currentOrganization, setCurrentOrganization] = useState<Organization | null>(null)
  const [organizations, setOrganizations] = useState<Organization[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch user's organizations
  const { data: userOrganizations, isLoading: isLoadingOrgs, error: orgsError, refetch } = api.organization.getUserOrganizations.useQuery(
    undefined,
    {
      enabled: !!session?.user,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  )

  // Create organization mutation
  const createOrgMutation = api.organization.create.useMutation({
    onSuccess: (newOrg) => {
      setOrganizations(prev => [newOrg, ...prev])
      setCurrentOrganization(newOrg)
      // Store in localStorage as fallback
      localStorage.setItem('currentOrganizationId', newOrg.id)
    },
    onError: (error) => {
      setError(error.message)
    }
  })

  // Update organizations when data changes
  useEffect(() => {
    if (userOrganizations) {
      setOrganizations(userOrganizations)

      // Only set current organization from session or localStorage, not automatically
      let targetOrg: Organization | null = null

      // Priority 1: Session-based organization (NextAuth.js v5)
      if (session?.user?.currentOrganizationId) {
        targetOrg = userOrganizations.find(org => org.id === session.user.currentOrganizationId) || null
      }

      // Priority 2: localStorage fallback
      if (!targetOrg) {
        const storedOrgId = localStorage.getItem('currentOrganizationId')
        if (storedOrgId) {
          targetOrg = userOrganizations.find(org => org.id === storedOrgId) || null
        }
      }

      // Don't automatically select first organization - let user choose
      // This prevents automatic redirects to demo organizations

      setCurrentOrganization(targetOrg)

      // Update session if we have a target org but session doesn't match
      if (targetOrg && session?.user?.currentOrganizationId !== targetOrg.id && session?.update) {
        session.update({ organizationId: targetOrg.id }).catch(console.error)
      }
    }
  }, [userOrganizations, session?.user?.currentOrganizationId])

  // Update loading state
  useEffect(() => {
    setIsLoading(isLoadingOrgs)
  }, [isLoadingOrgs])

  // Update error state
  useEffect(() => {
    setError(orgsError?.message || null)
  }, [orgsError])

  const switchOrganization = async (organizationId: string) => {
    const targetOrg = organizations.find(org => org.id === organizationId)
    if (targetOrg) {
      setCurrentOrganization(targetOrg)

      // Update session with new organization context using NextAuth.js v5 async APIs
      if (session?.update) {
        try {
          await session.update({ organizationId })
          // Store in localStorage as backup for session persistence
          localStorage.setItem('currentOrganizationId', organizationId)
        } catch (error) {
          console.error('Failed to update session:', error)
          // Fallback to localStorage if session update fails
          localStorage.setItem('currentOrganizationId', organizationId)
        }
      } else {
        // Fallback to localStorage if no session update method
        localStorage.setItem('currentOrganizationId', organizationId)
      }
    } else {
      setError('Organization not found')
    }
  }

  const refreshOrganizations = async () => {
    try {
      await refetch()
      setError(null)
    } catch (err) {
      setError('Failed to refresh organizations')
    }
  }

  const createOrganization = async (data: { name: string; slug: string; domain?: string }) => {
    try {
      setError(null)
      const newOrg = await createOrgMutation.mutateAsync(data)
      return newOrg
    } catch (err) {
      throw err
    }
  }

  const value: OrganizationContextType = {
    currentOrganization,
    organizations,
    isLoading,
    error,
    switchOrganization,
    refreshOrganizations,
    createOrganization,
  }

  return (
    <OrganizationContext.Provider value={value}>
      {children}
    </OrganizationContext.Provider>
  )
}

// Hook to get current organization ID for API calls
export function useCurrentOrganizationId(): string | null {
  const { currentOrganization } = useOrganization()
  return currentOrganization?.id || null
}

// Hook to check if user has specific role in current organization
export function useOrganizationRole(): 'OWNER' | 'ADMIN' | 'MEMBER' | 'VIEWER' | null {
  const { currentOrganization } = useOrganization()
  return currentOrganization?.role || null
}

// Hook to check if user has admin permissions
export function useIsOrganizationAdmin(): boolean {
  const role = useOrganizationRole()
  return role === 'OWNER' || role === 'ADMIN'
}
