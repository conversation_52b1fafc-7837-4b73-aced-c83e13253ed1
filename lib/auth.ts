import { NextAuthConfig } from 'next-auth'
import { PrismaAdapter } from '@auth/prisma-adapter'
import CredentialsProvider from 'next-auth/providers/credentials'
import GoogleProvider from 'next-auth/providers/google'
import { db } from '@/lib/db'
import { compare } from 'bcryptjs'
import { cookies } from 'next/headers'

declare module 'next-auth' {
  interface Session {
    user: {
      id: string
      email: string
      name?: string | null
      image?: string | null
      currentOrganizationId?: string | null
    }
    sessionId?: string
    lastActivity?: Date
    securityFlags?: {
      requiresReauth?: boolean
      suspiciousActivity?: boolean
    }
  }

  interface User {
    id: string
    email: string
    name?: string | null
    image?: string | null
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    sub: string
    currentOrganizationId?: string | null
    sessionId?: string
    lastActivity?: number
    securityFlags?: {
      requiresReauth?: boolean
      suspiciousActivity?: boolean
    }
  }
}

import NextAuth from 'next-auth'

export const authConfig: NextAuthConfig = {
  adapter: PrismaAdapter(db),
  session: {
    strategy: 'jwt',
    maxAge: 7 * 24 * 60 * 60, // 7 days (reduced for security)
    updateAge: 24 * 60 * 60, // Update session every 24 hours
  },
  cookies: {
    sessionToken: {
      name: `${process.env.NODE_ENV === 'production' ? '__Secure-' : ''}next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      },
    },
    callbackUrl: {
      name: `${process.env.NODE_ENV === 'production' ? '__Secure-' : ''}next-auth.callback-url`,
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      },
    },
    csrfToken: {
      name: `${process.env.NODE_ENV === 'production' ? '__Host-' : ''}next-auth.csrf-token`,
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      },
    },
  },
  pages: {
    signIn: '/signin',
    signUp: '/signup',
    error: '/auth/error',
    verifyRequest: '/auth/verify-request',
    newUser: '/auth/new-user',
  },
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      allowDangerousEmailAccountLinking: true,
    }),
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        const user = await db.user.findUnique({
          where: {
            email: credentials.email,
          },
          include: {
            organizations: {
              include: {
                organization: true,
              },
              orderBy: {
                joinedAt: 'desc',
              },
            },
          },
        })

        if (!user || !user.password) {
          return null
        }

        const isPasswordValid = await compare(credentials.password, user.password)

        if (!isPasswordValid) {
          return null
        }

        // Update last login
        await db.user.update({
          where: { id: user.id },
          data: { lastLoginAt: new Date() },
        })

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          image: user.image,
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user, trigger, session }) {
      if (user) {
        token.sub = user.id
        token.sessionId = crypto.randomUUID()
        token.lastActivity = Date.now()
      }

      // Update last activity on each request
      token.lastActivity = Date.now()

      // Handle organization switching with Next.js 15 async APIs
      if (trigger === 'update' && session?.organizationId) {
        token.currentOrganizationId = session.organizationId
      }

      // Note: We can't load organization from database here because
      // JWT callback runs in Edge Runtime where Prisma is not available.
      // Organization selection will be handled in the OrganizationContext.

      return token
    },
    async session({ session, token }) {
      if (token.sub) {
        session.user.id = token.sub
        session.user.currentOrganizationId = token.currentOrganizationId
        session.sessionId = token.sessionId
        session.lastActivity = token.lastActivity ? new Date(token.lastActivity) : undefined
      }
      return session
    },
    async signIn({ user, account, profile, email, credentials }) {
      // Handle email verification for OAuth providers
      if (account?.provider === 'google') {
        // Auto-verify email for Google OAuth
        if (user.email && !user.emailVerified) {
          await db.user.update({
            where: { email: user.email },
            data: { emailVerified: new Date() },
          })
        }
      }

      return true
    },
  },
  // events: {
  //   async signIn({ user, account, profile, isNewUser }) {
  //     // Log successful sign-in
  //     await db.auditLog.create({
  //       data: {
  //         userId: user.id,
  //         action: 'SIGN_IN',
  //         resource: 'USER',
  //         resourceId: user.id,
  //         details: {
  //           provider: account?.provider,
  //           isNewUser,
  //         },
  //       },
  //     })
  //   },
  //   async signOut({ session, token }) {
  //     // Log sign-out
  //     if (token?.sub) {
  //       await db.auditLog.create({
  //         data: {
  //           userId: token.sub,
  //           action: 'SIGN_OUT',
  //           resource: 'USER',
  //           resourceId: token.sub,
  //         },
  //       })
  //     }
  //   },
  // },
}

export const { auth, handlers, signIn, signOut } = NextAuth(authConfig)
