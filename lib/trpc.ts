import { initTR<PERSON>, TRPCError } from '@trpc/server'
import { type CreateNextContextOptions } from '@trpc/server/adapters/next'
import { type Session } from 'next-auth'
import { auth } from '@/lib/auth'
import superjson from 'superjson'
import { ZodError } from 'zod'
import { OrganizationRole } from '@prisma/client'

import { db } from '@/lib/db'
import { hasPermission, Permission } from '@/lib/permissions'

type CreateContextOptions = {
  session: Session | null
  req?: any
  res?: any
  userAgent?: string
  ipAddress?: string
}

const createInnerTRPCContext = (opts: CreateContextOptions) => {
  return {
    session: opts.session,
    db,
    req: opts.req,
    res: opts.res,
    userAgent: opts.userAgent,
    ipAddress: opts.ipAddress,
  }
}

/**
 * Extract client IP address from request headers
 */
function getClientIP(req: any): string {
  const forwarded = req.headers['x-forwarded-for']
  const realIP = req.headers['x-real-ip']
  const remoteAddress = req.connection?.remoteAddress || req.socket?.remoteAddress

  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  if (realIP) {
    return realIP
  }
  return remoteAddress || 'unknown'
}

export const createTRPCContext = async (opts: CreateNextContextOptions) => {
  const { req, res } = opts

  // Get the session from the server using NextAuth.js v5 auth function
  const session = await auth()

  // Extract request metadata for audit logging
  const userAgent = req.headers['user-agent'] || 'unknown'
  const ipAddress = getClientIP(req)

  return createInnerTRPCContext({
    session,
    req,
    res,
    userAgent,
    ipAddress,
  })
}

const t = initTRPC.context<typeof createTRPCContext>().create({
  transformer: superjson,
  errorFormatter({ shape, error }) {
    return {
      ...shape,
      data: {
        ...shape.data,
        zodError:
          error.cause instanceof ZodError ? error.cause.flatten() : null,
      },
    }
  },
})

export const createTRPCRouter = t.router

export const publicProcedure = t.procedure

const enforceUserIsAuthed = t.middleware(({ ctx, next }) => {
  if (!ctx.session || !ctx.session.user) {
    throw new TRPCError({ code: 'UNAUTHORIZED' })
  }
  return next({
    ctx: {
      // infers the `session` as non-nullable
      session: { ...ctx.session, user: ctx.session.user },
      db: ctx.db,
      req: ctx.req,
      res: ctx.res,
    },
  })
})

export const protectedProcedure = t.procedure.use(enforceUserIsAuthed)

// Organization-scoped procedure with role validation
const enforceOrganizationAccess = t.middleware(async ({ ctx, next }) => {
  if (!ctx.session?.user?.id) {
    throw new TRPCError({ code: 'UNAUTHORIZED' })
  }

  // We'll validate organization access at the procedure level
  // This middleware just ensures authentication
  return next({
    ctx: {
      ...ctx,
    },
  })
})

export const orgProcedure = protectedProcedure.use(enforceOrganizationAccess)

// Helper function to validate organization access and permissions
export async function validateOrganizationAccess(
  ctx: any,
  organizationId: string,
  permission: Permission
) {
  // Verify user has access to this organization
  const userOrganization = await ctx.db.userOrganization.findFirst({
    where: {
      userId: ctx.session.user.id,
      organizationId,
    },
    include: {
      organization: true,
    },
  })

  if (!userOrganization) {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'Access denied to this organization',
    })
  }

  // Check permissions
  if (!hasPermission(userOrganization.role, permission)) {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: `Insufficient permissions: ${permission}`,
    })
  }

  return {
    organization: userOrganization.organization,
    userRole: userOrganization.role,
    userOrganization,
  }
}

// Permission-based procedure that validates organization access after input parsing
export const createPermissionProcedure = (permission: Permission) => {
  return protectedProcedure.use(async ({ ctx, input, next }) => {
    // Extract organization ID from input or session
    const organizationId = (input as any)?.organizationId || ctx.session.user.currentOrganizationId

    if (!organizationId) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Organization ID is required',
      })
    }

    // Validate organization access and permissions
    const orgData = await validateOrganizationAccess(ctx, organizationId, permission)

    return next({
      ctx: {
        ...ctx,
        organization: orgData.organization,
        userRole: orgData.userRole,
        userOrganization: orgData.userOrganization,
        organizationId: orgData.organization.id
      },
    })
  })
}

// Enhanced audit logging middleware
const auditLogger = t.middleware(async ({ ctx, path, type, input, next }) => {
  const startTime = Date.now()
  const result = await next()
  const duration = Date.now() - startTime

  // Log all operations (successful and failed) for security monitoring
  if (ctx.session?.user?.id) {
    try {
      const organizationId = (ctx as any).organization?.id ||
                           ctx.session.user.currentOrganizationId ||
                           (input as any)?.organizationId

      await ctx.db.auditLog.create({
        data: {
          userId: ctx.session.user.id,
          organizationId: organizationId || null,
          action: `${type.toUpperCase()}_${path.replace(/\./g, '_').toUpperCase()}`,
          resource: path.split('.')[0]?.toUpperCase() || 'UNKNOWN',
          resourceId: (input as any)?.id || (input as any)?.organizationId || null,
          details: {
            path,
            type,
            input: type === 'mutation' ? sanitizeInput(input) : undefined,
            success: result.ok,
            error: result.ok ? undefined : result.error?.message,
            duration,
            timestamp: new Date().toISOString(),
          } as any,
          ipAddress: (ctx as any).ipAddress || 'unknown',
          userAgent: (ctx as any).userAgent || 'unknown',
        },
      })
    } catch (error) {
      // Don't fail the request if audit logging fails, but log the error
      console.error('Audit logging failed:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        path,
        type,
        userId: ctx.session.user.id,
      })
    }
  }

  return result
})

// Sanitize input for audit logging (remove sensitive data)
function sanitizeInput(input: any): any {
  if (!input) return null
  if (typeof input !== 'object') return input

  const sanitized = { ...input }

  // Remove sensitive fields
  const sensitiveFields = ['password', 'token', 'secret', 'key', 'credential']
  sensitiveFields.forEach(field => {
    if (field in sanitized) {
      sanitized[field] = '[REDACTED]'
    }
  })

  return sanitized
}

export const auditedProcedure = protectedProcedure
// Temporarily disabled: .use(auditLogger)
export const auditedOrgProcedure = orgProcedure
// Temporarily disabled: .use(auditLogger)

// Audited permission-based procedure
export const createAuditedPermissionProcedure = (permission: Permission) => {
  return auditedOrgProcedure.use(({ ctx, next }) => {
    if (!hasPermission(ctx.userRole, permission)) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: `Insufficient permissions: ${permission}`,
      })
    }

    return next({
      ctx,
    })
  })
}
