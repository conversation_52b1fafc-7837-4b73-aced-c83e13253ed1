import { OrganizationRole } from '@prisma/client'

// Define all possible permissions in the system
export const PERMISSIONS = {
  // Organization management
  ORGANIZATION_READ: 'organization:read',
  ORGANIZATION_UPDATE: 'organization:update',
  ORGANIZATION_DELETE: 'organization:delete',
  ORGANIZATION_SETTINGS: 'organization:settings',
  ORGANIZATION_TRANSFER: 'organization:transfer',

  // Member management
  MEMBERS_READ: 'members:read',
  MEMBERS_INVITE: 'members:invite',
  MEMBERS_UPDATE: 'members:update',
  MEMBERS_REMOVE: 'members:remove',

  // Contribution management
  CONTRIBUTIONS: {
    READ: 'contributions:read',
    CREATE: 'contributions:create',
    UPDATE: 'contributions:update',
    DELETE: 'contributions:delete',
    VERIFY: 'contributions:verify',
  },

  // Expense management
  EXPENSES_READ: 'expenses:read',
  EXPENSES_CREATE: 'expenses:create',
  EXPENSES_UPDATE: 'expenses:update',
  EXPENSES_DELETE: 'expenses:delete',
  EXPENSES_APPROVE: 'expenses:approve',

  // Collection management
  COLLECTIONS_READ: 'collections:read',
  COLLECTIONS_CREATE: 'collections:create',
  COLLECTIONS_UPDATE: 'collections:update',
  COLLECTIONS_DELETE: 'collections:delete',

  // Category management
  CATEGORIES_READ: 'categories:read',
  CATEGORIES_CREATE: 'categories:create',
  CATEGORIES_UPDATE: 'categories:update',
  CATEGORIES_DELETE: 'categories:delete',

  // Reports and analytics
  REPORTS_READ: 'reports:read',
  REPORTS_EXPORT: 'reports:export',

  // Audit logs
  AUDIT_LOGS_READ: 'audit_logs:read',
} as const

export type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS]

// Define role-based permissions
export const ROLE_PERMISSIONS: Record<OrganizationRole, Permission[]> = {
  OWNER: [
    // Organization permissions
    PERMISSIONS.ORGANIZATION_READ,
    PERMISSIONS.ORGANIZATION_UPDATE,
    PERMISSIONS.ORGANIZATION_DELETE,
    PERMISSIONS.ORGANIZATION_SETTINGS,
    PERMISSIONS.ORGANIZATION_TRANSFER,

    // Member permissions
    PERMISSIONS.MEMBERS_READ,
    PERMISSIONS.MEMBERS_INVITE,
    PERMISSIONS.MEMBERS_UPDATE,
    PERMISSIONS.MEMBERS_REMOVE,

    // Contribution permissions
    PERMISSIONS.CONTRIBUTIONS.READ,
    PERMISSIONS.CONTRIBUTIONS.CREATE,
    PERMISSIONS.CONTRIBUTIONS.UPDATE,
    PERMISSIONS.CONTRIBUTIONS.DELETE,
    PERMISSIONS.CONTRIBUTIONS.VERIFY,

    // Expense permissions
    PERMISSIONS.EXPENSES_READ,
    PERMISSIONS.EXPENSES_CREATE,
    PERMISSIONS.EXPENSES_UPDATE,
    PERMISSIONS.EXPENSES_DELETE,
    PERMISSIONS.EXPENSES_APPROVE,

    // Collection permissions
    PERMISSIONS.COLLECTIONS_READ,
    PERMISSIONS.COLLECTIONS_CREATE,
    PERMISSIONS.COLLECTIONS_UPDATE,
    PERMISSIONS.COLLECTIONS_DELETE,

    // Category permissions
    PERMISSIONS.CATEGORIES_READ,
    PERMISSIONS.CATEGORIES_CREATE,
    PERMISSIONS.CATEGORIES_UPDATE,
    PERMISSIONS.CATEGORIES_DELETE,

    // Reports permissions
    PERMISSIONS.REPORTS_READ,
    PERMISSIONS.REPORTS_EXPORT,

    // Audit permissions
    PERMISSIONS.AUDIT_LOGS_READ,
  ],

  ADMIN: [
    // Organization permissions (limited)
    PERMISSIONS.ORGANIZATION_READ,
    PERMISSIONS.ORGANIZATION_UPDATE,
    PERMISSIONS.ORGANIZATION_SETTINGS,

    // Member permissions
    PERMISSIONS.MEMBERS_READ,
    PERMISSIONS.MEMBERS_INVITE,
    PERMISSIONS.MEMBERS_UPDATE,

    // Contribution permissions
    PERMISSIONS.CONTRIBUTIONS.READ,
    PERMISSIONS.CONTRIBUTIONS.CREATE,
    PERMISSIONS.CONTRIBUTIONS.UPDATE,
    PERMISSIONS.CONTRIBUTIONS.DELETE,
    PERMISSIONS.CONTRIBUTIONS.VERIFY,

    // Expense permissions
    PERMISSIONS.EXPENSES_READ,
    PERMISSIONS.EXPENSES_CREATE,
    PERMISSIONS.EXPENSES_UPDATE,
    PERMISSIONS.EXPENSES_DELETE,
    PERMISSIONS.EXPENSES_APPROVE,

    // Collection permissions
    PERMISSIONS.COLLECTIONS_READ,
    PERMISSIONS.COLLECTIONS_CREATE,
    PERMISSIONS.COLLECTIONS_UPDATE,
    PERMISSIONS.COLLECTIONS_DELETE,

    // Category permissions
    PERMISSIONS.CATEGORIES_READ,
    PERMISSIONS.CATEGORIES_CREATE,
    PERMISSIONS.CATEGORIES_UPDATE,
    PERMISSIONS.CATEGORIES_DELETE,

    // Reports permissions
    PERMISSIONS.REPORTS_READ,
    PERMISSIONS.REPORTS_EXPORT,

    // Audit permissions
    PERMISSIONS.AUDIT_LOGS_READ,
  ],

  MEMBER: [
    // Organization permissions (read-only)
    PERMISSIONS.ORGANIZATION_READ,

    // Member permissions (read-only)
    PERMISSIONS.MEMBERS_READ,

    // Contribution permissions
    PERMISSIONS.CONTRIBUTIONS.READ,
    PERMISSIONS.CONTRIBUTIONS.CREATE,
    PERMISSIONS.CONTRIBUTIONS.UPDATE, // Own contributions only

    // Expense permissions
    PERMISSIONS.EXPENSES_READ,
    PERMISSIONS.EXPENSES_CREATE,
    PERMISSIONS.EXPENSES_UPDATE, // Own expenses only

    // Collection permissions (read-only)
    PERMISSIONS.COLLECTIONS_READ,

    // Category permissions (read-only)
    PERMISSIONS.CATEGORIES_READ,

    // Reports permissions (limited)
    PERMISSIONS.REPORTS_READ,
  ],

  VIEWER: [
    // Organization permissions (read-only)
    PERMISSIONS.ORGANIZATION_READ,

    // Member permissions (read-only)
    PERMISSIONS.MEMBERS_READ,

    // Contribution permissions (read-only)
    PERMISSIONS.CONTRIBUTIONS.READ,

    // Expense permissions (read-only)
    PERMISSIONS.EXPENSES_READ,

    // Collection permissions (read-only)
    PERMISSIONS.COLLECTIONS_READ,

    // Category permissions (read-only)
    PERMISSIONS.CATEGORIES_READ,

    // Reports permissions (read-only)
    PERMISSIONS.REPORTS_READ,
  ],
}

// Helper functions for permission checking
export function hasPermission(role: OrganizationRole, permission: Permission): boolean {
  return ROLE_PERMISSIONS[role].includes(permission)
}

export function hasAnyPermission(role: OrganizationRole, permissions: Permission[]): boolean {
  return permissions.some(permission => hasPermission(role, permission))
}

export function hasAllPermissions(role: OrganizationRole, permissions: Permission[]): boolean {
  return permissions.every(permission => hasPermission(role, permission))
}

export function getRolePermissions(role: OrganizationRole): Permission[] {
  return ROLE_PERMISSIONS[role]
}

// Check if user can perform action on resource
export function canAccessResource(
  role: OrganizationRole,
  action: 'read' | 'create' | 'update' | 'delete',
  resource: 'contributions' | 'expenses' | 'collections' | 'categories' | 'members' | 'organization',
  isOwner?: boolean
): boolean {
  const permission = `${resource}:${action}` as Permission

  // Special case: members can update/delete their own resources
  if ((action === 'update' || action === 'delete') && isOwner && role === 'MEMBER') {
    if (resource === 'contributions' || resource === 'expenses') {
      return true
    }
  }

  return hasPermission(role, permission)
}

// Feature flags based on role
export function getFeatureAccess(role: OrganizationRole) {
  return {
    canManageOrganization: hasPermission(role, PERMISSIONS.ORGANIZATION_UPDATE),
    canInviteMembers: hasPermission(role, PERMISSIONS.MEMBERS_INVITE),
    canVerifyContributions: hasPermission(role, PERMISSIONS.CONTRIBUTIONS.VERIFY),
    canApproveExpenses: hasPermission(role, PERMISSIONS.EXPENSES_APPROVE),
    canManageCategories: hasPermission(role, PERMISSIONS.CATEGORIES_CREATE),
    canViewAuditLogs: hasPermission(role, PERMISSIONS.AUDIT_LOGS_READ),
    canExportReports: hasPermission(role, PERMISSIONS.REPORTS_EXPORT),
    canDeleteOrganization: hasPermission(role, PERMISSIONS.ORGANIZATION_DELETE),
  }
}
