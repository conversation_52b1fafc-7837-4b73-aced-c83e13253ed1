import { cookies } from 'next/headers'
import { db } from '@/lib/db'

/**
 * Session Security Utilities
 * Provides enhanced session management and security features
 */

export interface SessionSecurityOptions {
  invalidateAllSessions?: boolean
  reason?: string
  auditLog?: boolean
}

/**
 * Invalidate user sessions for security purposes
 */
export async function invalidateUserSessions(
  userId: string,
  options: SessionSecurityOptions = {}
) {
  const { invalidateAllSessions = false, reason = 'Security invalidation', auditLog = true } = options

  try {
    // Clear session cookies
    const cookieStore = await cookies()
    
    // Clear NextAuth session cookies
    cookieStore.delete('next-auth.session-token')
    cookieStore.delete('__Secure-next-auth.session-token')
    cookieStore.delete('next-auth.callback-url')
    cookieStore.delete('__Secure-next-auth.callback-url')
    cookieStore.delete('next-auth.csrf-token')
    cookieStore.delete('__Host-next-auth.csrf-token')
    
    // Clear organization context cookies
    cookieStore.delete('current-organization-id')

    // Log the session invalidation if requested
    if (auditLog) {
      await db.auditLog.create({
        data: {
          userId,
          action: 'SESSION_INVALIDATED',
          resource: 'USER',
          resourceId: userId,
          details: {
            reason,
            invalidateAllSessions,
            timestamp: new Date().toISOString(),
          },
        },
      })
    }

    return { success: true, reason }
  } catch (error) {
    console.error('Failed to invalidate user sessions:', error)
    return { success: false, error: 'Failed to invalidate sessions' }
  }
}

/**
 * Check if session is suspicious and should be invalidated
 */
export async function checkSessionSecurity(
  userId: string,
  sessionData: {
    ipAddress?: string
    userAgent?: string
    lastActivity?: Date
  }
): Promise<{
  isValid: boolean
  reason?: string
  shouldInvalidate: boolean
}> {
  try {
    // Get recent audit logs for this user
    const recentLogs = await db.auditLog.findMany({
      where: {
        userId,
        createdAt: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }, // Last 24 hours
      },
      orderBy: { createdAt: 'desc' },
      take: 50,
    })

    // Check for suspicious patterns
    const suspiciousPatterns = {
      multipleIPs: checkMultipleIPs(recentLogs),
      rapidRequests: checkRapidRequests(recentLogs),
      unusualActivity: checkUnusualActivity(recentLogs),
    }

    const shouldInvalidate = Object.values(suspiciousPatterns).some(Boolean)

    if (shouldInvalidate) {
      const reasons = Object.entries(suspiciousPatterns)
        .filter(([_, detected]) => detected)
        .map(([pattern]) => pattern)

      return {
        isValid: false,
        reason: `Suspicious activity detected: ${reasons.join(', ')}`,
        shouldInvalidate: true,
      }
    }

    return {
      isValid: true,
      shouldInvalidate: false,
    }
  } catch (error) {
    console.error('Failed to check session security:', error)
    return {
      isValid: true, // Fail open for availability
      shouldInvalidate: false,
    }
  }
}

/**
 * Check for multiple IP addresses in recent activity
 */
function checkMultipleIPs(logs: any[]): boolean {
  const uniqueIPs = new Set(
    logs
      .filter(log => log.ipAddress && log.ipAddress !== 'unknown')
      .map(log => log.ipAddress)
  )
  
  // Flag if more than 3 different IPs in 24 hours
  return uniqueIPs.size > 3
}

/**
 * Check for rapid successive requests
 */
function checkRapidRequests(logs: any[]): boolean {
  if (logs.length < 10) return false

  // Check if more than 50 requests in the last hour
  const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
  const recentRequests = logs.filter(log => log.createdAt > oneHourAgo)
  
  return recentRequests.length > 50
}

/**
 * Check for unusual activity patterns
 */
function checkUnusualActivity(logs: any[]): boolean {
  // Check for failed authentication attempts
  const failedAttempts = logs.filter(log => 
    log.action.includes('FAILED') || 
    log.details?.success === false
  )
  
  // Flag if more than 5 failed attempts in 24 hours
  return failedAttempts.length > 5
}

/**
 * Generate secure session token
 */
export function generateSecureToken(): string {
  return crypto.randomUUID() + '-' + Date.now().toString(36)
}

/**
 * Validate session token format
 */
export function validateTokenFormat(token: string): boolean {
  // Basic format validation for our tokens
  const tokenPattern = /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}-[a-z0-9]+$/
  return tokenPattern.test(token)
}

/**
 * Rate limiting for authentication attempts
 */
export async function checkAuthRateLimit(
  identifier: string, // email or IP
  windowMs: number = 15 * 60 * 1000, // 15 minutes
  maxAttempts: number = 5
): Promise<{
  allowed: boolean
  remaining: number
  resetTime: Date
}> {
  try {
    const windowStart = new Date(Date.now() - windowMs)
    
    // Count recent failed attempts
    const recentAttempts = await db.auditLog.count({
      where: {
        action: 'SIGN_IN_FAILED',
        details: {
          path: ['identifier'],
          equals: identifier,
        },
        createdAt: { gte: windowStart },
      },
    })

    const remaining = Math.max(0, maxAttempts - recentAttempts)
    const resetTime = new Date(Date.now() + windowMs)

    return {
      allowed: recentAttempts < maxAttempts,
      remaining,
      resetTime,
    }
  } catch (error) {
    console.error('Failed to check auth rate limit:', error)
    // Fail open for availability
    return {
      allowed: true,
      remaining: maxAttempts,
      resetTime: new Date(Date.now() + windowMs),
    }
  }
}

/**
 * Clean up expired sessions and audit logs
 */
export async function cleanupExpiredSessions(): Promise<{
  sessionsDeleted: number
  auditLogsDeleted: number
}> {
  try {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    const ninetyDaysAgo = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)

    // Clean up old audit logs (keep for 90 days)
    const auditLogsDeleted = await db.auditLog.deleteMany({
      where: {
        createdAt: { lt: ninetyDaysAgo },
      },
    })

    return {
      sessionsDeleted: 0, // NextAuth handles session cleanup
      auditLogsDeleted: auditLogsDeleted.count,
    }
  } catch (error) {
    console.error('Failed to cleanup expired sessions:', error)
    return {
      sessionsDeleted: 0,
      auditLogsDeleted: 0,
    }
  }
}
