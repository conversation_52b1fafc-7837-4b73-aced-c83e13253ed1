import { Resend } from 'resend'

const resend = new Resend(process.env.RESEND_API_KEY)

const FROM_EMAIL = process.env.FROM_EMAIL || '<EMAIL>'
const APP_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3002'

export async function sendVerificationEmail(email: string, name: string) {
  const verificationUrl = `${APP_URL}/auth/verify-email?email=${encodeURIComponent(email)}`

  try {
    await resend.emails.send({
      from: FROM_EMAIL,
      to: email,
      subject: 'Verify your Ko<PERSON>Lek account',
      html: `
        <!DOCTYPE html>
        <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Verify your KooLek account</title>
            <style>
              body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif; line-height: 1.6; color: #333; }
              .container { max-width: 600px; margin: 0 auto; padding: 20px; }
              .header { text-align: center; margin-bottom: 30px; }
              .logo { font-size: 24px; font-weight: bold; color: #2563eb; }
              .content { background: #f8fafc; padding: 30px; border-radius: 8px; margin: 20px 0; }
              .button { display: inline-block; background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; }
              .footer { text-align: center; margin-top: 30px; font-size: 14px; color: #666; }
            </style>
          </head>
          <body>
            <div class="container">
              <div class="header">
                <div class="logo">KooLek</div>
              </div>

              <div class="content">
                <h2>Welcome to KooLek, ${name}!</h2>
                <p>Thank you for signing up. To complete your registration and verify your email address, please click the button below:</p>

                <p style="text-align: center; margin: 30px 0;">
                  <a href="${verificationUrl}" class="button">Verify Email Address</a>
                </p>

                <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                <p style="word-break: break-all; color: #666;">${verificationUrl}</p>

                <p>This verification link will expire in 24 hours for security reasons.</p>
              </div>

              <div class="footer">
                <p>If you didn't create a KooLek account, you can safely ignore this email.</p>
                <p>&copy; ${new Date().getFullYear()} KooLek. All rights reserved.</p>
              </div>
            </div>
          </body>
        </html>
      `,
    })
  } catch (error) {
    console.error('Failed to send verification email:', error)
    throw new Error('Failed to send verification email')
  }
}

export async function sendPasswordResetEmail(email: string, name: string, token: string) {
  const resetUrl = `${APP_URL}/auth/reset-password?token=${token}`

  try {
    await resend.emails.send({
      from: FROM_EMAIL,
      to: email,
      subject: 'Reset your KooLek password',
      html: `
        <!DOCTYPE html>
        <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Reset your KooLek password</title>
            <style>
              body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
              .container { max-width: 600px; margin: 0 auto; padding: 20px; }
              .header { text-align: center; margin-bottom: 30px; }
              .logo { font-size: 24px; font-weight: bold; color: #2563eb; }
              .content { background: #f8fafc; padding: 30px; border-radius: 8px; margin: 20px 0; }
              .button { display: inline-block; background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; }
              .footer { text-align: center; margin-top: 30px; font-size: 14px; color: #666; }
              .warning { background: #fef2f2; border: 1px solid #fecaca; padding: 15px; border-radius: 6px; margin: 20px 0; }
            </style>
          </head>
          <body>
            <div class="container">
              <div class="header">
                <div class="logo">KooLek</div>
              </div>

              <div class="content">
                <h2>Password Reset Request</h2>
                <p>Hello ${name},</p>
                <p>We received a request to reset your KooLek account password. If you made this request, click the button below to reset your password:</p>

                <p style="text-align: center; margin: 30px 0;">
                  <a href="${resetUrl}" class="button">Reset Password</a>
                </p>

                <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                <p style="word-break: break-all; color: #666;">${resetUrl}</p>

                <div class="warning">
                  <strong>Security Notice:</strong>
                  <ul>
                    <li>This link will expire in 1 hour for security reasons</li>
                    <li>If you didn't request this reset, please ignore this email</li>
                    <li>Your password will remain unchanged until you create a new one</li>
                  </ul>
                </div>
              </div>

              <div class="footer">
                <p>If you didn't request a password reset, you can safely ignore this email.</p>
                <p>For security questions, contact our support team.</p>
                <p>&copy; ${new Date().getFullYear()} KooLek. All rights reserved.</p>
              </div>
            </div>
          </body>
        </html>
      `,
    })
  } catch (error) {
    console.error('Failed to send password reset email:', error)
    throw new Error('Failed to send password reset email')
  }
}

export async function sendInvitationEmail(
  email: string,
  organizationName: string,
  inviterName: string,
  token: string,
  role: string
) {
  const inviteUrl = `${APP_URL}/invitations/accept?token=${token}`

  try {
    await resend.emails.send({
      from: FROM_EMAIL,
      to: email,
      subject: `You're invited to join ${organizationName} on KooLek`,
      html: `
        <!DOCTYPE html>
        <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Invitation to join ${organizationName}</title>
            <style>
              body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
              .container { max-width: 600px; margin: 0 auto; padding: 20px; }
              .header { text-align: center; margin-bottom: 30px; }
              .logo { font-size: 24px; font-weight: bold; color: #2563eb; }
              .content { background: #f8fafc; padding: 30px; border-radius: 8px; margin: 20px 0; }
              .button { display: inline-block; background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; }
              .footer { text-align: center; margin-top: 30px; font-size: 14px; color: #666; }
              .role-badge { background: #e0e7ff; color: #3730a3; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500; }
            </style>
          </head>
          <body>
            <div class="container">
              <div class="header">
                <div class="logo">KooLek</div>
              </div>

              <div class="content">
                <h2>You're invited to join ${organizationName}</h2>
                <p>${inviterName} has invited you to join <strong>${organizationName}</strong> on KooLek as a <span class="role-badge">${role}</span>.</p>

                <p>KooLek helps teams manage KooLeknts, expenses, and financial collections efficiently. Join your team to get started!</p>

                <p style="text-align: center; margin: 30px 0;">
                  <a href="${inviteUrl}" class="button">Accept Invitation</a>
                </p>

                <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                <p style="word-break: break-all; color: #666;">${inviteUrl}</p>

                <p><strong>Note:</strong> This invitation will expire in 7 days.</p>
              </div>

              <div class="footer">
                <p>If you weren't expecting this invitation, you can safely ignore this email.</p>
                <p>&copy; ${new Date().getFullYear()} KooLek. All rights reserved.</p>
              </div>
            </div>
          </body>
        </html>
      `,
    })
  } catch (error) {
    console.error('Failed to send invitation email:', error)
    throw new Error('Failed to send invitation email')
  }
}
