# KooLek Multi-Tenant SaaS - Quick Reference

## 🚀 Current Status
- **Phase**: Planning & Architecture Complete
- **Next Step**: Phase 1 Implementation (Core Multi-Tenancy)
- **Architecture**: Next.js 15 + React 19 + Multi-Tenant SaaS

## 🎯 Key Objectives
1. Transform single-org app to multi-tenant SaaS
2. Implement organization-level data isolation
3. Create user invitation system
4. Support multi-organization users
5. Enterprise-grade security & performance

## 🛠️ Tech Stack (Latest 2024)
```
Frontend: Next.js 15 + React 19 + TypeScript 5+
State: Zustand + TanStack Query v5
Styling: Tailwind CSS 4 + Radix UI
API: tRPC (type-safe) + Zod validation
Auth: NextAuth.js v5 (async APIs)
Database: PostgreSQL 15+ + Prisma ORM
Cache: Upstash Redis (edge-compatible)
Dev: Turbopack (76% faster) + ESLint 9
```

## 📋 Implementation Phases

### Phase 1: Core Multi-Tenancy (Current)
```typescript
Priority Tasks:
- [ ] Prisma schema with organization models
- [ ] Organization context providers
- [ ] Organization selector component
- [ ] tRPC organization router
- [ ] Data migration scripts
```

### Phase 2: Auth & Authorization
```typescript
Tasks:
- [ ] NextAuth.js v5 with async cookies
- [ ] Multi-org user support
- [ ] RBAC implementation
- [ ] Organization switching
- [ ] Security middleware
```

### Phase 3: Invitation System
```typescript
Tasks:
- [ ] Invitation models & APIs
- [ ] Email service (Resend)
- [ ] Invitation acceptance flow
- [ ] Bulk invitation management
- [ ] Status tracking
```

## 🔧 Development Commands

### Setup & Development
```bash
# Install dependencies
npm install

# Development with Turbopack (Next.js 15)
npm run dev --turbo

# Type checking
npm run type-check

# Linting
npm run lint

# Testing
npm run test
npm run test:e2e

# Database
npx prisma generate
npx prisma db push
npx prisma studio
```

### Code Quality
```bash
# Pre-commit checks
npm run pre-commit

# Format code
npm run format

# Build for production
npm run build

# Start production server
npm start
```

## 🏗️ File Structure Quick Guide

```
app/
├── (auth)/login, register
├── (dashboard)/[orgSlug]/...
├── organizations/
├── invitations/
└── api/trpc, auth, webhooks

components/
├── ui/ (reusable)
├── organization/
├── auth/
└── forms/

lib/
├── auth.ts (NextAuth config)
├── db.ts (Prisma client)
├── trpc.ts (tRPC setup)
└── utils.ts

stores/
├── organization-store.ts
└── auth-store.ts
```

## 🔒 Security Checklist

### Every Feature Must Have:
- [ ] Organization-scoped data access
- [ ] Input validation (Zod schemas)
- [ ] User permission checks
- [ ] Error handling
- [ ] Audit logging

### Data Access Pattern:
```typescript
// ✅ Always include organizationId
const data = await db.model.findMany({
  where: {
    organizationId: user.currentOrganizationId,
    // other filters...
  }
})
```

## 🎨 UI/UX Standards

### Organization Context:
- Current org always visible in header
- Organization selector dropdown
- Contextual navigation per org
- Clear data separation visually

### Design System:
- Colors: KooLek blue (#2563eb) + neutrals
- Spacing: 8px grid system
- Typography: Inter font
- Components: Radix UI + Tailwind

## 📊 Performance Targets

### Core Web Vitals:
- **FCP**: < 200ms
- **LCP**: < 500ms
- **TTI**: < 800ms
- **CLS**: < 0.1

### Development:
- **Dev server**: 76% faster (Turbopack)
- **Hot reload**: 96% faster
- **Build time**: 70% faster (React Compiler)

## 🧪 Testing Strategy

```typescript
Testing Pyramid:
├── Unit Tests (70%): Vitest + Testing Library
├── Integration (20%): API testing
└── E2E Tests (10%): Playwright critical paths

Key Test Areas:
- Organization isolation
- User permissions
- Invitation flow
- Data security
- Performance
```

## 🚨 Common Pitfalls to Avoid

### Security:
- ❌ Forgetting organizationId in queries
- ❌ Not validating user org access
- ❌ Exposing data across organizations
- ❌ Missing input validation

### Performance:
- ❌ N+1 query problems
- ❌ Large bundle sizes
- ❌ Unnecessary re-renders
- ❌ Missing caching

### Code Quality:
- ❌ Using `any` types
- ❌ Missing error boundaries
- ❌ Inconsistent naming
- ❌ Poor component structure

## 🔄 Git Workflow

### Branch Naming:
```
feature/org-selector
fix/invitation-bug
refactor/auth-optimization
docs/api-update
```

### Commit Format:
```
feat(org): add organization selector
fix(auth): resolve session issue
refactor(db): optimize queries
docs(api): update documentation
```

## 📞 Key Contacts & Resources

### Documentation:
- Architecture: `MULTI_TENANT_ARCHITECTURE.md`
- Guidelines: `.augment-guidelines`
- Quick Ref: `.augment-quick-reference`

### External Resources:
- Next.js 15 Docs: https://nextjs.org/docs
- React 19 Guide: https://react.dev/blog/2024/04/25/react-19-upgrade-guide
- Prisma Docs: https://www.prisma.io/docs
- tRPC Docs: https://trpc.io/docs

## 🎯 Next Actions

### Immediate (This Week):
1. Set up development environment
2. Create Prisma schema for organizations
3. Implement organization context
4. Build organization selector component
5. Create basic tRPC organization router

### Short Term (Next 2 Weeks):
1. Complete Phase 1 implementation
2. Begin Phase 2 (Auth & Authorization)
3. Set up testing infrastructure
4. Create CI/CD pipeline
5. Documentation updates

---

*Keep this reference handy during development. Update as the project evolves.*
