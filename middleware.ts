import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { auth } from '@/lib/auth'

// Define protected routes that require authentication
const protectedRoutes = [
  '/dashboard',
  '/organizations',
  '/contributions',
  '/expenses',
  '/collections',
  '/settings',
  '/profile',
]

// Define organization-scoped routes that require organization context
const organizationRoutes = [
  '/dashboard',
  '/contributions',
  '/expenses',
  '/collections',
  '/reports',
  '/members',
  '/settings',
]

// Define public routes that should redirect authenticated users
const publicRoutes = [
  '/signin',
  '/signup',
  '/auth/signin',
  '/auth/signup',
  '/auth/reset-password',
  '/auth/verify-email',
]

// Define admin routes that require elevated permissions
const adminRoutes = [
  '/admin',
]

// Define API routes that need special handling
const apiRoutes = [
  '/api/auth',
  '/api/trpc',
]

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  const response = NextResponse.next()

  // Add comprehensive security headers for all requests
  addSecurityHeaders(response)

  // Skip middleware for API routes (handled by tRPC middleware)
  if (apiRoutes.some(route => pathname.startsWith(route))) {
    return response
  }

  // Skip middleware for static files and Next.js internals
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/favicon') ||
    pathname.includes('.')
  ) {
    return response
  }

  try {
    // Get session using NextAuth v5
    const session = await auth()

    // Handle authentication checks
    const authResult = await handleAuthentication(request, session, pathname)
    if (authResult) return authResult

    // Handle organization context validation
    const orgResult = await handleOrganizationContext(request, session, pathname)
    if (orgResult) return orgResult

    // Handle admin route protection
    const adminResult = await handleAdminRoutes(request, session, pathname)
    if (adminResult) return adminResult

    return response
  } catch (error) {
    console.error('Middleware error:', error)
    // On error, redirect to signin for protected routes
    if (isProtectedRoute(pathname)) {
      return redirectToSignIn(request)
    }
    return response
  }
}

// Enhanced security headers
function addSecurityHeaders(response: NextResponse) {
  // Prevent MIME type sniffing
  response.headers.set('X-Content-Type-Options', 'nosniff')

  // Prevent clickjacking
  response.headers.set('X-Frame-Options', 'DENY')

  // XSS protection
  response.headers.set('X-XSS-Protection', '1; mode=block')

  // Referrer policy
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')

  // Content Security Policy (basic)
  response.headers.set(
    'Content-Security-Policy',
    "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:;"
  )

  // Prevent caching of sensitive pages
  if (response.url.includes('/dashboard') || response.url.includes('/settings')) {
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate')
    response.headers.set('Pragma', 'no-cache')
    response.headers.set('Expires', '0')
  }
}

// Authentication handler
async function handleAuthentication(
  request: NextRequest,
  session: any,
  pathname: string
): Promise<NextResponse | null> {
  const isProtected = isProtectedRoute(pathname)
  const isPublic = isPublicRoute(pathname)

  // Redirect authenticated users away from public auth pages
  if (session && isPublic) {
    return NextResponse.redirect(new URL('/organizations', request.url))
  }

  // Redirect unauthenticated users from protected routes
  if (!session && isProtected) {
    return redirectToSignIn(request)
  }

  return null
}

// Organization context handler
async function handleOrganizationContext(
  request: NextRequest,
  session: any,
  pathname: string
): Promise<NextResponse | null> {
  if (!session) return null

  const isOrgRoute = isOrganizationRoute(pathname)
  if (!isOrgRoute) return null

  // Extract organization slug from URL
  const orgSlugMatch = pathname.match(/^\/([^\/]+)\//)
  if (!orgSlugMatch) {
    // Organization route without slug, redirect to organizations page
    return NextResponse.redirect(new URL('/organizations', request.url))
  }

  const orgSlug = orgSlugMatch[1]

  // Skip validation for certain routes
  if (['organizations', 'auth', 'api', 'invitations'].includes(orgSlug)) {
    return null
  }

  // Add organization slug to request headers for server components
  const response = NextResponse.next()
  response.headers.set('x-organization-slug', orgSlug)

  return null
}

// Admin route handler
async function handleAdminRoutes(
  request: NextRequest,
  session: any,
  pathname: string
): Promise<NextResponse | null> {
  if (!session) return null

  const isAdmin = isAdminRoute(pathname)
  if (!isAdmin) return null

  // For now, redirect to dashboard (admin features not implemented)
  return NextResponse.redirect(new URL('/organizations', request.url))
}

// Helper functions
function isProtectedRoute(pathname: string): boolean {
  return protectedRoutes.some(route => pathname.startsWith(route)) ||
         pathname.match(/^\/[^\/]+\/(dashboard|KooLeknts|expenses|collections|reports|members|settings)/)
}

function isPublicRoute(pathname: string): boolean {
  return publicRoutes.some(route => pathname.startsWith(route))
}

function isOrganizationRoute(pathname: string): boolean {
  return organizationRoutes.some(route => pathname.includes(route)) ||
         pathname.match(/^\/[^\/]+\/(dashboard|KooLeknts|expenses|collections|reports|members|settings)/)
}

function isAdminRoute(pathname: string): boolean {
  return adminRoutes.some(route => pathname.startsWith(route))
}

function redirectToSignIn(request: NextRequest): NextResponse {
  const signInUrl = new URL('/signin', request.url)
  signInUrl.searchParams.set('callbackUrl', request.url)
  return NextResponse.redirect(signInUrl)
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
}
