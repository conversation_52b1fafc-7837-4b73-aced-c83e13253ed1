import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Search as SearchIcon,
  CollectionsBookmark as CollectionsIcon,
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import moment from 'moment';

import { useAuth } from '../contexts/AuthContext';
import { useLoading } from '../contexts/LoadingContext';
import { apiService } from '../services/apiService';
import { formatCurrency, formatDateTime, isAdmin, isManager } from '../utils/helpers';
import Layout from '../components/Layout';

export default function Collections() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const { showLoading, hideLoading } = useLoading();
  const { enqueueSnackbar } = useSnackbar();

  const [collections, setCollections] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [year, setYear] = useState(moment().format('YYYY'));
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login');
      return;
    }

    if (!isManager(user)) {
      router.push('/');
      return;
    }

    fetchCollections();
  }, [isAuthenticated, router, user, year]);

  const fetchCollections = async () => {
    showLoading('Loading collections...');
    try {
      const response = await apiService.getCollections({ year });
      if (response.status === 'success') {
        setCollections(response.data);
      }
    } catch (error) {
      enqueueSnackbar('Failed to load collections', { variant: 'error' });
    } finally {
      hideLoading();
    }
  };

  const handleCollectContribution = (userCollection) => {
    setSelectedUser(userCollection);
    setDialogOpen(true);
  };

  const handleConfirmCollection = async () => {
    if (!selectedUser) return;

    showLoading('Processing collection...');
    try {
      const response = await apiService.createCollection({
        user_id: selectedUser.staff_id,
        year,
      });

      if (response.status === 'success') {
        enqueueSnackbar('Collection processed successfully!', { variant: 'success' });
        setDialogOpen(false);
        fetchCollections();
      }
    } catch (error) {
      enqueueSnackbar('Failed to process collection', { variant: 'error' });
    } finally {
      hideLoading();
    }
  };

  const filteredCollections = collections.filter(collection =>
    collection.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    collection.staff_id?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    collection.department?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getContributionStatus = (collection) => {
    const expectedAmount = collection.monthly_contribution * 12;
    const paidAmount = collection.total_paid || 0;

    if (paidAmount >= expectedAmount) {
      return { status: 'Complete', color: 'success' };
    } else if (paidAmount > 0) {
      return { status: 'Partial', color: 'warning' };
    } else {
      return { status: 'Pending', color: 'error' };
    }
  };

  if (!isAuthenticated || !isManager(user)) {
    return null;
  }

  return (
    <Layout>
      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Collections
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Monitor and manage contribution collections from staff members.
          </Typography>
        </Box>

        {/* Collections Table */}
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6" component="h2">
                Contribution Collection Status
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                <TextField
                  size="small"
                  placeholder="Search collections..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                  }}
                />
                <FormControl size="small" sx={{ minWidth: 120 }}>
                  <InputLabel>Year</InputLabel>
                  <Select
                    value={year}
                    onChange={(e) => setYear(e.target.value)}
                    label="Year"
                  >
                    {[2024, 2023, 2022].map((y) => (
                      <MenuItem key={y} value={y.toString()}>
                        {y}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
            </Box>

            <TableContainer component={Paper} variant="outlined">
              <Table>
                <TableHead>
                  <TableRow sx={{ bgcolor: '#2563eb' }}>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>No.</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Staff ID</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Name</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Department</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Monthly Contribution</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Total Paid</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Expected Annual</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Status</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Last Contribution</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredCollections.map((collection, index) => {
                    const contributionStatus = getContributionStatus(collection);
                    const expectedAnnual = collection.monthly_contribution * 12;

                    return (
                      <TableRow key={collection.id} hover>
                        <TableCell>{index + 1}</TableCell>
                        <TableCell>{collection.staff_id}</TableCell>
                        <TableCell>{collection.name}</TableCell>
                        <TableCell>{collection.department}</TableCell>
                        <TableCell>{formatCurrency(collection.monthly_contribution)}</TableCell>
                        <TableCell>{formatCurrency(collection.total_paid || 0)}</TableCell>
                        <TableCell>{formatCurrency(expectedAnnual)}</TableCell>
                        <TableCell>
                          <Chip
                            label={contributionStatus.status}
                            size="small"
                            color={contributionStatus.color}
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell>
                          {collection.last_contribution
                            ? formatDateTime(collection.last_contribution)
                            : 'No contributions'
                          }
                        </TableCell>
                        <TableCell>
                          <Button
                            size="small"
                            variant="outlined"
                            startIcon={<CollectionsIcon />}
                            onClick={() => handleCollectContribution(collection)}
                            disabled={contributionStatus.status === 'Complete'}
                          >
                            Collect
                          </Button>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                  {filteredCollections.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={10} align="center" sx={{ py: 4 }}>
                        <Typography variant="body2" color="text.secondary">
                          {collections.length === 0
                            ? 'No collection data found'
                            : 'No collections match your search'
                          }
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>

        {/* Collection Confirmation Dialog */}
        <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>
            Collect Contribution
          </DialogTitle>
          <DialogContent>
            {selectedUser && (
              <Box sx={{ py: 2 }}>
                <Typography variant="body1" gutterBottom>
                  Process contribution collection for this staff member?
                </Typography>

                <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Staff Details:
                  </Typography>
                  <Typography variant="body2">
                    <strong>Name:</strong> {selectedUser.name}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Staff ID:</strong> {selectedUser.staff_id}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Department:</strong> {selectedUser.department}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Monthly Contribution:</strong> {formatCurrency(selectedUser.monthly_contribution)}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Total Paid:</strong> {formatCurrency(selectedUser.total_paid || 0)}
                  </Typography>
                </Box>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleConfirmCollection}
              variant="contained"
              color="primary"
            >
              Process Collection
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </Layout>
  );
}
