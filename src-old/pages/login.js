import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useFormik } from 'formik';
import * as yup from 'yup';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  Container,
  Paper,
  InputAdornment,
  IconButton,
} from '@mui/material';
import { Visibility, VisibilityOff, Email, Lock } from '@mui/icons-material';
import { useSnackbar } from 'notistack';

import { useAuth } from '../contexts/AuthContext';
import { useLoading } from '../contexts/LoadingContext';

const validationSchema = yup.object({
  email: yup
    .string()
    .email('Enter a valid email')
    .required('Email is required'),
  password: yup
    .string()
    .min(6, 'Password should be of minimum 6 characters length')
    .required('Password is required'),
});

export default function Login() {
  const router = useRouter();
  const { login, isAuthenticated, error, clearError } = useAuth();
  const { showLoading, hideLoading } = useLoading();
  const { enqueueSnackbar } = useSnackbar();
  const [showPassword, setShowPassword] = useState(false);

  useEffect(() => {
    if (isAuthenticated) {
      router.push('/');
    }
  }, [isAuthenticated, router]);

  useEffect(() => {
    if (error) {
      enqueueSnackbar(error, { variant: 'error' });
      clearError();
    }
  }, [error, enqueueSnackbar, clearError]);

  const formik = useFormik({
    initialValues: {
      email: '',
      password: '',
    },
    validationSchema: validationSchema,
    onSubmit: async (values) => {
      showLoading('Logging in...');
      try {
        const result = await login(values);
        if (result.success) {
          enqueueSnackbar('Login successful!', { variant: 'success' });
          router.push('/');
        }
      } catch (error) {
        enqueueSnackbar('Login failed. Please try again.', { variant: 'error' });
      } finally {
        hideLoading();
      }
    },
  });

  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        bgcolor: '#ffffff',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 3,
      }}
    >
      <Container maxWidth="sm">
        <Box
          sx={{
            maxWidth: 400,
            mx: 'auto',
            p: 4,
          }}
        >
          {/* Header */}
          <Box sx={{ textAlign: 'center', mb: 6 }}>
            <Typography
              variant="h1"
              component="h1"
              sx={{
                fontSize: '32px',
                fontWeight: 700,
                color: '#37352f',
                mb: 1,
                letterSpacing: '-0.5px'
              }}
            >
              KooLek
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: '#6b7280',
                fontSize: '14px',
                fontWeight: 400
              }}
            >
              Financial Management System
            </Typography>
          </Box>

          {/* Welcome Text */}
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            <Typography
              variant="h2"
              component="h2"
              sx={{
                fontSize: '24px',
                fontWeight: 600,
                color: '#37352f',
                mb: 1
              }}
            >
              Welcome back
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: '#6b7280',
                fontSize: '14px'
              }}
            >
              Sign in to your account to continue
            </Typography>
          </Box>

          {/* Demo Credentials Info */}
          <Alert
            severity="info"
            sx={{
              mb: 4,
              borderRadius: '8px',
              border: '1px solid #dbeafe',
              bgcolor: '#eff6ff',
              '& .MuiAlert-icon': {
                color: '#2563eb'
              }
            }}
          >
            <Typography variant="body2" sx={{ fontWeight: 600, mb: 1, color: '#37352f' }}>
              Demo Credentials:
            </Typography>
            <Typography variant="body2" sx={{ color: '#6b7280', fontSize: '13px' }}>
              Admin: <EMAIL> / admin123
            </Typography>
            <Typography variant="body2" sx={{ color: '#6b7280', fontSize: '13px' }}>
              User: <EMAIL> / user123
            </Typography>
          </Alert>

          {/* Login Form */}
          <form onSubmit={formik.handleSubmit}>
            <Box sx={{ mb: 3 }}>
              <Typography
                variant="body2"
                sx={{
                  mb: 1,
                  fontWeight: 500,
                  color: '#37352f',
                  fontSize: '14px'
                }}
              >
                Email
              </Typography>
              <TextField
                fullWidth
                name="email"
                type="email"
                placeholder="Enter your email"
                value={formik.values.email}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.email && Boolean(formik.errors.email)}
                helperText={formik.touched.email && formik.errors.email}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    fontSize: '14px',
                    '& fieldset': {
                      borderColor: '#e5e7eb',
                    },
                    '&:hover fieldset': {
                      borderColor: '#d1d5db',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#2563eb',
                      borderWidth: '1px',
                    },
                  },
                }}
              />
            </Box>

            <Box sx={{ mb: 4 }}>
              <Typography
                variant="body2"
                sx={{
                  mb: 1,
                  fontWeight: 500,
                  color: '#37352f',
                  fontSize: '14px'
                }}
              >
                Password
              </Typography>
              <TextField
                fullWidth
                name="password"
                type={showPassword ? 'text' : 'password'}
                placeholder="Enter your password"
                value={formik.values.password}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.password && Boolean(formik.errors.password)}
                helperText={formik.touched.password && formik.errors.password}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={handleClickShowPassword}
                        edge="end"
                        sx={{ color: '#6b7280' }}
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    fontSize: '14px',
                    '& fieldset': {
                      borderColor: '#e5e7eb',
                    },
                    '&:hover fieldset': {
                      borderColor: '#d1d5db',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#2563eb',
                      borderWidth: '1px',
                    },
                  },
                }}
              />
            </Box>

            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{
                py: 1.5,
                bgcolor: '#2563eb',
                fontSize: '14px',
                fontWeight: 500,
                textTransform: 'none',
                borderRadius: '8px',
                boxShadow: 'none',
                '&:hover': {
                  bgcolor: '#1d4ed8',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                  transform: 'translateY(-1px)',
                },
                '&:disabled': {
                  bgcolor: '#9ca3af',
                  color: '#ffffff',
                },
              }}
              disabled={formik.isSubmitting}
            >
              {formik.isSubmitting ? 'Signing in...' : 'Sign in'}
            </Button>
          </form>

          {/* Footer */}
          <Typography
            variant="caption"
            sx={{
              display: 'block',
              textAlign: 'center',
              mt: 6,
              color: '#9ca3af',
              fontSize: '12px'
            }}
          >
            © 2024 KooLek Financial Management System
          </Typography>
        </Box>
      </Container>
    </Box>
  );
}
