import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Card,
  CardContent,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Fab,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  QrCode as QrCodeIcon,
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { useFormik } from 'formik';
import * as yup from 'yup';
import moment from 'moment';

import { useAuth } from '../contexts/AuthContext';
import { useLoading } from '../contexts/LoadingContext';
import { apiService } from '../services/apiService';
import { formatCurrency, formatDateTime } from '../utils/helpers';
import { CONTRIBUTION_CATEGORIES } from '../utils/constants';
import Layout from '../components/Layout';

const validationSchema = yup.object({
  user_reference: yup.string().required('Reference is required'),
  user_amount: yup.number().positive('Amount must be positive').required('Amount is required'),
  user_category: yup.string().required('Category is required'),
});

export default function Contributions() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const { showLoading, hideLoading } = useLoading();
  const { enqueueSnackbar } = useSnackbar();

  const [contributions, setContributions] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [year, setYear] = useState(moment().format('YYYY'));

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login');
      return;
    }
    fetchContributions();
  }, [isAuthenticated, router, year]);

  const fetchContributions = async () => {
    showLoading('Loading contributions...');
    try {
      const response = await apiService.getContributions({ year });
      if (response.status === 'success') {
        setContributions(response.data);
      }
    } catch (error) {
      enqueueSnackbar('Failed to load contributions', { variant: 'error' });
    } finally {
      hideLoading();
    }
  };

  const formik = useFormik({
    initialValues: {
      user_reference: '',
      user_amount: '',
      user_category: '',
    },
    validationSchema,
    onSubmit: async (values, { resetForm }) => {
      showLoading('Creating contribution...');
      try {
        const contributionData = {
          ...values,
          user_id: user?.staff_id,
          user_name: user?.name,
          year,
        };

        const response = await apiService.createContribution(contributionData);
        if (response.status === 'success') {
          enqueueSnackbar('Contribution created successfully!', { variant: 'success' });
          setDialogOpen(false);
          resetForm();
          fetchContributions();
        }
      } catch (error) {
        enqueueSnackbar('Failed to create contribution', { variant: 'error' });
      } finally {
        hideLoading();
      }
    },
  });

  const filteredContributions = contributions.filter(contribution =>
    contribution.user_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contribution.user_reference?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contribution.user_category?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (!isAuthenticated) {
    return null;
  }

  return (
    <Layout>
      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Contributions
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage your contribution requests and track their status.
          </Typography>
        </Box>

        {/* Contributions Table */}
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6" component="h2">
                Contribution History
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                <TextField
                  size="small"
                  placeholder="Search contributions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                  }}
                />
                <FormControl size="small" sx={{ minWidth: 120 }}>
                  <InputLabel>Year</InputLabel>
                  <Select
                    value={year}
                    onChange={(e) => setYear(e.target.value)}
                    label="Year"
                  >
                    {[2024, 2023, 2022].map((y) => (
                      <MenuItem key={y} value={y.toString()}>
                        {y}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => setDialogOpen(true)}
                  sx={{ bgcolor: '#2563eb', display: { xs: 'none', md: 'flex' } }}
                >
                  Add Contribution
                </Button>
              </Box>
            </Box>

            <TableContainer component={Paper} variant="outlined">
              <Table>
                <TableHead>
                  <TableRow sx={{ bgcolor: '#2563eb' }}>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>No.</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Reference</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Amount</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Category</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Status</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Date</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredContributions.map((contribution, index) => (
                    <TableRow key={contribution.id} hover>
                      <TableCell>{index + 1}</TableCell>
                      <TableCell>{contribution.user_reference}</TableCell>
                      <TableCell>{formatCurrency(contribution.user_amount)}</TableCell>
                      <TableCell>{contribution.user_category}</TableCell>
                      <TableCell>
                        <Chip
                          label={contribution.status}
                          size="small"
                          sx={{
                            bgcolor: contribution.status === 'verified' ? '#dcfce7' :
                                    contribution.status === 'pending' ? '#fef3c7' : '#fee2e2',
                            color: contribution.status === 'verified' ? '#166534' :
                                   contribution.status === 'pending' ? '#92400e' : '#991b1b',
                          }}
                        />
                      </TableCell>
                      <TableCell>{formatDateTime(contribution.created_at)}</TableCell>
                    </TableRow>
                  ))}
                  {filteredContributions.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={6} align="center" sx={{ py: 4 }}>
                        <Typography variant="body2" color="text.secondary">
                          No contributions found
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>

        {/* Mobile FAB */}
        <Fab
          color="primary"
          aria-label="add contribution"
          onClick={() => setDialogOpen(true)}
          sx={{
            position: 'fixed',
            bottom: 16,
            right: 16,
            display: { xs: 'flex', md: 'none' },
            bgcolor: '#2563eb',
          }}
        >
          <AddIcon />
        </Fab>

        {/* Add Contribution Dialog */}
        <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
          <DialogTitle sx={{ bgcolor: '#2563eb', color: 'white' }}>
            Add New Contribution
          </DialogTitle>
          <DialogContent sx={{ pt: 3 }}>
            <Grid container spacing={3}>
              {/* Contribution Form */}
              <Grid item xs={12} md={6}>
                <form onSubmit={formik.handleSubmit}>
                  <TextField
                    fullWidth
                    margin="normal"
                    name="user_reference"
                    label="Reference"
                    value={formik.values.user_reference}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.user_reference && Boolean(formik.errors.user_reference)}
                    helperText={formik.touched.user_reference && formik.errors.user_reference}
                  />

                  <TextField
                    fullWidth
                    margin="normal"
                    name="user_amount"
                    label="Amount (RM)"
                    type="number"
                    value={formik.values.user_amount}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.user_amount && Boolean(formik.errors.user_amount)}
                    helperText={formik.touched.user_amount && formik.errors.user_amount}
                  />

                  <FormControl fullWidth margin="normal">
                    <InputLabel>Category</InputLabel>
                    <Select
                      name="user_category"
                      value={formik.values.user_category}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.user_category && Boolean(formik.errors.user_category)}
                    >
                      {CONTRIBUTION_CATEGORIES.map((category) => (
                        <MenuItem key={category} value={category}>
                          {category}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>

                  <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
                    <Button
                      variant="outlined"
                      onClick={() => setDialogOpen(false)}
                      fullWidth
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      fullWidth
                      sx={{ bgcolor: '#2563eb' }}
                      disabled={formik.isSubmitting}
                    >
                      {formik.isSubmitting ? 'Creating...' : 'Create Contribution'}
                    </Button>
                  </Box>
                </form>
              </Grid>

              {/* QR Code Section */}
              <Grid item xs={12} md={6}>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    height: '100%',
                    bgcolor: 'grey.50',
                    borderRadius: 2,
                    p: 3,
                  }}
                >
                  <QrCodeIcon sx={{ fontSize: 120, color: '#2563eb', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    KooLek QR Code
                  </Typography>
                  <Typography variant="body2" color="text.secondary" textAlign="center">
                    Scan this QR code to make contribution via your mobile banking app
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </DialogContent>
        </Dialog>
      </Container>
    </Layout>
  );
}
