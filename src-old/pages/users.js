import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Card,
  CardContent,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { useFormik } from 'formik';
import * as yup from 'yup';

import { useAuth } from '../contexts/AuthContext';
import { useLoading } from '../contexts/LoadingContext';
import { apiService } from '../services/apiService';
import { formatCurrency, isAdmin } from '../utils/helpers';
import { CONTRIBUTION_OPTIONS, USER_ROLES } from '../utils/constants';
import Layout from '../components/Layout';

const validationSchema = yup.object({
  staff_id: yup.string().required('Staff ID is required'),
  name: yup.string().required('Name is required'),
  email: yup.string().email('Invalid email').required('Email is required'),
  department: yup.string().required('Department is required'),
  role: yup.string().required('Role is required'),
  monthly_contribution: yup.number().positive('Must be positive').required('Monthly contribution is required'),
  others_contribution: yup.number().min(0, 'Must be non-negative'),
});

export default function Users() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const { showLoading, hideLoading } = useLoading();
  const { enqueueSnackbar } = useSnackbar();

  const [users, setUsers] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login');
      return;
    }
    
    if (!isAdmin(user)) {
      router.push('/');
      return;
    }
    
    fetchUsers();
  }, [isAuthenticated, router, user]);

  const fetchUsers = async () => {
    showLoading('Loading users...');
    try {
      const response = await apiService.getUsers();
      if (response.status === 'success') {
        setUsers(response.data);
      }
    } catch (error) {
      enqueueSnackbar('Failed to load users', { variant: 'error' });
    } finally {
      hideLoading();
    }
  };

  const formik = useFormik({
    initialValues: {
      staff_id: '',
      name: '',
      email: '',
      department: '',
      role: 'user',
      monthly_contribution: 25,
      others_contribution: 0,
    },
    validationSchema,
    onSubmit: async (values, { resetForm }) => {
      showLoading(editMode ? 'Updating user...' : 'Creating user...');
      try {
        let response;
        if (editMode) {
          response = await apiService.updateUser(selectedUser.id, values);
        } else {
          response = await apiService.createUser(values);
        }

        if (response.status === 'success') {
          enqueueSnackbar(
            editMode ? 'User updated successfully!' : 'User created successfully!',
            { variant: 'success' }
          );
          handleCloseDialog();
          fetchUsers();
        }
      } catch (error) {
        enqueueSnackbar(
          editMode ? 'Failed to update user' : 'Failed to create user',
          { variant: 'error' }
        );
      } finally {
        hideLoading();
      }
    },
  });

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditMode(false);
    setSelectedUser(null);
    formik.resetForm();
  };

  const handleEditUser = (userToEdit) => {
    setSelectedUser(userToEdit);
    setEditMode(true);
    formik.setValues({
      staff_id: userToEdit.staff_id,
      name: userToEdit.name,
      email: userToEdit.email,
      department: userToEdit.department,
      role: userToEdit.role,
      monthly_contribution: userToEdit.monthly_contribution,
      others_contribution: userToEdit.others_contribution || 0,
    });
    setDialogOpen(true);
  };

  const handleDeleteUser = async (userId) => {
    if (window.confirm('Are you sure you want to delete this user?')) {
      showLoading('Deleting user...');
      try {
        const response = await apiService.deleteUser(userId);
        if (response.status === 'success') {
          enqueueSnackbar('User deleted successfully!', { variant: 'success' });
          fetchUsers();
        }
      } catch (error) {
        enqueueSnackbar('Failed to delete user', { variant: 'error' });
      } finally {
        hideLoading();
      }
    }
  };

  const filteredUsers = users.filter(u =>
    u.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    u.staff_id?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    u.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    u.department?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (!isAuthenticated || !isAdmin(user)) {
    return null;
  }

  return (
    <Layout>
      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            User Management
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage staff members and their KooLeknt configurations.
          </Typography>
        </Box>

        {/* Users Table */}
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6" component="h2">
                Staff Members
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                <TextField
                  size="small"
                  placeholder="Search users..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                  }}
                />
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => setDialogOpen(true)}
                  sx={{ bgcolor: '#2563eb' }}
                >
                  Add User
                </Button>
              </Box>
            </Box>

            <TableContainer component={Paper} variant="outlined">
              <Table>
                <TableHead>
                  <TableRow sx={{ bgcolor: '#2563eb' }}>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>No.</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Staff ID</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Name</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Email</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Department</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Role</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Monthly Contribution</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Status</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredUsers.map((userItem, index) => (
                    <TableRow key={userItem.id} hover>
                      <TableCell>{index + 1}</TableCell>
                      <TableCell>{userItem.staff_id}</TableCell>
                      <TableCell>{userItem.name}</TableCell>
                      <TableCell>{userItem.email}</TableCell>
                      <TableCell>{userItem.department}</TableCell>
                      <TableCell>
                        <Chip
                          label={userItem.role}
                          size="small"
                          color={userItem.role === 'admin' ? 'error' : 'primary'}
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>{formatCurrency(userItem.monthly_contribution)}</TableCell>
                      <TableCell>
                        <Chip
                          label={userItem.status || 'active'}
                          size="small"
                          color="success"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          onClick={() => handleEditUser(userItem)}
                          color="primary"
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleDeleteUser(userItem.id)}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                  {filteredUsers.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={9} align="center" sx={{ py: 4 }}>
                        <Typography variant="body2" color="text.secondary">
                          {users.length === 0 ? 'No users found' : 'No users match your search'}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>

        {/* Add/Edit User Dialog */}
        <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
          <DialogTitle sx={{ bgcolor: '#2563eb', color: 'white' }}>
            {editMode ? 'Edit User' : 'Add New User'}
          </DialogTitle>
          <DialogContent sx={{ pt: 3 }}>
            <form onSubmit={formik.handleSubmit}>
              <TextField
                fullWidth
                margin="normal"
                name="staff_id"
                label="Staff ID"
                value={formik.values.staff_id}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.staff_id && Boolean(formik.errors.staff_id)}
                helperText={formik.touched.staff_id && formik.errors.staff_id}
                disabled={editMode}
              />

              <TextField
                fullWidth
                margin="normal"
                name="name"
                label="Full Name"
                value={formik.values.name}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.name && Boolean(formik.errors.name)}
                helperText={formik.touched.name && formik.errors.name}
              />

              <TextField
                fullWidth
                margin="normal"
                name="email"
                label="Email"
                type="email"
                value={formik.values.email}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.email && Boolean(formik.errors.email)}
                helperText={formik.touched.email && formik.errors.email}
              />

              <TextField
                fullWidth
                margin="normal"
                name="department"
                label="Department"
                value={formik.values.department}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.department && Boolean(formik.errors.department)}
                helperText={formik.touched.department && formik.errors.department}
              />

              <FormControl fullWidth margin="normal">
                <InputLabel>Role</InputLabel>
                <Select
                  name="role"
                  value={formik.values.role}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.role && Boolean(formik.errors.role)}
                >
                  <MenuItem value="user">User</MenuItem>
                  <MenuItem value="manager">Manager</MenuItem>
                  <MenuItem value="admin">Admin</MenuItem>
                </Select>
              </FormControl>

              <FormControl fullWidth margin="normal">
                <InputLabel>Monthly Contribution (RM)</InputLabel>
                <Select
                  name="monthly_contribution"
                  value={formik.values.monthly_contribution}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.monthly_contribution && Boolean(formik.errors.monthly_contribution)}
                >
                  {CONTRIBUTION_OPTIONS.map((amount) => (
                    <MenuItem key={amount} value={amount}>
                      RM {amount}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <TextField
                fullWidth
                margin="normal"
                name="others_contribution"
                label="Others Contribution (RM)"
                type="number"
                value={formik.values.others_contribution}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.others_contribution && Boolean(formik.errors.others_contribution)}
                helperText={formik.touched.others_contribution && formik.errors.others_contribution}
              />

              <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
                <Button
                  variant="outlined"
                  onClick={handleCloseDialog}
                  fullWidth
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  fullWidth
                  sx={{ bgcolor: '#2563eb' }}
                  disabled={formik.isSubmitting}
                >
                  {formik.isSubmitting 
                    ? (editMode ? 'Updating...' : 'Creating...') 
                    : (editMode ? 'Update User' : 'Create User')
                  }
                </Button>
              </Box>
            </form>
          </DialogContent>
        </Dialog>
      </Container>
    </Layout>
  );
}
