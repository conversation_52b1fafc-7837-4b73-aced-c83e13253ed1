import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
} from '@mui/material';
import {
  Add as AddIcon,
  AccountBalance as AccountBalanceIcon,
  Receipt as ReceiptIcon,
  Pending as PendingIcon,
  Verified as VerifiedIcon,
  Search as SearchIcon,
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { useFormik } from 'formik';
import * as yup from 'yup';
import moment from 'moment';

import { useAuth } from '../contexts/AuthContext';
import { useLoading } from '../contexts/LoadingContext';
import { apiService } from '../services/apiService';
import { formatCurrency, formatDateTime, getStatusStyle, isAdmin } from '../utils/helpers';
import { KooLekNT_CATEGORIES } from '../utils/constants';
import Layout from '../components/Layout';

const validationSchema = yup.object({
  user_reference: yup.string().required('Reference is required'),
  user_amount: yup.number().positive('Amount must be positive').required('Amount is required'),
  user_category: yup.string().required('Category is required'),
});

export default function Dashboard() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const { showLoading, hideLoading } = useLoading();
  const { enqueueSnackbar } = useSnackbar();

  const [summary, setSummary] = useState({
    totalCollection: 0,
    totalExpenses: 0,
    pendingAmount: 0,
    verifiedAmount: 0,
  });
  const [transactions, setTransactions] = useState([]);
  const [year, setYear] = useState(moment().format('YYYY'));
  const [searchTerm, setSearchTerm] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login');
      return;
    }
    fetchData();
  }, [isAuthenticated, router, year]);

  const fetchData = async () => {
    showLoading('Loading dashboard data...');
    try {
      // Fetch dashboard summary
      const summaryResponse = await apiService.getDashboardSummary(user?.staff_id, year);
      if (summaryResponse.status === 'success') {
        setSummary(summaryResponse.data[0]);
      }

      // Fetch transactions
      const transactionsResponse = await apiService.getTransactions();
      if (transactionsResponse.status === 'success') {
        setTransactions(transactionsResponse.data);
      }
    } catch (error) {
      enqueueSnackbar('Failed to load dashboard data', { variant: 'error' });
    } finally {
      hideLoading();
    }
  };

  const formik = useFormik({
    initialValues: {
      user_reference: '',
      user_amount: '',
      user_category: '',
    },
    validationSchema,
    onSubmit: async (values, { resetForm }) => {
      showLoading('Creating KooLeknt...');
      try {
        const KooLekntData = {
          ...values,
          user_id: user?.staff_id,
          user_name: user?.name,
          year,
        };

        const response = await apiService.createKooLeknt(KooLekntData);
        if (response.status === 'success') {
          enqueueSnackbar('KooLeknt created successfully!', { variant: 'success' });
          setDialogOpen(false);
          resetForm();
          fetchData();
        }
      } catch (error) {
        enqueueSnackbar('Failed to create KooLeknt', { variant: 'error' });
      } finally {
        hideLoading();
      }
    },
  });

  const filteredTransactions = transactions.filter(transaction =>
    transaction.user_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    transaction.user_reference?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    transaction.user_category?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const summaryCards = [
    {
      title: 'Total Collection',
      value: summary.totalCollection,
      icon: <AccountBalanceIcon />,
      color: '#10b981',
      bgColor: '#ecfdf5',
    },
    {
      title: 'Total Expenses',
      value: summary.totalExpenses,
      icon: <ReceiptIcon />,
      color: '#f59e0b',
      bgColor: '#fffbeb',
    },
    {
      title: 'Pending Amount',
      value: summary.pendingAmount,
      icon: <PendingIcon />,
      color: '#ef4444',
      bgColor: '#fef2f2',
    },
    {
      title: 'Verified Amount',
      value: summary.verifiedAmount,
      icon: <VerifiedIcon />,
      color: '#3b82f6',
      bgColor: '#eff6ff',
    },
  ];

  if (!isAuthenticated) {
    return null;
  }

  return (
    <Layout>
      <Box sx={{ p: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 6 }}>
          <Typography
            variant="h1"
            component="h1"
            sx={{
              fontSize: '32px',
              fontWeight: 700,
              color: '#37352f',
              mb: 1,
              lineHeight: 1.2
            }}
          >
            Dashboard
          </Typography>
          <Typography
            variant="body1"
            sx={{
              color: '#6b7280',
              fontSize: '16px',
              fontWeight: 400
            }}
          >
            Welcome back, {user?.name}! Here's your financial overview.
          </Typography>
        </Box>

        {/* Summary Cards */}
        <Grid container spacing={3} sx={{ mb: 6 }}>
          {summaryCards.map((card, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Card
                sx={{
                  height: '100%',
                  background: '#ffffff',
                  border: '1px solid #f3f4f6',
                  borderRadius: '12px',
                  boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                    borderColor: '#e5e7eb',
                    transform: 'translateY(-1px)',
                  },
                }}
              >
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                    <Box
                      sx={{
                        p: 1.5,
                        borderRadius: '8px',
                        backgroundColor: card.bgColor,
                        color: card.color,
                        mr: 2,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      {card.icon}
                    </Box>
                    <Typography
                      variant="body2"
                      sx={{
                        color: '#6b7280',
                        fontSize: '14px',
                        fontWeight: 500,
                        lineHeight: 1.4
                      }}
                    >
                      {card.title}
                    </Typography>
                  </Box>
                  <Typography
                    variant="h3"
                    component="div"
                    sx={{
                      color: '#37352f',
                      fontWeight: 700,
                      fontSize: '28px',
                      lineHeight: 1.2
                    }}
                  >
                    {formatCurrency(card.value)}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        {/* Transactions Table */}
        <Card
          sx={{
            background: '#ffffff',
            border: '1px solid #f3f4f6',
            borderRadius: '12px',
            boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
          }}
        >
          <CardContent sx={{ p: 0 }}>
            {/* Table Header */}
            <Box sx={{ p: 3, borderBottom: '1px solid #f3f4f6' }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography
                  variant="h3"
                  component="h2"
                  sx={{
                    fontSize: '18px',
                    fontWeight: 600,
                    color: '#37352f',
                    lineHeight: 1.4
                  }}
                >
                  Latest Transactions
                </Typography>
                <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                  <TextField
                    size="small"
                    placeholder="Search transactions..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        fontSize: '14px',
                        '& fieldset': {
                          borderColor: '#e5e7eb',
                        },
                        '&:hover fieldset': {
                          borderColor: '#d1d5db',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: '#2563eb',
                          borderWidth: '1px',
                        },
                      },
                    }}
                    InputProps={{
                      startAdornment: <SearchIcon sx={{ mr: 1, color: '#6b7280', fontSize: 20 }} />,
                    }}
                  />
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => setDialogOpen(true)}
                    sx={{
                      bgcolor: '#2563eb',
                      fontSize: '14px',
                      fontWeight: 500,
                      textTransform: 'none',
                      borderRadius: '8px',
                      boxShadow: 'none',
                      '&:hover': {
                        bgcolor: '#1d4ed8',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                        transform: 'translateY(-1px)',
                      },
                    }}
                  >
                    Add KooLeknt
                  </Button>
                </Box>
              </Box>
            </Box>

            {/* Table */}
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ color: '#37352f', fontWeight: 600, fontSize: '13px', py: 2 }}>No.</TableCell>
                    <TableCell sx={{ color: '#37352f', fontWeight: 600, fontSize: '13px', py: 2 }}>Staff ID</TableCell>
                    <TableCell sx={{ color: '#37352f', fontWeight: 600, fontSize: '13px', py: 2 }}>Name</TableCell>
                    <TableCell sx={{ color: '#37352f', fontWeight: 600, fontSize: '13px', py: 2 }}>Reference</TableCell>
                    <TableCell sx={{ color: '#37352f', fontWeight: 600, fontSize: '13px', py: 2 }}>Amount</TableCell>
                    <TableCell sx={{ color: '#37352f', fontWeight: 600, fontSize: '13px', py: 2 }}>Category</TableCell>
                    <TableCell sx={{ color: '#37352f', fontWeight: 600, fontSize: '13px', py: 2 }}>Status</TableCell>
                    <TableCell sx={{ color: '#37352f', fontWeight: 600, fontSize: '13px', py: 2 }}>Date</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredTransactions.map((transaction, index) => (
                    <TableRow
                      key={transaction.id}
                      sx={{
                        '&:hover': {
                          bgcolor: '#f9fafb',
                        },
                      }}
                    >
                      <TableCell sx={{ py: 2, fontSize: '14px', color: '#37352f' }}>{index + 1}</TableCell>
                      <TableCell sx={{ py: 2, fontSize: '14px', color: '#37352f' }}>{transaction.user_id}</TableCell>
                      <TableCell sx={{ py: 2, fontSize: '14px', color: '#37352f', fontWeight: 500 }}>{transaction.user_name}</TableCell>
                      <TableCell sx={{ py: 2, fontSize: '14px', color: '#6b7280' }}>{transaction.user_reference}</TableCell>
                      <TableCell sx={{ py: 2, fontSize: '14px', color: '#37352f', fontWeight: 600 }}>{formatCurrency(transaction.user_amount)}</TableCell>
                      <TableCell sx={{ py: 2, fontSize: '14px', color: '#6b7280' }}>{transaction.user_category}</TableCell>
                      <TableCell sx={{ py: 2 }}>
                        <Box
                          sx={{
                            display: 'inline-flex',
                            alignItems: 'center',
                            px: 2,
                            py: 0.5,
                            borderRadius: '6px',
                            fontSize: '12px',
                            fontWeight: 500,
                            textTransform: 'capitalize',
                            bgcolor: transaction.status === 'verified' ? '#d1fae5' :
                                    transaction.status === 'pending' ? '#fef3c7' : '#fee2e2',
                            color: transaction.status === 'verified' ? '#065f46' :
                                   transaction.status === 'pending' ? '#92400e' : '#991b1b',
                          }}
                        >
                          {transaction.status}
                        </Box>
                      </TableCell>
                      <TableCell sx={{ py: 2, fontSize: '14px', color: '#6b7280' }}>{formatDateTime(transaction.created_at)}</TableCell>
                    </TableRow>
                  ))}
                  {filteredTransactions.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={8} align="center" sx={{ py: 6 }}>
                        <Typography variant="body2" sx={{ color: '#6b7280', fontSize: '14px' }}>
                          No transactions found
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>

        {/* Add KooLeknt Dialog */}
        <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle
            sx={{
              p: 3,
              borderBottom: '1px solid #f3f4f6',
              fontSize: '18px',
              fontWeight: 600,
              color: '#37352f'
            }}
          >
            Add New KooLeknt
          </DialogTitle>
          <DialogContent sx={{ p: 3 }}>
            <form onSubmit={formik.handleSubmit}>
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="body2"
                  sx={{
                    mb: 1,
                    fontWeight: 500,
                    color: '#37352f',
                    fontSize: '14px'
                  }}
                >
                  Reference
                </Typography>
                <TextField
                  fullWidth
                  name="user_reference"
                  placeholder="Enter KooLeknt reference"
                  value={formik.values.user_reference}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.user_reference && Boolean(formik.errors.user_reference)}
                  helperText={formik.touched.user_reference && formik.errors.user_reference}
                />
              </Box>

              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="body2"
                  sx={{
                    mb: 1,
                    fontWeight: 500,
                    color: '#37352f',
                    fontSize: '14px'
                  }}
                >
                  Amount (RM)
                </Typography>
                <TextField
                  fullWidth
                  name="user_amount"
                  type="number"
                  placeholder="0.00"
                  value={formik.values.user_amount}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.user_amount && Boolean(formik.errors.user_amount)}
                  helperText={formik.touched.user_amount && formik.errors.user_amount}
                />
              </Box>

              <Box sx={{ mb: 4 }}>
                <Typography
                  variant="body2"
                  sx={{
                    mb: 1,
                    fontWeight: 500,
                    color: '#37352f',
                    fontSize: '14px'
                  }}
                >
                  Category
                </Typography>
                <FormControl fullWidth>
                  <Select
                    name="user_category"
                    value={formik.values.user_category}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.user_category && Boolean(formik.errors.user_category)}
                    displayEmpty
                    sx={{
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#e5e7eb',
                      },
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#d1d5db',
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#2563eb',
                        borderWidth: '1px',
                      },
                    }}
                  >
                    <MenuItem value="" disabled>
                      <Typography sx={{ color: '#9ca3af' }}>Select a category</Typography>
                    </MenuItem>
                    {KooLekNT_CATEGORIES.map((category) => (
                      <MenuItem key={category} value={category}>
                        {category}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>

              <Box sx={{ display: 'flex', gap: 2 }}>
                <Button
                  variant="outlined"
                  onClick={() => setDialogOpen(false)}
                  fullWidth
                  sx={{
                    borderColor: '#e5e7eb',
                    color: '#37352f',
                    '&:hover': {
                      borderColor: '#d1d5db',
                      backgroundColor: '#f9fafb',
                    },
                  }}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  fullWidth
                  sx={{
                    bgcolor: '#2563eb',
                    fontSize: '14px',
                    fontWeight: 500,
                    textTransform: 'none',
                    borderRadius: '8px',
                    boxShadow: 'none',
                    '&:hover': {
                      bgcolor: '#1d4ed8',
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                      transform: 'translateY(-1px)',
                    },
                    '&:disabled': {
                      bgcolor: '#9ca3af',
                      color: '#ffffff',
                    },
                  }}
                  disabled={formik.isSubmitting}
                >
                  {formik.isSubmitting ? 'Creating...' : 'Create KooLeknt'}
                </Button>
              </Box>
            </form>
          </DialogContent>
        </Dialog>
      </Box>
    </Layout>
  );
}
