import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Card,
  CardContent,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Fab,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  AttachFile as AttachFileIcon,
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { useFormik } from 'formik';
import * as yup from 'yup';
import moment from 'moment';

import { useAuth } from '../contexts/AuthContext';
import { useLoading } from '../contexts/LoadingContext';
import { apiService } from '../services/apiService';
import { formatCurrency, formatDateTime } from '../utils/helpers';
import Layout from '../components/Layout';

const validationSchema = yup.object({
  title: yup.string().required('Title is required'),
  description: yup.string().required('Description is required'),
  amount: yup.number().positive('Amount must be positive').required('Amount is required'),
  remarks: yup.string(),
});

export default function Expenses() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const { showLoading, hideLoading } = useLoading();
  const { enqueueSnackbar } = useSnackbar();

  const [expenses, setExpenses] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [selectedExpense, setSelectedExpense] = useState(null);
  const [year, setYear] = useState(moment().format('YYYY'));
  const [selectedFile, setSelectedFile] = useState(null);

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login');
      return;
    }
    fetchExpenses();
  }, [isAuthenticated, router, year]);

  const fetchExpenses = async () => {
    showLoading('Loading expenses...');
    try {
      const response = await apiService.getExpenses({ year });
      if (response.status === 'success') {
        setExpenses(response.data);
      }
    } catch (error) {
      enqueueSnackbar('Failed to load expenses', { variant: 'error' });
    } finally {
      hideLoading();
    }
  };

  const formik = useFormik({
    initialValues: {
      title: '',
      description: '',
      amount: '',
      remarks: '',
    },
    validationSchema,
    onSubmit: async (values, { resetForm }) => {
      showLoading(editMode ? 'Updating expense...' : 'Creating expense...');
      try {
        const expenseData = {
          ...values,
          user_id: user?.staff_id,
          user_name: user?.name,
          years: year,
          file_name: selectedFile?.name || '',
        };

        let response;
        if (editMode) {
          response = await apiService.updateExpense(selectedExpense.id, expenseData);
        } else {
          response = await apiService.createExpense(expenseData);
        }

        if (response.status === 'success') {
          // Handle file upload if file is selected
          if (selectedFile && !editMode) {
            await apiService.uploadFile(selectedFile, response.data.id);
          }

          enqueueSnackbar(
            editMode ? 'Expense updated successfully!' : 'Expense created successfully!',
            { variant: 'success' }
          );
          handleCloseDialog();
          fetchExpenses();
        }
      } catch (error) {
        enqueueSnackbar(
          editMode ? 'Failed to update expense' : 'Failed to create expense',
          { variant: 'error' }
        );
      } finally {
        hideLoading();
      }
    },
  });

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditMode(false);
    setSelectedExpense(null);
    setSelectedFile(null);
    formik.resetForm();
  };

  const handleEditExpense = (expense) => {
    setSelectedExpense(expense);
    setEditMode(true);
    formik.setValues({
      title: expense.title,
      description: expense.description,
      amount: expense.amount,
      remarks: expense.remarks || '',
    });
    setDialogOpen(true);
  };

  const handleDeleteExpense = async (expenseId) => {
    if (window.confirm('Are you sure you want to delete this expense?')) {
      showLoading('Deleting expense...');
      try {
        const response = await apiService.deleteExpense(expenseId);
        if (response.status === 'success') {
          enqueueSnackbar('Expense deleted successfully!', { variant: 'success' });
          fetchExpenses();
        }
      } catch (error) {
        enqueueSnackbar('Failed to delete expense', { variant: 'error' });
      } finally {
        hideLoading();
      }
    }
  };

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Validate file size (5MB limit)
      if (file.size > 5 * 1024 * 1024) {
        enqueueSnackbar('File size must be less than 5MB', { variant: 'error' });
        return;
      }
      setSelectedFile(file);
    }
  };

  const filteredExpenses = expenses.filter(expense =>
    expense.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    expense.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    expense.user_name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (!isAuthenticated) {
    return null;
  }

  return (
    <Layout>
      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Expenses
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Track and manage your business expenses with receipt attachments.
          </Typography>
        </Box>

        {/* Expenses Table */}
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6" component="h2">
                Expense Records
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                <TextField
                  size="small"
                  placeholder="Search expenses..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                  }}
                />
                <FormControl size="small" sx={{ minWidth: 120 }}>
                  <InputLabel>Year</InputLabel>
                  <Select
                    value={year}
                    onChange={(e) => setYear(e.target.value)}
                    label="Year"
                  >
                    {[2024, 2023, 2022].map((y) => (
                      <MenuItem key={y} value={y.toString()}>
                        {y}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => setDialogOpen(true)}
                  sx={{ bgcolor: '#2563eb', display: { xs: 'none', md: 'flex' } }}
                >
                  Add Expense
                </Button>
              </Box>
            </Box>

            <TableContainer component={Paper} variant="outlined">
              <Table>
                <TableHead>
                  <TableRow sx={{ bgcolor: '#2563eb' }}>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>No.</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Title</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Description</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Amount</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Attachment</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Date</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredExpenses.map((expense, index) => (
                    <TableRow key={expense.id} hover>
                      <TableCell>{index + 1}</TableCell>
                      <TableCell>{expense.title}</TableCell>
                      <TableCell>{expense.description}</TableCell>
                      <TableCell>{formatCurrency(expense.amount)}</TableCell>
                      <TableCell>
                        {expense.file_name ? (
                          <Tooltip title={expense.file_name}>
                            <AttachFileIcon color="primary" />
                          </Tooltip>
                        ) : (
                          '-'
                        )}
                      </TableCell>
                      <TableCell>{formatDateTime(expense.created_at)}</TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          onClick={() => handleEditExpense(expense)}
                          color="primary"
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleDeleteExpense(expense.id)}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                  {filteredExpenses.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={7} align="center" sx={{ py: 4 }}>
                        <Typography variant="body2" color="text.secondary">
                          No expenses found
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>

        {/* Mobile FAB */}
        <Fab
          color="primary"
          aria-label="add expense"
          onClick={() => setDialogOpen(true)}
          sx={{
            position: 'fixed',
            bottom: 16,
            right: 16,
            display: { xs: 'flex', md: 'none' },
            bgcolor: '#2563eb',
          }}
        >
          <AddIcon />
        </Fab>

        {/* Add/Edit Expense Dialog */}
        <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
          <DialogTitle sx={{ bgcolor: '#2563eb', color: 'white' }}>
            {editMode ? 'Edit Expense' : 'Add New Expense'}
          </DialogTitle>
          <DialogContent sx={{ pt: 3 }}>
            <form onSubmit={formik.handleSubmit}>
              <TextField
                fullWidth
                margin="normal"
                name="title"
                label="Title"
                value={formik.values.title}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.title && Boolean(formik.errors.title)}
                helperText={formik.touched.title && formik.errors.title}
              />

              <TextField
                fullWidth
                margin="normal"
                name="description"
                label="Description"
                multiline
                rows={3}
                value={formik.values.description}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.description && Boolean(formik.errors.description)}
                helperText={formik.touched.description && formik.errors.description}
              />

              <TextField
                fullWidth
                margin="normal"
                name="amount"
                label="Amount (RM)"
                type="number"
                value={formik.values.amount}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.amount && Boolean(formik.errors.amount)}
                helperText={formik.touched.amount && formik.errors.amount}
              />

              <TextField
                fullWidth
                margin="normal"
                name="remarks"
                label="Remarks (Optional)"
                multiline
                rows={2}
                value={formik.values.remarks}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />

              {/* File Upload */}
              <Box sx={{ mt: 2 }}>
                <input
                  accept="image/*,.pdf"
                  style={{ display: 'none' }}
                  id="file-upload"
                  type="file"
                  onChange={handleFileChange}
                />
                <label htmlFor="file-upload">
                  <Button
                    variant="outlined"
                    component="span"
                    startIcon={<AttachFileIcon />}
                    fullWidth
                  >
                    {selectedFile ? selectedFile.name : 'Attach Receipt (Optional)'}
                  </Button>
                </label>
              </Box>

              <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
                <Button
                  variant="outlined"
                  onClick={handleCloseDialog}
                  fullWidth
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  fullWidth
                  sx={{ bgcolor: '#2563eb' }}
                  disabled={formik.isSubmitting}
                >
                  {formik.isSubmitting 
                    ? (editMode ? 'Updating...' : 'Creating...') 
                    : (editMode ? 'Update Expense' : 'Create Expense')
                  }
                </Button>
              </Box>
            </form>
          </DialogContent>
        </Dialog>
      </Container>
    </Layout>
  );
}
