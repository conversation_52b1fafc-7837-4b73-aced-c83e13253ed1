import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TextField,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Search as SearchIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Visibility as VisibilityIcon,
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';

import { useAuth } from '../contexts/AuthContext';
import { useLoading } from '../contexts/LoadingContext';
import { apiService } from '../services/apiService';
import { formatCurrency, formatDateTime, isAdmin } from '../utils/helpers';
import Layout from '../components/Layout';

export default function PendingVerification() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const { showLoading, hideLoading } = useLoading();
  const { enqueueSnackbar } = useSnackbar();

  const [transactions, setTransactions] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState(null);
  const [actionType, setActionType] = useState('');

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login');
      return;
    }
    
    if (!isAdmin(user)) {
      router.push('/');
      return;
    }
    
    fetchPendingTransactions();
  }, [isAuthenticated, router, user]);

  const fetchPendingTransactions = async () => {
    showLoading('Loading pending transactions...');
    try {
      const response = await apiService.getTransactions();
      if (response.status === 'success') {
        // Filter only pending transactions
        const pendingTransactions = response.data.filter(
          transaction => transaction.status.toLowerCase() === 'pending'
        );
        setTransactions(pendingTransactions);
      }
    } catch (error) {
      enqueueSnackbar('Failed to load pending transactions', { variant: 'error' });
    } finally {
      hideLoading();
    }
  };

  const handleVerifyTransaction = (transaction, action) => {
    setSelectedTransaction(transaction);
    setActionType(action);
    setDialogOpen(true);
  };

  const handleConfirmAction = async () => {
    if (!selectedTransaction) return;

    showLoading(`${actionType === 'verify' ? 'Verifying' : 'Rejecting'} transaction...`);
    try {
      const status = actionType === 'verify' ? 'verified' : 'rejected';
      const response = await apiService.updateTransactionStatus(selectedTransaction.id, status);
      
      if (response.status === 'success') {
        enqueueSnackbar(
          `Transaction ${status} successfully!`,
          { variant: 'success' }
        );
        setDialogOpen(false);
        fetchPendingTransactions();
      }
    } catch (error) {
      enqueueSnackbar(
        `Failed to ${actionType} transaction`,
        { variant: 'error' }
      );
    } finally {
      hideLoading();
    }
  };

  const filteredTransactions = transactions.filter(transaction =>
    transaction.user_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    transaction.user_id?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    transaction.user_reference?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    transaction.user_category?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (!isAuthenticated || !isAdmin(user)) {
    return null;
  }

  return (
    <Layout>
      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Pending Verification
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Review and approve or reject pending KooLeknt transactions.
          </Typography>
        </Box>

        {/* Pending Transactions Table */}
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6" component="h2">
                Transactions Awaiting Verification
              </Typography>
              <TextField
                size="small"
                placeholder="Search transactions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                }}
              />
            </Box>

            <TableContainer component={Paper} variant="outlined">
              <Table>
                <TableHead>
                  <TableRow sx={{ bgcolor: '#2563eb' }}>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>No.</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Staff ID</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Name</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Reference</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Amount</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Category</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Status</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Date</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredTransactions.map((transaction, index) => (
                    <TableRow key={transaction.id} hover>
                      <TableCell>{index + 1}</TableCell>
                      <TableCell>{transaction.user_id}</TableCell>
                      <TableCell>{transaction.user_name}</TableCell>
                      <TableCell>{transaction.user_reference}</TableCell>
                      <TableCell>{formatCurrency(transaction.user_amount)}</TableCell>
                      <TableCell>{transaction.user_category}</TableCell>
                      <TableCell>
                        <Chip
                          label="Pending"
                          size="small"
                          sx={{
                            bgcolor: '#fef3c7',
                            color: '#92400e',
                          }}
                        />
                      </TableCell>
                      <TableCell>{formatDateTime(transaction.created_at)}</TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Tooltip title="Verify Transaction">
                            <IconButton
                              size="small"
                              onClick={() => handleVerifyTransaction(transaction, 'verify')}
                              sx={{ color: '#10b981' }}
                            >
                              <CheckCircleIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Reject Transaction">
                            <IconButton
                              size="small"
                              onClick={() => handleVerifyTransaction(transaction, 'reject')}
                              sx={{ color: '#ef4444' }}
                            >
                              <CancelIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                  {filteredTransactions.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={9} align="center" sx={{ py: 4 }}>
                        <Typography variant="body2" color="text.secondary">
                          {transactions.length === 0 
                            ? 'No pending transactions found' 
                            : 'No transactions match your search'
                          }
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>

        {/* Confirmation Dialog */}
        <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>
            {actionType === 'verify' ? 'Verify Transaction' : 'Reject Transaction'}
          </DialogTitle>
          <DialogContent>
            {selectedTransaction && (
              <Box sx={{ py: 2 }}>
                <Typography variant="body1" gutterBottom>
                  Are you sure you want to {actionType} this transaction?
                </Typography>
                
                <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Transaction Details:
                  </Typography>
                  <Typography variant="body2">
                    <strong>Staff:</strong> {selectedTransaction.user_name} ({selectedTransaction.user_id})
                  </Typography>
                  <Typography variant="body2">
                    <strong>Reference:</strong> {selectedTransaction.user_reference}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Amount:</strong> {formatCurrency(selectedTransaction.user_amount)}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Category:</strong> {selectedTransaction.user_category}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Date:</strong> {formatDateTime(selectedTransaction.created_at)}
                  </Typography>
                </Box>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleConfirmAction}
              variant="contained"
              color={actionType === 'verify' ? 'success' : 'error'}
            >
              {actionType === 'verify' ? 'Verify' : 'Reject'}
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </Layout>
  );
}
