@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

:root {
  /* Notion-inspired color palette */
  --color-text-primary: #37352f;
  --color-text-secondary: #6b7280;
  --color-text-tertiary: #9ca3af;
  --color-background: #ffffff;
  --color-background-secondary: #f9fafb;
  --color-background-tertiary: #f3f4f6;
  --color-border: #e5e7eb;
  --color-border-light: #f3f4f6;
  --color-accent: #2563eb;
  --color-accent-light: #eff6ff;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;

  /* Spacing scale (8px grid) */
  --spacing-xs: 8px;
  --spacing-sm: 16px;
  --spacing-md: 24px;
  --spacing-lg: 32px;
  --spacing-xl: 48px;
  --spacing-2xl: 64px;

  /* Border radius */
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  color: var(--color-text-primary);
  background-color: var(--color-background);
  line-height: 1.6;
  font-size: 14px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom scrollbar */
.scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.scrollbar::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}

.scrollbar::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Modern component styles */
.notion-card {
  background: var(--color-background);
  border: 1px solid var(--color-border-light);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
}

.notion-card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--color-border);
}

.notion-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  font-weight: 500;
  font-size: 14px;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  text-decoration: none;
  gap: var(--spacing-xs);
}

.notion-button-primary {
  background-color: var(--color-accent);
  color: white;
}

.notion-button-primary:hover {
  background-color: #1d4ed8;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.notion-button-secondary {
  background-color: var(--color-background-secondary);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

.notion-button-secondary:hover {
  background-color: var(--color-background-tertiary);
  border-color: var(--color-border);
}

.notion-input {
  width: 100%;
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  font-size: 14px;
  transition: all 0.2s ease;
  background-color: var(--color-background);
}

.notion-input:focus {
  outline: none;
  border-color: var(--color-accent);
  box-shadow: 0 0 0 3px var(--color-accent-light);
}

.notion-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--color-background);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.notion-table th {
  background-color: var(--color-background-secondary);
  color: var(--color-text-primary);
  font-weight: 600;
  padding: var(--spacing-sm);
  text-align: left;
  border-bottom: 1px solid var(--color-border);
  font-size: 13px;
}

.notion-table td {
  padding: var(--spacing-sm);
  border-bottom: 1px solid var(--color-border-light);
  font-size: 14px;
}

.notion-table tr:hover {
  background-color: var(--color-background-secondary);
}

/* Status badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
}

.status-pending {
  background-color: #fef3c7;
  color: #92400e;
}

.status-verified {
  background-color: #d1fae5;
  color: #065f46;
}

.status-rejected {
  background-color: #fee2e2;
  color: #991b1b;
}

/* Typography utilities */
.text-heading-1 {
  font-size: 32px;
  font-weight: 700;
  line-height: 1.2;
  color: var(--color-text-primary);
}

.text-heading-2 {
  font-size: 24px;
  font-weight: 600;
  line-height: 1.3;
  color: var(--color-text-primary);
}

.text-heading-3 {
  font-size: 18px;
  font-weight: 600;
  line-height: 1.4;
  color: var(--color-text-primary);
}

.text-body {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.6;
  color: var(--color-text-primary);
}

.text-body-secondary {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.6;
  color: var(--color-text-secondary);
}

.text-caption {
  font-size: 12px;
  font-weight: 400;
  line-height: 1.5;
  color: var(--color-text-tertiary);
}

/* Layout utilities */
.container-narrow {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.sidebar-width {
  width: 280px;
}

/* Animation utilities */
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-8px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Mobile responsive */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none;
  }

  .mobile-only {
    display: block;
  }

  .container-narrow {
    padding: 0 var(--spacing-sm);
  }
}

@media (min-width: 769px) {
  .mobile-only {
    display: none;
  }
}
