import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

class AuthService {
  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Add request interceptor to include auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('accessToken');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Add response interceptor to handle token refresh
    this.api.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;
        
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;
          
          try {
            const refreshToken = localStorage.getItem('refreshToken');
            if (refreshToken) {
              const response = await this.refreshToken(refreshToken);
              localStorage.setItem('accessToken', response.token);
              originalRequest.headers.Authorization = `Bearer ${response.token}`;
              return this.api(originalRequest);
            }
          } catch (refreshError) {
            this.logout();
            window.location.href = '/login';
          }
        }
        
        return Promise.reject(error);
      }
    );
  }

  async login(credentials) {
    try {
      // For demo purposes, create a mock login
      if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {
        const mockResponse = {
          user: {
            id: 1,
            staff_id: 'ADMIN001',
            name: 'Admin User',
            email: '<EMAIL>',
            role: 'admin',
            department: 'IT',
            position: 'System Administrator',
          },
          token: 'mock-jwt-token-' + Date.now(),
          refreshToken: 'mock-refresh-token-' + Date.now(),
        };
        return mockResponse;
      } else if (credentials.email === '<EMAIL>' && credentials.password === 'user123') {
        const mockResponse = {
          user: {
            id: 2,
            staff_id: 'USER001',
            name: 'Regular User',
            email: '<EMAIL>',
            role: 'user',
            department: 'Finance',
            position: 'Finance Executive',
          },
          token: 'mock-jwt-token-' + Date.now(),
          refreshToken: 'mock-refresh-token-' + Date.now(),
        };
        return mockResponse;
      } else {
        throw new Error('Invalid credentials');
      }
    } catch (error) {
      throw new Error(error.message || 'Login failed');
    }
  }

  async register(userData) {
    try {
      const response = await this.api.post('/auth/register', userData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Registration failed');
    }
  }

  async refreshToken(refreshToken) {
    try {
      const response = await this.api.post('/auth/refresh', { refreshToken });
      return response.data;
    } catch (error) {
      throw new Error('Token refresh failed');
    }
  }

  async getUserProfile() {
    try {
      // For demo purposes, return mock user data
      const token = localStorage.getItem('accessToken');
      if (token && token.includes('admin')) {
        return {
          id: 1,
          staff_id: 'ADMIN001',
          name: 'Admin User',
          email: '<EMAIL>',
          role: 'admin',
          department: 'IT',
          position: 'System Administrator',
        };
      } else {
        return {
          id: 2,
          staff_id: 'USER001',
          name: 'Regular User',
          email: '<EMAIL>',
          role: 'user',
          department: 'Finance',
          position: 'Finance Executive',
        };
      }
    } catch (error) {
      throw new Error('Failed to get user profile');
    }
  }

  logout() {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
  }

  isValidToken(token) {
    if (!token) return false;
    
    try {
      // For demo purposes, just check if token exists and is not expired
      return token.startsWith('mock-jwt-token');
    } catch (error) {
      return false;
    }
  }

  getToken() {
    return localStorage.getItem('accessToken');
  }
}

export const authService = new AuthService();
