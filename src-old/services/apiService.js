import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

class ApiService {
  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Add request interceptor to include auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('accessToken');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );
  }

  // Mock data generators
  generateMockContributions() {
    return [
      {
        id: 1,
        user_id: 'USER001',
        user_name: '<PERSON>',
        user_reference: 'REF001',
        user_amount: 150.00,
        user_category: 'Food & Beverage',
        status: 'pending',
        role: 'Executive',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: 2,
        user_id: 'USER002',
        user_name: '<PERSON>',
        user_reference: 'REF002',
        user_amount: 75.50,
        user_category: 'Transportation',
        status: 'verified',
        role: 'Manager',
        created_at: new Date(Date.now() - 86400000).toISOString(),
        updated_at: new Date(Date.now() - 86400000).toISOString(),
      },
    ];
  }

  generateMockExpenses() {
    return [
      {
        id: 1,
        title: 'Office Supplies',
        description: 'Stationery and office materials',
        amount: 120.00,
        user_id: 'USER001',
        user_name: 'John Doe',
        remarks: 'Monthly office supplies',
        file_name: 'receipt_001.pdf',
        years: '2024',
        created_at: new Date().toISOString(),
      },
    ];
  }

  generateMockUsers() {
    return [
      {
        id: 1,
        staff_id: 'USER001',
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'user',
        department: 'Finance',
        monthly_contribution: 25,
        others_contribution: 0,
        status: 'active',
      },
      {
        id: 2,
        staff_id: 'USER002',
        name: 'Jane Smith',
        email: '<EMAIL>',
        role: 'manager',
        department: 'HR',
        monthly_contribution: 50,
        others_contribution: 10,
        status: 'active',
      },
    ];
  }

  // Contribution endpoints
  async getContributions(params = {}) {
    try {
      // Return mock data for demo
      const contributions = this.generateMockContributions();
      return { data: contributions, status: 'success' };
    } catch (error) {
      throw new Error('Failed to fetch contributions');
    }
  }

  async createContribution(contributionData) {
    try {
      // Simulate API call
      const newContribution = {
        id: Date.now(),
        ...contributionData,
        status: 'pending',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      return { data: newContribution, status: 'success' };
    } catch (error) {
      throw new Error('Failed to create contribution');
    }
  }

  async updateContribution(id, contributionData) {
    try {
      const updatedContribution = {
        id,
        ...contributionData,
        updated_at: new Date().toISOString(),
      };
      return { data: updatedContribution, status: 'success' };
    } catch (error) {
      throw new Error('Failed to update contribution');
    }
  }

  async deleteContribution(id) {
    try {
      return { status: 'success', message: 'Contribution deleted successfully' };
    } catch (error) {
      throw new Error('Failed to delete contribution');
    }
  }

  // Transaction endpoints
  async getTransactions(params = {}) {
    try {
      const transactions = this.generateMockContributions();
      return { data: transactions, status: 'success' };
    } catch (error) {
      throw new Error('Failed to fetch transactions');
    }
  }

  async updateTransactionStatus(id, status) {
    try {
      return { status: 'success', message: `Transaction ${status} successfully` };
    } catch (error) {
      throw new Error('Failed to update transaction status');
    }
  }

  // Expense endpoints
  async getExpenses(params = {}) {
    try {
      const expenses = this.generateMockExpenses();
      return { data: expenses, status: 'success' };
    } catch (error) {
      throw new Error('Failed to fetch expenses');
    }
  }

  async createExpense(expenseData) {
    try {
      const newExpense = {
        id: Date.now(),
        ...expenseData,
        created_at: new Date().toISOString(),
      };
      return { data: newExpense, status: 'success' };
    } catch (error) {
      throw new Error('Failed to create expense');
    }
  }

  async updateExpense(id, expenseData) {
    try {
      const updatedExpense = {
        id,
        ...expenseData,
        updated_at: new Date().toISOString(),
      };
      return { data: updatedExpense, status: 'success' };
    } catch (error) {
      throw new Error('Failed to update expense');
    }
  }

  async deleteExpense(id) {
    try {
      return { status: 'success', message: 'Expense deleted successfully' };
    } catch (error) {
      throw new Error('Failed to delete expense');
    }
  }

  // File upload endpoints
  async uploadFile(file, expenseId) {
    try {
      // Simulate file upload
      return {
        status: 'success',
        message: 'File uploaded successfully',
        data: { filename: file.name }
      };
    } catch (error) {
      throw new Error('Failed to upload file');
    }
  }

  async deleteFile(expenseId) {
    try {
      return { status: 'success', message: 'File deleted successfully' };
    } catch (error) {
      throw new Error('Failed to delete file');
    }
  }

  // User endpoints
  async getUsers(params = {}) {
    try {
      const users = this.generateMockUsers();
      return { data: users, status: 'success' };
    } catch (error) {
      throw new Error('Failed to fetch users');
    }
  }

  async createUser(userData) {
    try {
      const newUser = {
        id: Date.now(),
        ...userData,
        status: 'active',
      };
      return { data: newUser, status: 'success' };
    } catch (error) {
      throw new Error('Failed to create user');
    }
  }

  async updateUser(id, userData) {
    try {
      const updatedUser = {
        id,
        ...userData,
      };
      return { data: updatedUser, status: 'success' };
    } catch (error) {
      throw new Error('Failed to update user');
    }
  }

  async deleteUser(id) {
    try {
      return { status: 'success', message: 'User deleted successfully' };
    } catch (error) {
      throw new Error('Failed to delete user');
    }
  }

  // Collection endpoints
  async getCollections(params = {}) {
    try {
      const collections = this.generateMockUsers().map(user => ({
        ...user,
        total_paid: user.monthly_contribution * 12,
        pending_amount: 0,
        last_contribution: new Date().toISOString(),
      }));
      return { data: collections, status: 'success' };
    } catch (error) {
      throw new Error('Failed to fetch collections');
    }
  }

  async createCollection(collectionData) {
    try {
      return { status: 'success', message: 'Collection created successfully' };
    } catch (error) {
      throw new Error('Failed to create collection');
    }
  }

  // Dashboard endpoints
  async getDashboardSummary(userId, year) {
    try {
      const summary = {
        total_collection: 2500.00,
        total_expenses: 1800.00,
        pending_amount: 350.00,
        verified_amount: 2150.00,
      };
      return { data: [summary], status: 'success' };
    } catch (error) {
      throw new Error('Failed to fetch dashboard summary');
    }
  }

  // Year configuration endpoints
  async getYearConfig(year) {
    try {
      const config = {
        year,
        monthly_contribution_default: 25,
        categories: [
          'Food & Beverage',
          'Transportation',
          'Office Supplies',
          'Equipment',
          'Training',
          'Marketing',
          'Utilities',
          'Maintenance',
          'Others',
        ],
      };
      return { data: config, status: 'success' };
    } catch (error) {
      throw new Error('Failed to fetch year config');
    }
  }

  async updateYearConfig(year, config) {
    try {
      return { status: 'success', message: 'Year configuration updated successfully' };
    } catch (error) {
      throw new Error('Failed to update year config');
    }
  }
}

export const apiService = new ApiService();
