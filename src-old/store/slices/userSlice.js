import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  users: [],
  collections: [],
  yearConfig: {},
  isLoading: false,
  error: null,
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setLoading: (state, action) => {
      state.isLoading = action.payload;
    },
    setUsers: (state, action) => {
      state.users = action.payload;
    },
    setCollections: (state, action) => {
      state.collections = action.payload;
    },
    setYearConfig: (state, action) => {
      state.yearConfig = action.payload;
    },
    addUser: (state, action) => {
      state.users.unshift(action.payload);
    },
    updateUser: (state, action) => {
      const index = state.users.findIndex(u => u.id === action.payload.id);
      if (index !== -1) {
        state.users[index] = action.payload;
      }
    },
    deleteUser: (state, action) => {
      state.users = state.users.filter(u => u.id !== action.payload);
    },
    updateCollection: (state, action) => {
      const index = state.collections.findIndex(c => c.id === action.payload.id);
      if (index !== -1) {
        state.collections[index] = action.payload;
      }
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
});

export const {
  setLoading,
  setUsers,
  setCollections,
  setYearConfig,
  addUser,
  updateUser,
  deleteUser,
  updateCollection,
  setError,
  clearError,
} = userSlice.actions;

export default userSlice.reducer;
