import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  isLoading: false,
  loadingMessage: '',
};

const loadingSlice = createSlice({
  name: 'loading',
  initialState,
  reducers: {
    setLoading: (state, action) => {
      state.isLoading = action.payload;
      if (!action.payload) {
        state.loadingMessage = '';
      }
    },
    setLoadingMessage: (state, action) => {
      state.loadingMessage = action.payload;
    },
  },
});

export const { setLoading, setLoadingMessage } = loadingSlice.actions;
export default loadingSlice.reducer;
