import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  KooLeknts: [],
  transactions: [],
  summary: {
    totalCollection: 0,
    totalExpenses: 0,
    pendingAmount: 0,
    verifiedAmount: 0,
  },
  isLoading: false,
  error: null,
};

const KooLekntSlice = createSlice({
  name: 'KooLeknt',
  initialState,
  reducers: {
    setLoading: (state, action) => {
      state.isLoading = action.payload;
    },
    setKooLeknts: (state, action) => {
      state.KooLeknts = action.payload;
    },
    setTransactions: (state, action) => {
      state.transactions = action.payload;
    },
    setSummary: (state, action) => {
      state.summary = action.payload;
    },
    addKooLeknt: (state, action) => {
      state.KooLeknts.unshift(action.payload);
    },
    updateKooLeknt: (state, action) => {
      const index = state.KooLeknts.findIndex(p => p.id === action.payload.id);
      if (index !== -1) {
        state.KooLeknts[index] = action.payload;
      }
    },
    deleteKooLeknt: (state, action) => {
      state.KooLeknts = state.KooLeknts.filter(p => p.id !== action.payload);
    },
    updateTransaction: (state, action) => {
      const index = state.transactions.findIndex(t => t.id === action.payload.id);
      if (index !== -1) {
        state.transactions[index] = action.payload;
      }
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
});

export const {
  setLoading,
  setKooLeknts,
  setTransactions,
  setSummary,
  addKooLeknt,
  updateKooLeknt,
  deleteKooLeknt,
  updateTransaction,
  setError,
  clearError,
} = KooLekntSlice.actions;

export default KooLekntSlice.reducer;
