import { useState } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  Divider,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Menu,
  MenuItem,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard as DashboardIcon,
  KooLeknt as KooLekntIcon,
  Receipt as ReceiptIcon,
  People as PeopleIcon,
  Assessment as AssessmentIcon,
  Settings as SettingsIcon,
  Logout as LogoutIcon,
  AccountCircle as AccountCircleIcon,
  VerifiedUser as VerifiedUserIcon,
  CollectionsBookmark as CollectionsIcon,
  AdminPanelSettings as AdminIcon,
} from '@mui/icons-material';

import { useAuth } from '../contexts/AuthContext';
import { isAdmin, getInitials } from '../utils/helpers';

const drawerWidth = 280;

const menuItems = [
  {
    text: 'Dashboard',
    icon: <DashboardIcon />,
    path: '/',
    roles: ['admin', 'user', 'manager'],
  },
  {
    text: 'KooLeknts',
    icon: <KooLekntIcon />,
    path: '/KooLeknts',
    roles: ['admin', 'user', 'manager'],
  },
  {
    text: 'Expenses',
    icon: <ReceiptIcon />,
    path: '/expenses',
    roles: ['admin', 'user', 'manager'],
  },
  {
    text: 'Pending Verification',
    icon: <VerifiedUserIcon />,
    path: '/pending-verification',
    roles: ['admin', 'manager'],
  },
  {
    text: 'Collections',
    icon: <CollectionsIcon />,
    path: '/collections',
    roles: ['admin', 'manager'],
  },
  {
    text: 'Users',
    icon: <PeopleIcon />,
    path: '/users',
    roles: ['admin'],
  },
  {
    text: 'Reports',
    icon: <AssessmentIcon />,
    path: '/reports',
    roles: ['admin', 'manager'],
  },
  {
    text: 'Settings',
    icon: <SettingsIcon />,
    path: '/settings',
    roles: ['admin'],
  },
];

export default function Layout({ children }) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const router = useRouter();
  const { user, logout } = useAuth();

  const [mobileOpen, setMobileOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    handleMenuClose();
    logout();
  };

  const handleNavigation = (path) => {
    router.push(path);
    if (isMobile) {
      setMobileOpen(false);
    }
  };

  const filteredMenuItems = menuItems.filter(item =>
    item.roles.includes(user?.role)
  );

  const drawer = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', bgcolor: '#ffffff' }}>
      {/* Header */}
      <Box sx={{ p: 3, borderBottom: '1px solid #f3f4f6' }}>
        <Typography
          variant="h5"
          component="div"
          sx={{
            fontWeight: 700,
            color: '#37352f',
            fontSize: '20px',
            mb: 0.5
          }}
        >
          KooLek
        </Typography>
        <Typography
          variant="caption"
          sx={{
            color: '#6b7280',
            fontSize: '12px',
            fontWeight: 400
          }}
        >
          Financial Management
        </Typography>
      </Box>

      {/* User Profile */}
      <Box sx={{ p: 3, borderBottom: '1px solid #f3f4f6' }}>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            p: 2,
            bgcolor: '#f9fafb',
            borderRadius: '8px',
            border: '1px solid #f3f4f6',
            transition: 'all 0.2s ease',
            '&:hover': {
              bgcolor: '#f3f4f6',
              borderColor: '#e5e7eb',
            }
          }}
        >
          <Avatar
            sx={{
              bgcolor: '#2563eb',
              mr: 2,
              width: 36,
              height: 36,
              fontSize: '14px',
              fontWeight: 600,
            }}
          >
            {getInitials(user?.name)}
          </Avatar>
          <Box sx={{ flex: 1, minWidth: 0 }}>
            <Typography
              variant="body2"
              sx={{
                fontWeight: 600,
                color: '#37352f',
                fontSize: '14px',
                lineHeight: 1.4,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}
            >
              {user?.name}
            </Typography>
            <Typography
              variant="caption"
              sx={{
                color: '#6b7280',
                fontSize: '12px',
                lineHeight: 1.3,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}
            >
              {user?.position}
            </Typography>
          </Box>
        </Box>
      </Box>

      {/* Navigation */}
      <Box sx={{ flex: 1, p: 2 }}>
        <List sx={{ p: 0 }}>
          {filteredMenuItems.map((item) => (
            <ListItem key={item.text} disablePadding sx={{ mb: 0.5 }}>
              <ListItemButton
                onClick={() => handleNavigation(item.path)}
                selected={router.pathname === item.path}
                sx={{
                  borderRadius: '6px',
                  py: 1.5,
                  px: 2,
                  minHeight: 'auto',
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    bgcolor: '#f9fafb',
                  },
                  '&.Mui-selected': {
                    bgcolor: '#eff6ff',
                    color: '#2563eb',
                    '&:hover': {
                      bgcolor: '#dbeafe',
                    },
                    '& .MuiListItemIcon-root': {
                      color: '#2563eb',
                    },
                  },
                }}
              >
                <ListItemIcon
                  sx={{
                    minWidth: 36,
                    color: router.pathname === item.path ? '#2563eb' : '#6b7280',
                    '& .MuiSvgIcon-root': {
                      fontSize: '20px',
                    },
                  }}
                >
                  {item.icon}
                </ListItemIcon>
                <ListItemText
                  primary={item.text}
                  primaryTypographyProps={{
                    fontSize: '14px',
                    fontWeight: router.pathname === item.path ? 600 : 500,
                    color: router.pathname === item.path ? '#2563eb' : '#37352f',
                  }}
                />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Box>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      <AppBar
        position="fixed"
        elevation={0}
        sx={{
          width: { md: `calc(100% - ${drawerWidth}px)` },
          ml: { md: `${drawerWidth}px` },
          bgcolor: '#ffffff',
          color: '#37352f',
          borderBottom: '1px solid #f3f4f6',
          boxShadow: 'none',
        }}
      >
        <Toolbar sx={{ minHeight: '64px !important', px: 3 }}>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{
              mr: 2,
              display: { md: 'none' },
              color: '#6b7280',
              '&:hover': {
                bgcolor: '#f9fafb',
              }
            }}
          >
            <MenuIcon />
          </IconButton>

          <Box sx={{ flexGrow: 1 }}>
            <Typography
              variant="h6"
              noWrap
              component="div"
              sx={{
                fontWeight: 600,
                fontSize: '18px',
                color: '#37352f',
                lineHeight: 1.4,
              }}
            >
              {menuItems.find(item => item.path === router.pathname)?.text || 'KooLek'}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {isAdmin(user) && (
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  bgcolor: '#eff6ff',
                  color: '#2563eb',
                  px: 2,
                  py: 0.5,
                  borderRadius: '6px',
                  fontSize: '11px',
                  fontWeight: 600,
                  border: '1px solid #dbeafe',
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px',
                }}
              >
                <AdminIcon sx={{ fontSize: 14, mr: 0.5 }} />
                Admin
              </Box>
            )}

            <IconButton
              size="medium"
              aria-label="account of current user"
              aria-controls="menu-appbar"
              aria-haspopup="true"
              onClick={handleMenuOpen}
              sx={{
                color: '#6b7280',
                '&:hover': {
                  bgcolor: '#f9fafb',
                }
              }}
            >
              <Avatar
                sx={{
                  bgcolor: '#2563eb',
                  width: 32,
                  height: 32,
                  fontSize: '14px',
                  fontWeight: 600,
                }}
              >
                {getInitials(user?.name)}
              </Avatar>
            </IconButton>

            <Menu
              id="menu-appbar"
              anchorEl={anchorEl}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'right',
              }}
              keepMounted
              transformOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
              sx={{
                '& .MuiPaper-root': {
                  borderRadius: '8px',
                  border: '1px solid #f3f4f6',
                  boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
                  mt: 1,
                  minWidth: 180,
                },
                '& .MuiMenuItem-root': {
                  fontSize: '14px',
                  py: 1.5,
                  px: 2,
                  '&:hover': {
                    bgcolor: '#f9fafb',
                  },
                },
              }}
            >
              <MenuItem onClick={handleMenuClose}>
                <ListItemIcon>
                  <AccountCircleIcon fontSize="small" sx={{ color: '#6b7280' }} />
                </ListItemIcon>
                Profile
              </MenuItem>
              <MenuItem onClick={handleLogout}>
                <ListItemIcon>
                  <LogoutIcon fontSize="small" sx={{ color: '#ef4444' }} />
                </ListItemIcon>
                <Typography sx={{ color: '#ef4444' }}>Logout</Typography>
              </MenuItem>
            </Menu>
          </Box>
        </Toolbar>
      </AppBar>

      <Box
        component="nav"
        sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true,
          }}
          sx={{
            display: { xs: 'block', md: 'none' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              borderRight: '1px solid #f3f4f6',
              boxShadow: 'none',
            },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', md: 'block' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              borderRight: '1px solid #f3f4f6',
              boxShadow: 'none',
            },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: { md: `calc(100% - ${drawerWidth}px)` },
          minHeight: '100vh',
          bgcolor: '#ffffff',
        }}
      >
        <Toolbar />
        {children}
      </Box>
    </Box>
  );
}
