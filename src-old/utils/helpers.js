import moment from 'moment';
import { ROLE_HIERARCHY, STATUS_COLORS } from './constants';

// Role text mapping
export const getRoleText = (role) => {
  if (!role) return 'Unknown';
  
  const lowerRole = role.toLowerCase();
  
  if (lowerRole.includes('penolong') || lowerRole.includes('kerani') || lowerRole.includes('secretary')) {
    return 'Non-Executive';
  }
  if (lowerRole.includes('exec') || lowerRole.includes('asst')) {
    return 'Assistant Manager';
  }
  if (lowerRole.includes('mgr') || lowerRole.includes('manager')) {
    return 'Manager';
  }
  if (lowerRole.includes('agm')) {
    return 'Assistant General Manager';
  }
  if (lowerRole.includes('head')) {
    return 'General Manager';
  }
  if (lowerRole.includes('vp')) {
    return 'Vice President';
  }
  if (lowerRole.includes('evp')) {
    return 'Executive Vice President';
  }
  
  return ROLE_HIERARCHY[lowerRole] || 'Unknown';
};

// Status styling
export const getStatusStyle = (status) => {
  return STATUS_COLORS[status?.toLowerCase()] || 'bg-gray-100 text-gray-800';
};

// Format currency
export const formatCurrency = (amount, currency = 'RM') => {
  if (amount === null || amount === undefined) return `${currency} 0.00`;
  
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  return `${currency} ${numAmount.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })}`;
};

// Format date
export const formatDate = (date, format = 'DD/MM/YYYY') => {
  if (!date) return '';
  return moment(date).format(format);
};

// Format date with time
export const formatDateTime = (date, format = 'DD/MM/YYYY, hh:mm A') => {
  if (!date) return '';
  return moment(date).format(format);
};

// Get relative time
export const getRelativeTime = (date) => {
  if (!date) return '';
  return moment(date).fromNow();
};

// Capitalize first letter
export const toUpperCaseFirstLetter = (str) => {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1);
};

// Sort array of objects by key alphabetically
export const sortArrayOfObjectsByCertainKeyAlphabetically = (array, key) => {
  return [...array].sort((a, b) => {
    const aValue = a[key]?.toString().toLowerCase() || '';
    const bValue = b[key]?.toString().toLowerCase() || '';
    return aValue.localeCompare(bValue);
  });
};

// Filter array by search term
export const filterArrayBySearchTerm = (array, searchTerm, searchKeys) => {
  if (!searchTerm) return array;
  
  const term = searchTerm.toLowerCase();
  return array.filter(item => 
    searchKeys.some(key => 
      item[key]?.toString().toLowerCase().includes(term)
    )
  );
};

// Validate email
export const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Validate file type
export const isValidFileType = (file, allowedTypes) => {
  return allowedTypes.includes(file.type);
};

// Validate file size
export const isValidFileSize = (file, maxSize) => {
  return file.size <= maxSize;
};

// Generate unique ID
export const generateUniqueId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// Debounce function
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// Deep clone object
export const deepClone = (obj) => {
  return JSON.parse(JSON.stringify(obj));
};

// Check if user is admin
export const isAdmin = (user) => {
  return user?.role === 'admin';
};

// Check if user is manager
export const isManager = (user) => {
  return user?.role === 'manager' || user?.role === 'admin';
};

// Get current year
export const getCurrentYear = () => {
  return moment().format('YYYY');
};

// Get year options for dropdown
export const getYearOptions = (startYear = 2020, endYear = null) => {
  const currentYear = parseInt(getCurrentYear());
  const end = endYear || currentYear + 2;
  const years = [];
  
  for (let year = end; year >= startYear; year--) {
    years.push({
      value: year.toString(),
      label: year.toString(),
    });
  }
  
  return years;
};

// Calculate percentage
export const calculatePercentage = (value, total) => {
  if (!total || total === 0) return 0;
  return ((value / total) * 100).toFixed(2);
};

// Truncate text
export const truncateText = (text, maxLength = 50) => {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

// Get initials from name
export const getInitials = (name) => {
  if (!name) return '';
  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .join('')
    .substring(0, 2);
};

// Format file size
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Check if object is empty
export const isEmpty = (obj) => {
  return Object.keys(obj).length === 0;
};

// Remove empty values from object
export const removeEmptyValues = (obj) => {
  return Object.fromEntries(
    Object.entries(obj).filter(([_, value]) => 
      value !== null && value !== undefined && value !== ''
    )
  );
};
