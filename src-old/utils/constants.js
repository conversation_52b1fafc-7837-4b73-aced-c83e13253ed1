// Contribution categories
export const CONTRIBUTION_CATEGORIES = [
  'Food & Beverage',
  'Transportation',
  'Office Supplies',
  'Equipment',
  'Training',
  'Marketing',
  'Utilities',
  'Maintenance',
  'Others',
];

// Contribution status
export const CONTRIBUTION_STATUS = {
  PENDING: 'pending',
  VERIFIED: 'verified',
  REJECTED: 'rejected',
};

// User roles
export const USER_ROLES = {
  ADMIN: 'admin',
  USER: 'user',
  MANAGER: 'manager',
};

// Role hierarchy for display
export const ROLE_HIERARCHY = {
  'non-executive': 'Non-Executive',
  'assistant manager': 'Assistant Manager',
  'manager': 'Manager',
  'assistant general manager': 'Assistant General Manager',
  'general manager': 'General Manager',
  'vice president': 'Vice President',
  'executive vice president': 'Executive Vice President',
};

// Status colors
export const STATUS_COLORS = {
  pending: 'bg-yellow-100 text-yellow-800',
  verified: 'bg-green-100 text-green-800',
  rejected: 'bg-red-100 text-red-800',
};

// Monthly contribution options
export const CONTRIBUTION_OPTIONS = [5, 10, 15, 20, 25, 30, 50, 100];

// Date formats
export const DATE_FORMATS = {
  DISPLAY: 'DD/MM/YYYY',
  API: 'YYYY-MM-DD',
  YEAR: 'YYYY',
};

// API endpoints
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    REFRESH: '/auth/refresh',
    PROFILE: '/auth/profile',
  },
  CONTRIBUTIONS: '/contributions',
  TRANSACTIONS: '/transactions',
  EXPENSES: '/expenses',
  USERS: '/users',
  COLLECTIONS: '/collections',
  DASHBOARD: '/dashboard',
  CONFIG: '/config',
};

// Table pagination
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZE_OPTIONS: [5, 10, 25, 50],
};

// File upload
export const FILE_UPLOAD = {
  MAX_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
  ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.gif', '.pdf'],
};

// Validation messages
export const VALIDATION_MESSAGES = {
  REQUIRED: 'This field is required',
  EMAIL: 'Please enter a valid email address',
  MIN_LENGTH: (min) => `Minimum ${min} characters required`,
  MAX_LENGTH: (max) => `Maximum ${max} characters allowed`,
  POSITIVE_NUMBER: 'Please enter a positive number',
  FILE_SIZE: `File size must be less than ${FILE_UPLOAD.MAX_SIZE / 1024 / 1024}MB`,
  FILE_TYPE: 'Invalid file type',
};

// Success messages
export const SUCCESS_MESSAGES = {
  KooLekNT_CREATED: 'KooLeknt created successfully',
  KooLekNT_UPDATED: 'KooLeknt updated successfully',
  KooLekNT_DELETED: 'KooLeknt deleted successfully',
  EXPENSE_CREATED: 'Expense created successfully',
  EXPENSE_UPDATED: 'Expense updated successfully',
  EXPENSE_DELETED: 'Expense deleted successfully',
  USER_CREATED: 'User created successfully',
  USER_UPDATED: 'User updated successfully',
  USER_DELETED: 'User deleted successfully',
  TRANSACTION_VERIFIED: 'Transaction verified successfully',
  TRANSACTION_REJECTED: 'Transaction rejected successfully',
  FILE_UPLOADED: 'File uploaded successfully',
  SETTINGS_SAVED: 'Settings saved successfully',
};

// Error messages
export const ERROR_MESSAGES = {
  GENERIC: 'Something went wrong. Please try again.',
  NETWORK: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  NOT_FOUND: 'The requested resource was not found.',
  VALIDATION: 'Please check your input and try again.',
  FILE_UPLOAD: 'File upload failed. Please try again.',
};

// Local storage keys
export const STORAGE_KEYS = {
  ACCESS_TOKEN: 'accessToken',
  REFRESH_TOKEN: 'refreshToken',
  USER_PREFERENCES: 'userPreferences',
  THEME: 'theme',
};
