import { PrismaClient } from '@prisma/client'
import { hash } from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create demo users
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Admin User',
      password: await hash('admin123', 12),
      emailVerified: new Date(),
      bio: 'System administrator with full access to all organization features.',
      timezone: 'UTC',
      locale: 'en',
      preferences: {
        theme: 'light',
        notifications: {
          email: true,
          browser: true,
        },
        dashboard: {
          defaultView: 'overview',
        },
      },
    },
  })

  const memberUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Member User',
      password: await hash('member123', 12),
      emailVerified: new Date(),
      bio: 'Regular team member focused on contributions and expense management.',
      timezone: 'UTC',
      locale: 'en',
      preferences: {
        theme: 'light',
        notifications: {
          email: true,
          browser: false,
        },
        dashboard: {
          defaultView: 'contributions',
        },
      },
    },
  })

  // Create demo organization
  const organization = await prisma.organization.upsert({
    where: { slug: 'demo-org' },
    update: {},
    create: {
      name: 'Demo Organization',
      slug: 'demo-org',
      domain: 'demo.com',
      settings: {
        monthlyContributionDefault: 25,
        currency: 'USD',
        timezone: 'UTC',
      },
      metadata: {
        industry: 'Technology',
        size: 'Small',
      },
    },
  })

  // Add users to organization
  await prisma.userOrganization.upsert({
    where: {
      userId_organizationId: {
        userId: adminUser.id,
        organizationId: organization.id,
      },
    },
    update: {},
    create: {
      userId: adminUser.id,
      organizationId: organization.id,
      role: 'OWNER',
    },
  })

  await prisma.userOrganization.upsert({
    where: {
      userId_organizationId: {
        userId: memberUser.id,
        organizationId: organization.id,
      },
    },
    update: {},
    create: {
      userId: memberUser.id,
      organizationId: organization.id,
      role: 'MEMBER',
    },
  })

  // Create default categories
  const KooLekntCategories = [
    'Monthly Contribution',
    'Special Assessment',
    'Event Fee',
    'Membership Fee',
    'Other',
  ]

  const expenseCategories = [
    'Food & Beverage',
    'Transportation',
    'Office Supplies',
    'Equipment',
    'Training',
    'Marketing',
    'Utilities',
    'Maintenance',
    'Others',
  ]

  for (const categoryName of KooLekntCategories) {
    await prisma.category.upsert({
      where: {
        organizationId_name_type: {
          organizationId: organization.id,
          name: categoryName,
          type: 'CONTRIBUTION',
        },
      },
      update: {},
      create: {
        organizationId: organization.id,
        name: categoryName,
        type: 'CONTRIBUTION',
        isDefault: true,
      },
    })
  }

  for (const categoryName of expenseCategories) {
    await prisma.category.upsert({
      where: {
        organizationId_name_type: {
          organizationId: organization.id,
          name: categoryName,
          type: 'EXPENSE',
        },
      },
      update: {},
      create: {
        organizationId: organization.id,
        name: categoryName,
        type: 'EXPENSE',
        isDefault: true,
      },
    })
  }

  // Create sample contributions
  const currentYear = new Date().getFullYear()

  await prisma.contribution.create({
    data: {
      organizationId: organization.id,
      userId: memberUser.id,
      reference: 'PAY-001',
      amount: 25.00,
      category: 'Monthly Contribution',
      description: 'January monthly contribution',
      status: 'VERIFIED',
      year: currentYear,
      verifiedAt: new Date(),
      verifiedBy: adminUser.id,
    },
  })

  await prisma.contribution.create({
    data: {
      organizationId: organization.id,
      userId: memberUser.id,
      reference: 'PAY-002',
      amount: 25.00,
      category: 'Monthly Contribution',
      description: 'February monthly contribution',
      status: 'PENDING',
      year: currentYear,
    },
  })

  // Create sample expenses
  await prisma.expense.create({
    data: {
      organizationId: organization.id,
      userId: adminUser.id,
      amount: 150.00,
      category: 'Food & Beverage',
      description: 'Team lunch meeting',
      status: 'APPROVED',
      year: currentYear,
      verifiedAt: new Date(),
      verifiedBy: adminUser.id,
    },
  })

  await prisma.expense.create({
    data: {
      organizationId: organization.id,
      userId: memberUser.id,
      amount: 75.50,
      category: 'Office Supplies',
      description: 'Stationery and printing',
      status: 'PENDING',
      year: currentYear,
    },
  })

  // Create sample collection
  await prisma.collection.create({
    data: {
      organizationId: organization.id,
      name: `${currentYear} Monthly Contributions`,
      amount: 300.00,
      description: 'Collection for monthly member contributions',
      year: currentYear,
      status: 'active',
    },
  })

  console.log('✅ Database seeded successfully!')
  console.log('📧 Demo accounts:')
  console.log('   Admin: <EMAIL> / admin123')
  console.log('   Member: <EMAIL> / member123')
  console.log('🏢 Demo organization: demo-org')
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error(e)
    await prisma.$disconnect()
    process.exit(1)
  })
