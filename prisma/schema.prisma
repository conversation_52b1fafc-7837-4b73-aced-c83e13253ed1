generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["multiSchema", "postgresqlExtensions"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Organization {
  id                  String               @id @default(cuid())
  name                String
  slug                String               @unique
  domain              String?              @unique
  settings            Json                 @default("{}")
  metadata            Json                 @default("{}")
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt
  contributions       Contribution[]
  auditLogs           AuditLog[]
  categories          Category[]
  collections         Collection[]
  expenses            Expense[]
  invitationTemplates InvitationTemplate[]
  invitations         Invitation[]
  userOnboarding      UserOnboarding[]
  members             UserOrganization[]
  webhooks            Webhook[]

  @@map("organizations")
}

model User {
  id                 String             @id @default(cuid())
  email              String             @unique
  emailVerified      DateTime?
  name               String?
  image              String?
  password           String?
  twoFactorEnabled   Boolean            @default(false)
  bio                String?
  timezone           String?            @default("UTC")
  locale             String?            @default("en")
  preferences        Json               @default("{}")
  createdAt          DateTime           @default(now())
  updatedAt          DateTime           @updatedAt
  lastLoginAt        DateTime?
  contributions      Contribution[]
  accounts           Account[]
  auditLogs          AuditLog[]
  expenses           Expense[]
  sentInvitations    Invitation[]       @relation("InvitationInviter")
  passwordResets     PasswordReset[]
  sessions           Session[]
  onboardingProgress UserOnboarding[]
  organizations      UserOrganization[]

  @@map("users")
}

model UserOrganization {
  id             String           @id @default(cuid())
  userId         String
  organizationId String
  role           OrganizationRole @default(MEMBER)
  joinedAt       DateTime         @default(now())
  organization   Organization     @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user           User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, organizationId])
  @@map("user_organizations")
}

model Invitation {
  id             String              @id @default(cuid())
  email          String
  organizationId String
  role           OrganizationRole    @default(MEMBER)
  token          String              @unique
  status         InvitationStatus    @default(PENDING)
  expiresAt      DateTime
  invitedBy      String?
  message        String?
  templateId     String?
  metadata       Json                @default("{}")
  batchId        String?
  batchIndex     Int?
  emailSentAt    DateTime?
  emailOpenedAt  DateTime?
  reminderSentAt DateTime?
  lastActivityAt DateTime?
  createdAt      DateTime            @default(now())
  updatedAt      DateTime            @updatedAt
  acceptedAt     DateTime?
  revokedAt      DateTime?
  inviter        User?               @relation("InvitationInviter", fields: [invitedBy], references: [id])
  organization   Organization        @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  template       InvitationTemplate? @relation(fields: [templateId], references: [id])

  @@unique([email, organizationId])
  @@index([batchId])
  @@index([status, expiresAt])
  @@map("invitations")
}

model InvitationTemplate {
  id             String       @id @default(cuid())
  organizationId String
  name           String
  subject        String
  content        String
  isDefault      Boolean      @default(false)
  metadata       Json         @default("{}")
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  invitations    Invitation[]

  @@unique([organizationId, name])
  @@map("invitation_templates")
}

model UserOnboarding {
  id             String       @id @default(cuid())
  userId         String
  organizationId String
  step           String
  completed      Boolean      @default(false)
  data           Json         @default("{}")
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  completedAt    DateTime?
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, organizationId, step])
  @@map("user_onboarding")
}

model Webhook {
  id             String            @id @default(cuid())
  organizationId String
  url            String
  events         String[]
  secret         String?
  isActive       Boolean           @default(true)
  lastTriggered  DateTime?
  failureCount   Int               @default(0)
  createdAt      DateTime          @default(now())
  updatedAt      DateTime          @updatedAt
  deliveries     WebhookDelivery[]
  organization   Organization      @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@map("webhooks")
}

model WebhookDelivery {
  id        String    @id @default(cuid())
  webhookId String
  event     String
  payload   Json
  response  Json?
  status    String
  attempts  Int       @default(1)
  nextRetry DateTime?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  webhook   Webhook   @relation(fields: [webhookId], references: [id], onDelete: Cascade)

  @@index([webhookId, createdAt])
  @@map("webhook_deliveries")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

model PasswordReset {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expires   DateTime
  used      Boolean  @default(false)
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("password_resets")
}

model AuditLog {
  id             String        @id @default(cuid())
  userId         String?
  organizationId String?
  action         String
  resource       String
  resourceId     String?
  details        Json          @default("{}")
  ipAddress      String?
  userAgent      String?
  createdAt      DateTime      @default(now())
  organization   Organization? @relation(fields: [organizationId], references: [id])
  user           User?         @relation(fields: [userId], references: [id])

  @@index([organizationId, createdAt])
  @@index([userId, createdAt])
  @@index([action, createdAt])
  @@map("audit_logs")
}

model Expense {
  id             String        @id @default(cuid())
  organizationId String
  userId         String
  amount         Decimal       @db.Decimal(10, 2)
  category       String
  description    String?
  status         ExpenseStatus @default(PENDING)
  year           Int
  attachments    Json          @default("[]")
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  verifiedAt     DateTime?
  verifiedBy     String?
  organization   Organization  @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user           User          @relation(fields: [userId], references: [id])

  @@index([organizationId, year])
  @@index([organizationId, status])
  @@map("expenses")
}

model Collection {
  id             String       @id @default(cuid())
  organizationId String
  name           String
  amount         Decimal      @db.Decimal(10, 2)
  description    String?
  year           Int
  status         String       @default("active")
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId, year])
  @@map("collections")
}

model Category {
  id             String       @id @default(cuid())
  organizationId String
  name           String
  type           CategoryType
  isDefault      Boolean      @default(false)
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([organizationId, name, type])
  @@map("categories")
}

model Contribution {
  id             String            @id @default(cuid())
  organizationId String
  userId         String
  reference      String
  amount         Decimal           @db.Decimal(10, 2)
  category       String
  description    String?
  status         ContributionStatus @default(PENDING)
  year           Int
  attachments    Json              @default("[]")
  createdAt      DateTime          @default(now())
  updatedAt      DateTime          @updatedAt
  verifiedAt     DateTime?
  verifiedBy     String?
  organization   Organization      @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user           User              @relation(fields: [userId], references: [id])

  @@index([organizationId, status])
  @@index([organizationId, year])
  @@map("contributions")
}

enum OrganizationRole {
  OWNER
  ADMIN
  MEMBER
  VIEWER
}

enum InvitationStatus {
  PENDING
  ACCEPTED
  EXPIRED
  REVOKED
}

enum ExpenseStatus {
  PENDING
  APPROVED
  REJECTED
}

enum CategoryType {
  CONTRIBUTION
  EXPENSE
}

enum ContributionStatus {
  PENDING
  VERIFIED
  REJECTED
}
