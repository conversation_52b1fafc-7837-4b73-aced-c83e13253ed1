# Phase 1: Core Multi-Tenancy Setup Guide

## 🎯 Overview

This guide walks you through setting up Phase 1 of the KooLek multi-tenant SaaS platform. After completing this phase, you'll have:

- ✅ Multi-tenant database schema with Prisma
- ✅ Organization management system
- ✅ tRPC type-safe APIs
- ✅ Organization context and selector component
- ✅ Data migration from single-tenant to multi-tenant

## 📋 Prerequisites

- Node.js 18+ installed
- PostgreSQL database (local or cloud)
- Git repository access

## 🚀 Installation Steps

### 1. Install Dependencies

```bash
# Install all new dependencies
npm install

# Install additional dependencies for bcryptjs (for password hashing)
npm install bcryptjs
npm install -D @types/bcryptjs
```

### 2. Database Setup

#### Option A: Local PostgreSQL
```bash
# Install PostgreSQL locally (macOS)
brew install postgresql
brew services start postgresql

# Create database
createdb koolek_db
```

#### Option B: Cloud Database (Recommended)
Use services like:
- [Supabase](https://supabase.com) (Free tier available)
- [PlanetScale](https://planetscale.com) (Free tier available)
- [Railway](https://railway.app) (Free tier available)

### 3. Environment Configuration

```bash
# Copy environment template
cp .env.example .env.local

# Edit .env.local with your database URL
DATABASE_URL="postgresql://username:password@localhost:5432/koolek_db"
NEXTAUTH_SECRET="your-super-secret-key-here"
NEXTAUTH_URL="http://localhost:3002"
```

### 4. Database Migration

```bash
# Generate Prisma client
npm run db:generate

# Push schema to database
npm run db:push

# Seed database with sample data
npm run db:seed
```

### 5. Run Migration Script (If you have existing data)

```bash
# Run the migration script to convert existing data
npx tsx scripts/migrate-to-multi-tenant.ts
```

## 🧪 Testing the Setup

### 1. Start Development Server

```bash
npm run dev
```

### 2. Access the Application

Open [http://localhost:3002](http://localhost:3002) in your browser.

### 3. Test Login

Use the seeded demo accounts:
- **Admin**: <EMAIL> / admin123
- **Member**: <EMAIL> / member123

### 4. Test Organization Features

1. **Organization Selector**: Look for the organization dropdown in the header
2. **Create Organization**: Click "Create new organization" in the selector
3. **Switch Organizations**: Select different organizations from the dropdown
4. **Organization Context**: Verify that data is scoped to the selected organization

## 🔧 Development Commands

```bash
# Development with Turbopack (Next.js 15)
npm run dev

# Type checking
npm run type-check

# Database commands
npm run db:generate    # Generate Prisma client
npm run db:push       # Push schema changes
npm run db:studio     # Open Prisma Studio
npm run db:migrate    # Create migration
npm run db:reset      # Reset database
npm run db:seed       # Seed with sample data

# Build and start
npm run build
npm start
```

## 📁 New File Structure

After Phase 1, your project structure includes:

```
koolek-standalone/
├── prisma/
│   ├── schema.prisma          # Multi-tenant database schema
│   └── seed.ts               # Database seeding script
├── lib/
│   ├── db.ts                 # Prisma client setup
│   ├── trpc.ts               # tRPC configuration
│   ├── auth.ts               # NextAuth configuration
│   ├── api.ts                # tRPC client setup
│   └── utils.ts              # Utility functions
├── server/
│   └── api/
│       ├── root.ts           # Main tRPC router
│       └── routers/
│           └── organization.ts # Organization API routes
├── contexts/
│   └── OrganizationContext.tsx # Organization state management
├── components/
│   ├── ui/                   # Reusable UI components
│   └── organization/
│       └── OrganizationSelector.tsx # Organization selector
├── scripts/
│   └── migrate-to-multi-tenant.ts # Migration script
└── .env.example              # Environment template
```

## 🔍 Verification Checklist

- [ ] Database schema created successfully
- [ ] Sample data seeded
- [ ] Application starts without errors
- [ ] Can login with demo accounts
- [ ] Organization selector appears in UI
- [ ] Can create new organizations
- [ ] Can switch between organizations
- [ ] Data is properly scoped to organizations

## 🐛 Troubleshooting

### Database Connection Issues
```bash
# Check if PostgreSQL is running
brew services list | grep postgresql

# Test database connection
psql $DATABASE_URL
```

### Prisma Issues
```bash
# Reset Prisma client
rm -rf node_modules/.prisma
npm run db:generate
```

### TypeScript Errors
```bash
# Check for type errors
npm run type-check

# Restart TypeScript server in VS Code
Cmd+Shift+P -> "TypeScript: Restart TS Server"
```

### Build Issues
```bash
# Clear Next.js cache
rm -rf .next
npm run build
```

## 🎯 Next Steps

After completing Phase 1, you're ready for:

- **Phase 2**: Authentication & Authorization (NextAuth.js v5, RBAC)
- **Phase 3**: Invitation System (Email invitations, user management)
- **Phase 4**: UI/UX Enhancement (App Router migration, modern components)
- **Phase 5**: Advanced Features (Branding, analytics, compliance)

## 📞 Support

If you encounter issues:

1. Check the troubleshooting section above
2. Review the error logs in the terminal
3. Ensure all environment variables are set correctly
4. Verify database connectivity
5. Check that all dependencies are installed

## 🎉 Success!

If you can see the organization selector and create/switch organizations, Phase 1 is complete! You now have a solid foundation for the multi-tenant KooLek SaaS platform.
