#!/bin/bash

echo "🚀 Setting up KooLek Standalone Application..."
echo "=============================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js version: $(node -v)"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ npm version: $(npm -v)"

# Install dependencies
echo ""
echo "📦 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo "✅ Dependencies installed successfully"

# Create uploads directory for file attachments
echo ""
echo "📁 Creating uploads directory..."
mkdir -p public/uploads
echo "✅ Uploads directory created"

# Set up environment variables
echo ""
echo "🔧 Setting up environment variables..."
if [ ! -f .env.local ]; then
    echo "❌ .env.local file not found"
    exit 1
fi
echo "✅ Environment variables configured"

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Start the development server: npm run dev"
echo "2. Open http://localhost:3002 in your browser"
echo "3. Login with demo credentials:"
echo "   - Admin: <EMAIL> / admin123"
echo "   - User: <EMAIL> / user123"
echo ""
echo "🔗 Available scripts:"
echo "   npm run dev     - Start development server"
echo "   npm run build   - Build for production"
echo "   npm run start   - Start production server"
echo "   npm run lint    - Run ESLint"
echo ""
echo "Happy coding! 🚀"
